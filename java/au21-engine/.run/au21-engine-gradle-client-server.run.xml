<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="au21-engine-gradle-client-server" type="QuarkusGradleBridge">
    <option name="arguments" value="--configuration-cache --continuous --info -Dquarkus.container-image.push=false -Dquarkus.container-image.build=false" />
    <option name="envVariables">
      <map>
        <entry key="OBJECTDB_URL" value="objectdb://localhost:6136/dev.odb" />
        <entry key="password" value="admin" />
        <entry key="user" value="admin" />
      </map>
    </option>
    <option name="vmOptions" />
    <option name="workingDir" value="$PROJECT_DIR$" />
    <method v="2" />
  </configuration>
</component>