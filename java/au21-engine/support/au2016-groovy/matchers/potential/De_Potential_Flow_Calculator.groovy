package app.services.de.matchers.potential

import app.model.Auction
import app.model.CounterPartyVolume
import app.model.DeAuction
import app.model.DeRound
import app.model.DeRoundTraderInfo
import app.model.DeTrader
import app.services.de.De_Getters
import app.services.de.matchers.potential.PotentialMatch_Model.PotentialMatch_Input
import groovy.transform.CompileStatic
import groovy.util.logging.Slf4j

import static app.services.de.matchers.potential.PotentialMatch_Model.*

@Slf4j
@CompileStatic
class De_Potential_Flow_Calculator {

  static final int DOMAIN_MAX = 1000

  // TODO: this would be the bridge into and out of the Potential Flow Solver
  // so that it can be tested independtley of DE

  static PotentialMatch_Input create_potential_match_input ( DeAuction a ) {

    boolean desc = ( a.clock.priceDirection == Auction.PriceDirection.DOWN )

    DeRound n = De_Getters.lastround ( a )

    List<TradingLimits> traders = a.traders.collect { DeTrader t ->

      DeRoundTraderInfo rbi = De_Getters.get_rbi ( t, n )

      new TradingLimits ( t.person.username, rbi.min_vol, rbi.max_vol )
    }

    def find_trader = { String username -> traders.find { it.username == username } }

    List<Seller_Buyer_Capacity> seller_buyer_flows = n.round_counterparty_limits.collect { CounterPartyVolume ccv ->
      new Seller_Buyer_Capacity (
        find_trader ( ccv.seller.username ),
        find_trader ( ccv.buyer.username ),
        ccv.sell_volume_limit )
    }

    return new PotentialMatch_Input (
      traders,
      seller_buyer_flows,
      DOMAIN_MAX )

  }

/*
// THIS WAS THE OLD WAY,

private static Trader find_trader ( Potential_Match_Sample g, DeTrader t) {
  g.traders.find { it.username == t?.person?.username }
}

static Potential_Match_Sample find_potential ( DE a, DeRound r, int domain_max ) {

  Potential_Match_Sample g = new Potential_Match_Sample (domain_max)

  g.traders = a.traders.collect { DeTrader t -> create_trader ( r, t, false ) }

  a.traders.each { DeTrader seller ->
    Trader s = find_trader ( g, seller )

    a.traders.each { DeTrader buyer ->
      Trader b = g.traders.find { it.username == seller.person.username }

      if (s != b)
        s.counterpart_capacities.put ( b, De_Extensions.counterparty_credit_vol_limit ( a, r, seller, buyer ) as Integer )
    }
  }

  g.solve_old ()

  return g
}

static Potential_Match_Sample find_solution_with_buy_min ( DE a, DeRound r, int domain_max ) {

  Potential_Match_Sample g = new Potential_Match_Sample (domain_max)

  g.traders = a.traders.collect { DeTrader t -> create_trader ( r, t, true ) } // use actual minimus

  a.traders.each { DeTrader seller ->
    Trader s = find_trader ( g, seller )

    a.traders.each { DeTrader buyer ->
      Trader b = g.traders.find { it.username == seller.person.username }

      if (s != b)
        s.counterpart_capacities.put ( b, De_Extensions.counterparty_credit_vol_limit ( a, r, seller, buyer ) as Integer )
    }
  }

  g.solve_old ()
  return g
}

*/

}
