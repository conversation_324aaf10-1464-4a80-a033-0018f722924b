# Code Style and Conventions

## Language
- Kotlin is the primary language (version 2.0.10)
- Java interoperability is used in some cases (especially for JVM-based libraries)

## Naming Conventions
- **Packages**: Lower case, snake_case for multi-word package names (e.g., `au21.engine.domain.de.services.matcher`)
- **Classes**: PascalCase (e.g., `DeAuction`, `EngineCommandHandler`)
- **Functions**: camelCase with underscores for domain-specific names (e.g., `de_auctions()`, `create_session_and_login()`)
- **Variables**: camelCase (e.g., `sessionId`, `auctioneerCount`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `OBJECTDB_URL`)
- **File Names**: Match class names for files containing a single class (e.g., `DeAuction.kt`)

## Code Organization
- Domain-driven design approach with clear separation of concerns
- Command pattern used extensively for operations
- Framework utilities separated from domain logic
- Packages follow functionality grouping (framework, domain, etc.)

## Documentation
- Comments use standard KDoc format
- Some files include descriptive headers about their purpose
- PlantUML and Mermaid diagrams are used for visual documentation

## Type System
- <PERSON><PERSON><PERSON>'s type system is used extensively
- Explicit types are used in function signatures
- Nullable types are explicitly marked with `?`

## Dependencies
- Dependencies are managed through Gradle
- Versions are specified in the build.gradle.kts file as constants

## Testing
- Kotest is used as the primary testing framework
- Tests follow JUnit 5 conventions
- Mockk is used for mocking

## GraphQL API Conventions
- GraphQL endpoints are annotated with `@GraphQLApi`
- Query methods are annotated with `@Query`
- Mutation methods are annotated with `@Mutation`

## Command Pattern Implementation
- Commands follow a consistent naming convention (`*Command`)
- Commands are serialized to JSON for transport
- Command handlers process commands and produce results

## Database Conventions
- ObjectDB is used as the database
- Entity classes are enhanced through a Gradle task
- Entity managers are injected where database access is needed

## Error Handling
- Exceptions are used for error cases
- AlertException is used for user-facing errors

## Observability
- OpenTelemetry is used for tracing
- GELF logging is used for structured logging
- JaCoCo is used for code coverage