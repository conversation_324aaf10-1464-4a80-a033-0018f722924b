package au21.engine.domain.common.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.framework.commands.AlertException
import au21.engine.test.helpers.base.command.CommandTestBase
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.quarkus.test.junit.QuarkusTest
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.util.*
import java.util.stream.Stream

@QuarkusTest
@Disabled
internal class ClientSocketCommandTest : CommandTestBase() {

    @Nested
    inner class Mutation {
        @Test
        fun `create a new session and verify it's state in database`() {
            val sid = UUID.randomUUID().toString()
            val clientSocketCommand =
                ClientSocketCommand(sid, AuSession.ClientSocketState.OPENED, "chrome", "1.2", "mac")
            handle(null, clientSocketCommand)
            val session: AuSession? = findSession(sid)
            session shouldNotBe null
            session?.session_id shouldBe sid
            session?.socket_state shouldBe AuSession.ClientSocketState.OPENED
            session?.browser_name shouldBe "chrome"
            session?.browser_version shouldBe "1.2"
            session?.browser_os shouldBe "mac"
        }

        @Test
        fun `create a new session and close it and open it again and verify it's state in database`() {
            val sid = UUID.randomUUID().toString()

            val openSocketCommand = ClientSocketCommand(sid, AuSession.ClientSocketState.OPENED, "chrome", "1.2", "mac")
            handle(null, openSocketCommand)

            val session: AuSession? = findSession(sid)
            session shouldNotBe null
            session?.socket_state shouldBe AuSession.ClientSocketState.OPENED

            val closeSocketCommand = ClientSocketCommand(sid, AuSession.ClientSocketState.CLOSED)
            handle(session, closeSocketCommand)

            val sessionClosed = findSession(sid)
            sessionClosed shouldNotBe null
            sessionClosed?.session_id shouldBe sid
            sessionClosed?.socket_state shouldBe AuSession.ClientSocketState.CLOSED

            val openSocketAgainCommand =
                ClientSocketCommand(sid, AuSession.ClientSocketState.OPENED, "chrome", "1.2", "mac")
            handle(session, openSocketAgainCommand)

            val sessionOpenedAgain = findSession(sid)
            sessionOpenedAgain shouldNotBe null
            sessionOpenedAgain?.session_id shouldBe sid
            sessionOpenedAgain?.socket_state shouldBe AuSession.ClientSocketState.OPENED
        }
    }


    @TestInstance(TestInstance.Lifecycle.PER_CLASS)
    @Nested
    inner class Validation {

        @ParameterizedTest
        @MethodSource("testCases")
        fun `valid session id parameter`(data: TestData) {
            val clientSocketCommand =
                ClientSocketCommand(data.sid, AuSession.ClientSocketState.OPENED, "chrome", "1.2", "mac")
            val exception = shouldThrow<AlertException> {
                handle(null, clientSocketCommand)
            }
            exception.message shouldBe data.expectedMessage
        }

        private fun testCases() = Stream.of(
            TestData(sid = "", expectedMessage = "No session id."),
            TestData(sid = "0", expectedMessage = "Session id cannot be zero"),
        )
    }

    data class TestData(
        val sid: String, val expectedMessage: String
    )
}
