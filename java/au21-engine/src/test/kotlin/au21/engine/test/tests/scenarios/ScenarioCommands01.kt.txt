package au21.engine.test.tests.scenarios

import au21.engine.domain.common.model.*
import au21.engine.domain.common.services.company_by_shortname
import au21.engine.domain.common.services.find_auction_by_name
import au21.engine.domain.common.services.traders
import au21.engine.domain.common.services.user_by_username
import au21.engine.domain.common.viewmodel.*
import au21.engine.domain.de.model.*
import au21.engine.domain.de.model.DeAuction.DeBidConstraints
import au21.engine.domain.de.viewmodel.*
import au21.engine.framework.PageName
import au21.engine.framework.commands.AlertException
import au21.engine.test.helpers.base.command.CommandTestValidator
import au21.engine.test.helpers.call_fn
import au21.engine.test.helpers.validators.LiveStoreValidator
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldContain
import io.quarkus.test.junit.QuarkusTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.TestInstance.Lifecycle.PER_CLASS

@QuarkusTest
@TestInstance(PER_CLASS)
class ScenarioCommands01 : CommandTestValidator() {

    @Test
    fun test() {
        call_fn(this::init_db)
        call_fn(this::create_auctioneer_session)
        call_fn(this::login_auctioneer)
        call_fn(this::create_auction)
        call_fn(this::user_page_select)
        call_fn(this::a2_user_create)
        call_fn(this::a2_user_delete)
        call_fn(this::create_companies)
        call_fn(this::create_trader_users)
        call_fn(this::delete_b1)
        call_fn(this::delete_c1)
        call_fn(this::recreate_c1)
        call_fn(this::recreate_b1)
        call_fn(this::create_trader_sessions)
        call_fn(this::login_traders)
        call_fn(this::edit_credits)
        call_fn(this::auctioneer_select_auction)
        call_fn(this::add_traders_to_auction)
        call_fn(this::remove_b1_from_auction)
        call_fn(this::re_add_c1_to_auction)
        call_fn(this::b1_selects_auction_before_starting_price_set)
        call_fn(this::fail_to_remove_c1_from_auction_after_has_seen_auction)
        call_fn(this::notice_set) // should be auctioneer only at this point
        call_fn(this::c1_set_eligibility)
        call_fn(this::set_starting_price)
        call_fn(this::announce_starting_price)
        call_fn(this::start_auction)
        call_fn(this::b2_selects_auction_after_auction_started)
        call_fn(this::message_broadcast)
        call_fn(this::first_round_b1_buys)
        call_fn(this::close_round_1)
        call_fn(this::next_round)
        call_fn(this::second_round_b1_same_b2_sells)
        call_fn(this::close_round_2)
        call_fn(this::award_auction)
    }

    lateinit var a1_store: LiveStoreValidator
    lateinit var b1_store: LiveStoreValidator
    lateinit var b2_store: LiveStoreValidator
    val all_stores = { listOf(a1_store, b1_store, b2_store) }
    lateinit var a1: Person
    lateinit var a2: Person
    lateinit var b1: Person
    lateinit var b2: Person
    lateinit var c1: Company
    lateinit var c2: Company
    lateinit var de: DeAuction
    lateinit var sa1: AuSession
    lateinit var sb1: AuSession
    lateinit var sb2: AuSession

    val auction_name = "Auction 1"


    val de_row_initial by lazy { // lazy because de not yet created
        AuctionRowElement(
            id = de.id_str(),
            auction_design = "DeAuction",
            auction_id = de.id_str(),
            auction_name = "Auction 1",
            isClosed = false,
            isHidden = false,
            starting_time_text = de.starting_time_text(), // TODO
            common_state_text = "Waiting for starting price"
        )
    }
    val de_row_announced by lazy {
        de_row_initial.copy(common_state_text = "Starting price announced")
    }
    val de_row_open by lazy {
        de_row_initial.copy(
            common_state_text = "Round 1 open for orders!",
            starting_time_text = "Auction started"
        )
    }

    val settings by lazy {
        DeSettingsValue(
            auction_name = de.auction_name,
            default_buyer_max = "50",
            default_seller_max = "50",
            price_change_initial = "0.500",
            price_change_post_reversal = "0.125",
            price_decimal_places = 3,
            price_label = "cpp",
            excess_level_0_label = "+",
            excess_level_1_label = "++",
            excess_level_2_label = "+++",
            excess_level_3_label = "++++",
            excess_level_4_label = "+++++",
            excess_level_1_volume = "10",
            excess_level_2_volume = "20",
            excess_level_3_volume = "30",
            excess_level_4_volume = "40",
            value_multiplier = "10000.0",
            volume_label = "MMlb",
            volume_minimum = "1",
            volume_step = "1",
            starting_price_announcement_mins = 5,
            starting_time = time_value.date_time,
            round_red_secs = 15,
            round_orange_secs = 30,
            round_open_min_secs = 15,
            round_closed_min_secs = 5
        )
    }

    val de_status_round_open = DeCommonStatusValue(
        isClosed = false,
        price_direction = null,
        price_has_reversed = false,
        round_number = 1,
        round_price = "100.000",
        round_seconds = 0,
        starting_price_announced = true,
        starting_time_text = "Auction started",
        common_state = DeCommonState.ROUND_OPEN,
        common_state_text = "Round 1 open for orders!",
    )

    val broadcast_message = MessageElement(
        id = "AuctionMessage." + fixed_time.toDate().time,
        from = "Auctioneer to all",
        message = "test message to all",
        message_type = AuMessageType.AUCTIONEER_BROADCAST,
        message_type_label = "AUCTIONEER_BROADCAST",
        timestamp = fixed_time.toDate().time,
        timestamp_label = "Dec 13, 08:00:00"
    )

    val de_matrix_edges: List<DeMatrixEdgeElement> by lazy {
        listOf(
            DeMatrixEdgeElement(
                id = DeMatrixEdgeElement.to_id(
                    round_num = 1,
                    b_cid = c2.id_str(),
                    s_cid = c1.id_str(),
                ),
                r = 1,
                buyer_cid = c2.id_str(),
                buyer_shortname = "c2",
                seller_cid = c1.id_str(),
                seller_shortname = "c1",
                match = 0,
                capacity = 0,
                //   credit = null,
                credit_str = "no limit"
            ),
            DeMatrixEdgeElement(
                id = DeMatrixEdgeElement.to_id(
                    round_num = 1,
                    b_cid = c1.id_str(),
                    s_cid = c2.id_str(),
                ),
                r = 1,
                buyer_shortname = "c1",
                buyer_cid = c1.id_str(),
                seller_shortname = "c2",
                seller_cid = c2.id_str(),
                match = 0,
                capacity = 0,
                //      credit = 20_000_000.0,
                credit_str = "$20,000,000.00"
            )
        )
    }

    fun init_db() {
        expect_last_stores_to_be_empty()
        last_stores().shouldContainExactlyInAnyOrder(emptyList())

        db_init_command()
        expect_last_stores_to_be_empty()

        expect_db_inited()
        expect_last_stores_to_be_empty()
    }

    fun create_auctioneer_session() {
        sa1 = create_session()
        a1_store = create_store_validator(sa1)
        expect_stores(true)
    }

    fun login_auctioneer() {
        login_command(sa1, "a1", "1")
        sa1.user.shouldNotBeNull()
        a1 = sa1.user!!

        a1_store.apply {
            login(AuUserRole.AUCTIONEER, a1)

            user_set(
                UserElement(
                    id = a1.id_str(),
                    company_id = "",
                    company_longname = "",
                    company_shortname = "",
                    email = "",
                    isAuctioneer = true,
                    isObserver = false,
                    isTester = false,
                    password = "1",
                    phone = "",
                    role = AuUserRole.AUCTIONEER,
                    username = "a1",
                    user_id = a1.id_str(),
                    isOnline = true,
                    current_auction_id = null,
                    termination_reason = null,
                    socket_state = AuSession.ClientSocketState.OPENED,
                    socket_state_last_closed = null,
                    has_connection_problem = false
                )
            )
        }

        expect_stores(true)
        expect_session_users("a1")
        expect_user_logged_in("a1")
        sa1.is_logged_in().shouldBeTrue()
    }

    fun create_auction() {
        create_default_de_auction(sa1, auction_name)
        expect_auctions_by_name(auction_name)
        db.find_auction_by_name<DeAuction>(auction_name).let {
            it.shouldNotBeNull()
            de = it
        }

        a1_store.auction_row_set(de_row_initial)
        expect_stores(true)
    }

    fun user_page_select() {
        page_set_command(sa1, PageName.USER_PAGE)
        a1_store.apply {
            auction_rows_clear()
            page_set_and_auction_reset(PageName.USER_PAGE)
            messages_clear()
        }
        expect_stores(true)
    }

    fun a2_user_create() {
        create_user(sa1, AuUserRole.AUCTIONEER, "a2", null)
        db.user_by_username("a2").also {
            it.shouldNotBeNull()
            a2 = it
        }

        a1_store.apply {
            user_set(
                UserElement(
                    id = a2.id_str(),
                    company_id = "",
                    company_longname = "",
                    company_shortname = "",
                    email = "",
                    isAuctioneer = true,
                    isObserver = false,
                    isTester = false,
                    password = "1",
                    phone = "",
                    role = AuUserRole.AUCTIONEER,
                    username = "a2",
                    user_id = a2.id_str(),
                    isOnline = false,
                    current_auction_id = null,
                    termination_reason = null,
                    socket_state = null,
                    socket_state_last_closed = null,
                    has_connection_problem = false
                )
            )
        }
        expect_stores(true)
    }

    fun a2_user_delete() {
        user_delete_command(sa1, a2)
        a1_store.user_delete(a2)
        expect_stores(true)
    }

    fun create_companies() {
        val company_shortnames = listOf("c1", "c2") //, "c3", "c4")
        company_shortnames.forEach {
            company_save_command(sa1, null, it, "$it-long")
        }

        val companies = db.findAll<Company>()
        companies.map { it.shortname }.shouldContainExactlyInAnyOrder(company_shortnames)

        c1 = companies[0]
        c2 = companies[1]

        a1_store.apply {
            company_set(
                CompanyElement(
                    id = c1.id_str(),
                    company_id = c1.id_str(),
                    company_shortname = c1.shortname,
                    company_longname = c1.longname,
                )
            )
            company_set(
                CompanyElement(
                    id = c2.id_str(),
                    company_id = c2.id_str(),
                    company_shortname = c2.shortname,
                    company_longname = c2.longname,
                )
            )

            counterparty_set(
                CounterpartyCreditElement(
                    id = "SELLER.${c1.id_str()}.BUYER.${c2.id_str()}",
                    seller_id = c1.id_str(),
                    seller_longname = "c1",
                    seller_shortname = "c1",
                    buyer_id = c2.id_str(),
                    buyer_longname = "c2",
                    buyer_shortname = "c2",
                    //limit = null,
                    limit_str = "no limit"
                )
            )
            counterparty_set(
                CounterpartyCreditElement(
                    id = "SELLER.${c2.id_str()}.BUYER.${c1.id_str()}",
                    seller_id = c2.id_str(),
                    seller_longname = "c2",
                    seller_shortname = "c2",
                    buyer_id = c1.id_str(),
                    buyer_longname = "c1",
                    buyer_shortname = "c1",
                    //limit = null,
                    limit_str = "no limit"
                )
            )
        }
        expect_stores(true)
    }

    fun create_trader_users() {

        create_user(sa1, AuUserRole.TRADER, "b1", c1)
        create_user(sa1, AuUserRole.TRADER, "b2", c2)

        val trader_users: List<Person> = db.traders()
        b1 = trader_users[0]
        b2 = trader_users[1]
        b1.shouldNotBeNull()
        b2.shouldNotBeNull()

        a1_store.apply {
            user_set(
                UserElement(
                    id = b1.id_str(),
                    company_id = c1.id_str(),
                    company_longname = "c1",
                    company_shortname = "c1",
                    email = "",
                    isAuctioneer = false,
                    isObserver = false,
                    isTester = false,
                    password = "1",
                    phone = "",
                    role = AuUserRole.TRADER,
                    username = "b1",
                    user_id = b1.id_str(),
                    isOnline = false,
                    current_auction_id = null,
                    termination_reason = null,
                    socket_state = null,
                    socket_state_last_closed = null,
                    has_connection_problem = false
                )
            )
            user_set(
                UserElement(
                    id = b2.id_str(),
                    company_id = c2.id_str(),
                    company_longname = "c2",
                    company_shortname = "c2",
                    email = "",
                    isAuctioneer = false,
                    isObserver = false,
                    isTester = false,
                    password = "1",
                    phone = "",
                    role = AuUserRole.TRADER,
                    username = "b2",
                    user_id = b2.id_str(),
                    isOnline = false,
                    current_auction_id = null,
                    termination_reason = null,
                    socket_state = null,
                    socket_state_last_closed = null,
                    has_connection_problem = false
                )
            )

        }

        expect_stores(true)
    }

    fun delete_b1() {
        shouldThrow<AlertException> {
            company_delete_command(sa1, c1)
        }.message.shouldBe("Cannot delete company: ${c1.shortname} because it has traders.")

        user_delete_command(sa1, b1)
        a1_store.user_delete(b1)
        expect_stores(true)
    }

    fun delete_c1() {
        company_delete_command(sa1, c1)
        a1_store.company_remove(c1)
        a1_store.counterparties_remove(c1)
        expect_stores(true)
    }

    fun recreate_c1() {

        // trying to re-create b1 with no c1, should fail!
        shouldThrow<AlertException> {
            create_user(sa1, AuUserRole.TRADER, "b1", c1)
        }.message.shouldContain("Traders must have a company, and none found with id:")


        company_save_command(sa1, null, "c1", "$c1-long")
        db.company_by_shortname("c1").let {
            it.shouldNotBeNull()
            c1 = it
        }

        a1_store.apply {
            company_set(
                CompanyElement(
                    id = c1.id_str(),
                    company_id = c1.id_str(),
                    company_shortname = c1.shortname,
                    company_longname = c1.longname,
                )
            )
            company_set(
                CompanyElement(
                    id = c2.id_str(),
                    company_id = c2.id_str(),
                    company_shortname = c2.shortname,
                    company_longname = c2.longname,
                )
            )

            counterparty_set(
                CounterpartyCreditElement(
                    id = "SELLER.${c1.id_str()}.BUYER.${c2.id_str()}",
                    seller_id = c1.id_str(),
                    seller_longname = "c1",
                    seller_shortname = "c1",
                    buyer_id = c2.id_str(),
                    buyer_longname = "c2",
                    buyer_shortname = "c2",
                    //limit = null,
                    limit_str = "no limit"
                )
            )
            counterparty_set(
                CounterpartyCreditElement(
                    id = "SELLER.${c2.id_str()}.BUYER.${c1.id_str()}",
                    seller_id = c2.id_str(),
                    seller_longname = "c2",
                    seller_shortname = "c2",
                    buyer_id = c1.id_str(),
                    buyer_longname = "c1",
                    buyer_shortname = "c1",
                    //limit = null,
                    limit_str = "no limit"
                )
            )
        }
        expect_stores(true)
    }

    fun recreate_b1() {
        user_save_command(
            sa1,
            company = c1,
            email = "",
            password = "1",
            phone = "",
            role = AuUserRole.TRADER,
            user = null,
            username = "b1"
        )
        // need to point b1 to new entity (ie: different id):
        db.user_by_username("b1").also {
            it.shouldNotBeNull()
            b1 = it
        }
        a1_store.user_set(
            UserElement(
                company_id = c1.id_str(),
                company_longname = "c1",
                company_shortname = "c1",
                current_auction_id = null,
                email = "",
                has_connection_problem = false,
                id = b1.id_str(),
                isAuctioneer = false,
                isObserver = false,
                isOnline = false,
                isTester = false,
                password = "1",
                phone = "",
                role = AuUserRole.TRADER,
                socket_state = null,
                socket_state_last_closed = null,
                termination_reason = null,
                user_id = b1.id_str(),
                username = "b1",
            )
        )

        expect_stores(true)
    }

    fun create_trader_sessions() {
        listOf(1, 2).map { create_session().also { it.shouldNotBeNull() } }.also {
            sb1 = it[0]
            sb2 = it[1]
        }
        b1_store = create_store_validator(sb1)
        b2_store = create_store_validator(sb2)
        expect_stores(true)
    }

    fun login_traders() {
        login_command(sb1, "b1", "1")
        login_command(sb2, "b2", "1")

        b1_store.apply {
            login(AuUserRole.TRADER, b1)
            counterparty_set(a1_store.find_counterparty_credit(c1, c2)!!)
        }
        b2_store.apply {
            login(AuUserRole.TRADER, b2)
            counterparty_set(a1_store.find_counterparty_credit(c2, c1)!!)
        }
        a1_store.apply {
            trader_logged_in(sb1)
            trader_logged_in(sb2)
        }
        expect_stores(true)
    }

    fun edit_credits() {
        // only setting for one!
        counterparty_credit_set(de, sa1, seller = c2, buyer = c1, "\$20,000,000")
        CounterpartyCreditElement(
            id = "SELLER.${c2.id_str()}.BUYER.${c1.id_str()}",
            seller_id = c2.id_str(),
            seller_longname = c2.longname,
            seller_shortname = c2.shortname,
            buyer_id = c1.id_str(),
            buyer_longname = c1.longname,
            buyer_shortname = c1.shortname,
            //limit = 1_000_000.0,
            limit_str = "\$20,000,000.00"
        ).also {
            a1_store.counterparty_set(it)
            b2_store.counterparty_set(it)
        }
        expect_stores(true)
    }

    fun auctioneer_select_auction() {
        auction_select_command(sa1, de)
        a1_store.de_auction_select(
            DeAuctionValue(
                auction_id = de.id_str(),
                auctioneer_info = DeAuctioneerInfoValue(
                    pen_round = "---",
                    last_round = 1,
                    pen_buyers = "---",
                    last_buyers = "0",
                    pen_sellers = "---",
                    last_sellers = "0",
                    pen_total_buy = "---",
                    last_total_buy = "0",
                    pen_total_sell = "---",
                    last_total_sell = "0",
                    pen_sell_dec = "",
                    last_sell_dec = "",
                    pen_match = "---",
                    last_match = "0",
                    pen_excess = "---",
                    last_excess = "0",
                    potential = "0"
                ),
                auctioneer_status = DeAuctioneerStatusValue(
                    announced = false,
                    auctioneer_state = DeAuctioneerState.STARTING_PRICE_NOT_SET,
                    auctioneer_state_text = "Starting price not set",
                    autopilot = AutopilotMode.DISENGAGED,
                    awardable = false,
                    controls = mapOf(
                        DeFlowControlType.SET_STARTING_PRICE to true,
                        DeFlowControlType.ANNOUNCE_STARTING_PRICE to false,
                        DeFlowControlType.START_AUCTION to false,
                        DeFlowControlType.CLOSE_ROUND to false,
                        DeFlowControlType.REOPEN_ROUND to false,
                        DeFlowControlType.NEXT_ROUND to false,
                        DeFlowControlType.AWARD_AUCTION to false
                    ),
                    excess_direction = "none",
                    excess_level = "0",
                    price_has_overshot = false,
                    round_open_min_secs = null,
                    starting_price = "---",
                    time_state = DeTimeState.AUCTION_HAS_STARTED,
                ),
                award_value = DeAwardValue(
                    round_results = listOf(
                        DeRoundResultVM(
                            round_number = 1,
                            round_price = "---",
                            buy_total = "0",
                            sell_total = "0",
                            match_total = "0",
                            trader_flows = emptyList(),
                            matches = emptyList()
                        )
                    )
                ),
                blotter = DeBlotter(
                    rounds = listOf(
                        DeRoundElement(
                            id = "ROUND.1",
                            buy_volume = "0",
                            buyer_count = "0",
                            excess_direction = "none",
                            excess_level = "0",
                            has_reversed = false,
                            match_changed = "0",
                            matched = "0",
                            potential = "0",
                            potential_changed = "0",
                            raw_matched = "0",
                            round_direction = null,
                            round_duration = "not implemented",
                            round_number = 1,
                            round_price = null,
                            round_price_str = "---",
                            sell_change = 0,
                            sell_volume = "0",
                            seller_count = "0"
                        )
                    ), traders = emptyList(), round_traders = emptyList()
                ),
                matrix_last_round = DeMatrixRoundElement(
                    id = "Round.1", round_number = 1, nodes = emptyList(), edges = emptyList()
                ),
                messages = emptyList(),
                common_status = DeCommonStatusValue(
                    isClosed = false,
                    price_direction = null,
                    price_has_reversed = false,
                    round_number = 1,
                    round_price = "waiting",
                    round_seconds = 0,
                    starting_price_announced = false,
                    starting_time_text = "$fixed_time_formatted  (starting now)",
                    common_state = DeCommonState.SETUP,
                    common_state_text = "Waiting for starting price"
                ),
                notice = "",
                settings = settings,
                trader_history_rows = emptyList(),
                trader_info = null,
                users_that_have_seen_auction = emptySet()
            )
        )
        expect_stores(true)
    }

    fun add_traders_to_auction() {

        de_traders_add_command(sa1, de, listOf(c1, c2))

        a1_store.apply {

            de_award_flow_set(
                DeTraderFlowVM(
                    company_shortname = "c1", company_id = c1.id_str(), OrderVolumeType.NONE, volume = "0"
                )
            )

            de_award_flow_set(
                DeTraderFlowVM(
                    company_shortname = "c2", company_id = c2.id_str(), OrderVolumeType.NONE, volume = "0"
                )
            )

            de_award_match_set(
                round_number = 1,
                DeScenarioMatchVM(
                    round_number = 1,
                    buyer_id = c1.id_str(),
                    buyer_shortname = "c1",
                    seller_id = c2.id_str(),
                    seller_shortname = "c2",
                    actual_match = 0,
                    actual_match_str = "0"
                )
            )
            de_award_match_set(
                round_number = 1,
                DeScenarioMatchVM(
                    round_number = 1,
                    buyer_id = c2.id_str(),
                    buyer_shortname = "c2",
                    seller_id = c1.id_str(),
                    seller_shortname = "c1",
                    actual_match = 0,
                    actual_match_str = "0"
                )
            )

            // blotter: traders:
            de_blotter_trader_set(
                DeTraderElement(
                    id = "COMPANY.${c1.id_str()}",
                    has_seen_auction = false,
                    company_id = c1.id_str(),
                    shortname = "c1",
                    rank = null,
                )
            )
            de_blotter_trader_set(
                DeTraderElement(
                    id = "COMPANY.${c2.id_str()}",
                    has_seen_auction = false,
                    company_id = c2.id_str(),
                    shortname = "c2",
                    rank = null,
                )
            )

            // blotter: round traders:
            de_blotter_round_trader_set(
                DeRoundTraderElement(
                    id = "ROUND.1.COMPANY.${c1.id_str()}",
                    changed = false,
                    cid = c1.id_str(),
                    order_submission_type = OrderSubmissionType.DEFAULT,
                    order_volume_type = OrderVolumeType.NONE,
                    round = 1,
                    timestamp_formatted = "08:00:00", // unclear why we have the different formatting?
                    order_submitted_by = "default",
                    volume_int = 0,
                    volume_str = "0"
                )
            )

            de_blotter_round_trader_set(
                DeRoundTraderElement(
                    id = "ROUND.1.COMPANY.${c2.id_str()}",
                    changed = false,
                    cid = c2.id_str(),
                    order_submission_type = OrderSubmissionType.DEFAULT,
                    order_volume_type = OrderVolumeType.NONE,
                    round = 1,
                    timestamp_formatted = "08:00:00",
                    order_submitted_by = "default",
                    volume_int = 0,
                    volume_str = "0"
                )
            )


            de_matrix_node_element_set(
                DeMatrixNodeElement(
                    id = "R_1_T_${c1.id_str()}",
                    shortname = "c1",
                    buy_match = 0,
                    buy_max = 50,
                    buy_min = 0,
                    buy_vol = 0,
                    cid = c1.id_str(),
                    round = 1,
                    sell_match = 0,
                    sell_max = 50,
                    sell_min = 0,
                    sell_vol = 0,
                )
            )
            de_matrix_node_element_set(
                DeMatrixNodeElement(
                    id = "R_1_T_${c2.id_str()}",
                    shortname = "c2",
                    buy_match = 0,
                    buy_max = 50,
                    buy_min = 0,
                    buy_vol = 0,
                    cid = c2.id_str(),
                    round = 1,
                    sell_match = 0,
                    sell_max = 50,
                    sell_min = 0,
                    sell_vol = 0,
                )
            )

            de_matrix_edges.forEach {
                de_matrix_edge_element_set(it)
            }
        }
        b1_store.auction_row_set(de_row_initial)
        b2_store.auction_row_set(de_row_initial)

        expect_stores(true)
    }

    fun remove_b1_from_auction() {
        de_trader_remove_command(sa1, de, listOf(c1))

        a1_store.apply {
            de_blotter_remove(c1)
            de_matrix_company_remove(c1)
            user_current_auction_id(b1, null)
            de_award_flow_remove(c1)
            de_award_matches_clear(round_number = 1)
        }
        b1_store.apply {
            auction_rows_clear()
            page_set_and_auction_reset(PageName.HOME_PAGE)
        }


        expect_stores(true)
    }

    fun re_add_c1_to_auction() {
        de_traders_add_command(sa1, de, listOf(c1))
        a1_store.apply {
            de_blotter_trader_set(
                DeTraderElement(
                    id = "COMPANY.${c1.id_str()}",
                    has_seen_auction = false,
                    company_id = c1.id_str(),
                    shortname = "c1",
                    rank = null,
                )
            )

            de_blotter_round_trader_set(
                DeRoundTraderElement(
                    id = "ROUND.1.COMPANY.${c1.id_str()}",
                    changed = false,
                    cid = c1.id_str(),
                    order_submission_type = OrderSubmissionType.DEFAULT,
                    order_volume_type = OrderVolumeType.NONE,
                    round = 1,
                    timestamp_formatted = "08:00:00",
                    order_submitted_by = "default",
                    volume_int = 0,
                    volume_str = "0"
                )
            )

            de_matrix_node_element_set(
                DeMatrixNodeElement(
                    id = "R_1_T_${c1.id_str()}",
                    buy_match = 0,
                    buy_max = 50, // note: eligibility goes back to initial eligibily!
                    buy_min = 0,
                    buy_vol = 0,
                    cid = c1.id_str(),
                    round = 1,
                    sell_match = 0,
                    sell_max = 50,
                    sell_min = 0,
                    sell_vol = 0,
                    shortname = "c1"
                )
            )
            // re-add the edges: adding all again, even though we don't need the c1/c1 edge
            de_matrix_edges.forEach {
                de_matrix_edge_element_set(it)
            }

            de_award_flow_set(
                DeTraderFlowVM(
                    company_shortname = "c1", company_id = c1.id_str(), OrderVolumeType.NONE, volume = "0"
                )
            )

            de_award_match_set(
                round_number = 1,
                DeScenarioMatchVM(
                    round_number = 1,
                    buyer_id = c1.id_str(),
                    buyer_shortname = "c1",
                    seller_id = c2.id_str(),
                    seller_shortname = "c2",
                    actual_match = 0,
                    actual_match_str = "0"
                )
            )
            de_award_match_set(
                round_number = 1,
                DeScenarioMatchVM(
                    round_number = 1,
                    buyer_id = c2.id_str(),
                    buyer_shortname = "c2",
                    seller_id = c1.id_str(),
                    seller_shortname = "c1",
                    actual_match = 0,
                    actual_match_str = "0"
                )
            )

        }
        b1_store.auction_row_set(de_row_initial)
        expect_stores(true)
    }

    fun b1_selects_auction_before_starting_price_set() {
        auction_select_command(sb1, de)
        a1_store.apply {
            user_has_seen_auction(sb1)
            user_current_auction_id(b1, de)
        }
        b1_store.apply {
            auction_rows_clear()
            de_auction_select(
                DeAuctionValue(
                    auction_id = de.id_str(),
                    auctioneer_info = null,
                    auctioneer_status = null,
                    award_value = null,
                    blotter = DeBlotter(
                        rounds = emptyList(), traders = emptyList(), round_traders = emptyList()
                    ),
                    matrix_last_round = null,
                    messages = emptyList(),
                    common_status = DeCommonStatusValue(
                        isClosed = false,
                        price_direction = null,
                        price_has_reversed = false,
                        round_number = 1,
                        round_price = "waiting",
                        round_seconds = 0,
                        starting_price_announced = false,
                        starting_time_text = "$fixed_time_formatted  (starting now)",
                        common_state = DeCommonState.SETUP,
                        common_state_text = "Waiting for starting price"
                    ),
                    notice = "",
                    settings = settings,
                    trader_history_rows = listOf(
                        DeTraderHistoryRowElement(
                            id = "ROUND.1",
                            auction_id = de.id_str(),
                            bid_constraints = DeBidConstraints(
                                max_buy_volume = 50, min_buy_volume = 0, min_sell_volume = 0, max_sell_volume = 50
                            ),
                            company_id = c1.id_str(),
                            excess_direction = "",
                            excess_level = "",
                            order_submitted_by = "(default)",
                            order_submission_type = OrderSubmissionType.DEFAULT,
                            order_volume_type = OrderVolumeType.NONE,
                            price_direction = null,
                            price_has_reversed = false,
                            price_suffix = "none",
                            round_number = "1",
                            round_price = "waiting",
                            value = "---",
                            volume = "0"
                        )
                    ),
                    trader_info = DeTraderInfoValue(
                        auction_id = de.id_str(),
                        award_direction = "TODO",
                        award_line = null,
                        awarded_price = "---",
                        awarded_round_number = "---",
                        awarded_value = "---",
                        awarded_volume = "---",
                        bid_constraints = DeBidConstraints(
                            max_buy_volume = 50, min_buy_volume = 0, min_sell_volume = 0, max_sell_volume = 50
                        ),
                        company_id = c1.id_str(),
                        order_submission_type = OrderSubmissionType.DEFAULT,
                        order_volume_type = OrderVolumeType.NONE,
                        order_volume = 0,
                        price_label = "cpp",
                        round_number = 1,
                        round_price = "waiting",
                        value = "---",
                        volume_label = "MMlb"
                    ),                // will override below
                    users_that_have_seen_auction = emptySet()
                )
            )
        }
        expect_stores(true)
    }

    fun fail_to_remove_c1_from_auction_after_has_seen_auction() {
        shouldThrow<AlertException> {
            de_trader_remove_command(sa1, de, listOf(c1))
        }.message.shouldBe("Unable to remove these bidders as they have already seen this auction: c1")
    }

    fun notice_set() {
        // only auctioneer should see the notice, as traders have not yet selected the auction
        notice_save_command(sa1, de, "test")
        listOf(a1_store, b1_store).forEach { it.notice_set("test") }
        expect_stores(true)

        notice_save_command(sa1, de, "")
        listOf(a1_store, b1_store).forEach { it.notice_set("") }
        expect_stores(true)

        notice_save_command(sa1, de, "test2")
        listOf(a1_store, b1_store).forEach { it.notice_set("test2") }
        expect_stores(true)
    }

    fun c1_set_eligibility() {
        de_eligibility_set_command(sa1, de, c1, max_buy = "51", max_sell = "52")
        a1_store.de_matrix_node_element_set(
            DeMatrixNodeElement(
                id = "R_1_T_${c1.id_str()}",
                buy_match = 0,
                buy_max = 51,
                buy_min = 0,
                buy_vol = 0,
                cid = c1.id_str(),
                round = 1,
                sell_match = 0,
                sell_max = 52,
                sell_min = 0,
                sell_vol = 0,
                shortname = "c1"
            )
        )
        b1_store.apply {
            de_trader_info_set(
                DeTraderInfoValue(
                    auction_id = de.id_str(),
                    award_direction = "TODO",
                    award_line = null,
                    awarded_price = "---",
                    awarded_round_number = "---",
                    awarded_value = "---",
                    awarded_volume = "---",
                    bid_constraints = DeBidConstraints(
                        max_buy_volume = 51, min_buy_volume = 0, min_sell_volume = 0, max_sell_volume = 52
                    ),
                    company_id = c1.id_str(),
                    order_submission_type = OrderSubmissionType.DEFAULT,
                    order_volume_type = OrderVolumeType.NONE,
                    order_volume = 0,
                    price_label = "cpp",
                    round_number = 1,
                    round_price = "waiting",
                    value = "---",
                    volume_label = "MMlb"
                )
            )
            de_trader_history_row_set(
                DeTraderHistoryRowElement(
                    id = "ROUND.1",
                    auction_id = de.id_str(),
                    bid_constraints = DeBidConstraints(
                        max_buy_volume = 51, min_buy_volume = 0, min_sell_volume = 0, max_sell_volume = 52
                    ),
                    company_id = c1.id_str(),
                    excess_direction = "",
                    excess_level = "",
                    order_submitted_by = "(default)",
                    order_submission_type = OrderSubmissionType.DEFAULT,
                    order_volume_type = OrderVolumeType.NONE,
                    price_direction = null,
                    price_has_reversed = false,
                    price_suffix = "none",
                    round_number = "1",
                    round_price = "waiting",
                    value = "---",
                    volume = "0"
                )
            )
        }
        expect_stores(true)
    }

    fun set_starting_price() {
        de_flow_control_command(sa1, de, DeFlowControlType.SET_STARTING_PRICE, "100.000")

        a1_store.de_starting_price_set("100.000", announced = false)
        b1_store.de_starting_price_set("100.000", announced = false)

        a1_store.apply {
            expected_store.de_auction!!.apply {
                // auctioneer status
                auctioneer_status!!.starting_price.shouldBe("100.000")
            }
        }


        // Common status of a1 and b1 should see '---' because  price not announced:
        listOf(a1_store, b1_store).forEach {
            it.expected_store.de_auction!!.common_status!!.round_price.shouldBe("waiting")
        }
        // b2 hasn't selected auction, so should be blank
        b2_store.expected_store.de_auction!!.common_status.shouldBeNull()

        b1_store.apply {
            expected_store.de_auction!!.apply {
                de_trader_history_row_set(
                    trader_history_rows[0].copy(value = "\$0.00")
                )
                de_trader_info_set(
                    trader_info!!.copy(value = "\$0.00")
                )
            }
        }
        expect_stores(true)
    }

    fun announce_starting_price() {

        de_flow_control_command(sa1, de, DeFlowControlType.ANNOUNCE_STARTING_PRICE)

        DeCommonStatusValue(
            isClosed = false,
            price_direction = null,
            price_has_reversed = false,
            round_number = 1,
            round_price = "100.000",
            round_seconds = 0,
            starting_time_text = "$fixed_time_formatted  (starting now)",
            starting_price_announced = true,
            common_state = DeCommonState.STARTING_PRICE_ANNOUNCED,
            common_state_text = "Starting price announced"
        ).also {
            a1_store.de_common_status_set(it)
            b1_store.de_common_status_set(it)
        }


        a1_store.apply {
            auction_rows_clear()

            de_auctioneer_info_set(
                DeAuctioneerInfoValue(
                    pen_round = "---",
                    last_round = 1,
                    pen_buyers = "---",
                    last_buyers = "0",
                    pen_sellers = "---",
                    last_sellers = "0",
                    pen_total_buy = "---",
                    last_total_buy = "0",
                    pen_total_sell = "---",
                    last_total_sell = "0",
                    pen_sell_dec = "",
                    last_sell_dec = "",
                    pen_match = "---",
                    last_match = "0",
                    pen_excess = "---",
                    last_excess = "0",
                    potential = "0"
                )
            )

            de_auctioneer_status_set(
                DeAuctioneerStatusValue(
                    announced = true,
                    auctioneer_state = DeAuctioneerState.STARTING_PRICE_ANNOUNCED,
                    auctioneer_state_text = "Starting price announced",
                    autopilot = AutopilotMode.DISENGAGED,
                    awardable = false,
                    controls = mapOf(
                        DeFlowControlType.SET_STARTING_PRICE to true,
                        DeFlowControlType.ANNOUNCE_STARTING_PRICE to false,
                        DeFlowControlType.START_AUCTION to true,
                        DeFlowControlType.CLOSE_ROUND to false,
                        DeFlowControlType.REOPEN_ROUND to false,
                        DeFlowControlType.NEXT_ROUND to false,
                        DeFlowControlType.AWARD_AUCTION to false
                    ),
                    excess_direction = "none",
                    excess_level = "0",
                    price_has_overshot = false,
                    round_open_min_secs = null,
                    starting_price = "100.000",
                    time_state = DeTimeState.AUCTION_HAS_STARTED,
                )
            )
            de_award_value_set(
                DeAwardValue(
                    round_results = listOf(
                        DeRoundResultVM(
                            round_number = 1,
                            round_price = "100.000",
                            buy_total = "0",
                            sell_total = "0",
                            match_total = "0",
                            trader_flows = listOf(
                                DeTraderFlowVM(
                                    company_shortname = "c1",
                                    company_id = c1.id_str(),
                                    OrderVolumeType.NONE,
                                    volume = "0"
                                ), DeTraderFlowVM(
                                    company_shortname = "c2",
                                    company_id = c2.id_str(),
                                    OrderVolumeType.NONE,
                                    volume = "0"
                                )
                            ),
                            matches = listOf(
                                DeScenarioMatchVM(
                                    round_number = 1,
                                    buyer_id = c2.id_str(),
                                    buyer_shortname = "c2",
                                    seller_id = c1.id_str(),
                                    seller_shortname = "c1",
                                    actual_match = 0,
                                    actual_match_str = "0"
                                ),
                                DeScenarioMatchVM(
                                    round_number = 1,
                                    buyer_id = c1.id_str(),
                                    buyer_shortname = "c1",
                                    seller_id = c2.id_str(),
                                    seller_shortname = "c2",
                                    actual_match = 0,
                                    actual_match_str = "0"
                                )
                            )
                        )
                    )
                )
            )

            de_blotter_round_set(
                DeRoundElement(
                    id = "ROUND.1",
                    buy_volume = "0",
                    buyer_count = "0",
                    excess_direction = "none",
                    excess_level = "0",
                    has_reversed = false,
                    match_changed = "0",
                    matched = "0",
                    potential = "0",
                    potential_changed = "0",
                    raw_matched = "0",
                    round_direction = null,
                    round_duration = "not implemented",
                    round_number = 1,
                    round_price = 100.0,
                    round_price_str = "100.000",
                    sell_change = 0,
                    sell_volume = "0",
                    seller_count = "0"
                )
            )

            de_blotter_trader_set(
                DeTraderElement(
                    id = "COMPANY.${c1.id_str()}",
                    has_seen_auction = true,
                    company_id = c1.id_str(),
                    shortname = "c1",
                    rank = null,
                )
            )

            de_blotter_trader_set(
                DeTraderElement(
                    id = "COMPANY.${c2.id_str()}",
                    has_seen_auction = false,
                    company_id = c2.id_str(),
                    shortname = "c2",
                    rank = null,
                )
            )

            de_matrix_round_set(
                DeMatrixRoundElement(
                    id = "Round.1", round_number = 1, nodes = listOf(
                        DeMatrixNodeElement(
                            id = "R_1_T_${c1.id_str()}",
                            buy_match = 0,
                            buy_max = 51,
                            buy_min = 0,
                            buy_vol = 0,
                            cid = c1.id_str(),
                            round = 1,
                            sell_match = 0,
                            sell_max = 52,
                            sell_min = 0,
                            sell_vol = 0,
                            shortname = "c1"
                        ), DeMatrixNodeElement(
                            id = "R_1_T_${c2.id_str()}",
                            buy_match = 0,
                            buy_max = 50,
                            buy_min = 0,
                            buy_vol = 0,
                            cid = c2.id_str(),
                            round = 1,
                            sell_match = 0,
                            sell_max = 50,
                            sell_min = 0,
                            sell_vol = 0,
                            shortname = "c2"
                        )
                    ), edges = emptyList()
                )
            )

            de_matrix_edges.forEach {
                de_matrix_edge_element_set(it)
            }

        }

        b1_store.apply {
            de_trader_info_set(
                DeTraderInfoValue(
                    auction_id = de.id_str(),
                    award_direction = "TODO",
                    award_line = null,
                    awarded_price = "---",
                    awarded_round_number = "---",
                    awarded_value = "---",
                    awarded_volume = "---",
                    bid_constraints = DeBidConstraints(
                        max_buy_volume = 51, min_buy_volume = 0, min_sell_volume = 0, max_sell_volume = 52
                    ),
                    company_id = c1.id_str(),
                    order_submission_type = OrderSubmissionType.DEFAULT,
                    order_volume_type = OrderVolumeType.NONE,
                    order_volume = 0,
                    price_label = "cpp",
                    round_number = 1,
                    round_price = "100.000",
                    value = "\$0.00",
                    volume_label = "MMlb"
                )
            )
            de_trader_history_row_set(
                DeTraderHistoryRowElement(
                    id = "ROUND.1",
                    auction_id = de.id_str(),
                    bid_constraints = DeBidConstraints(
                        max_buy_volume = 51, min_buy_volume = 0, min_sell_volume = 0, max_sell_volume = 52
                    ),
                    company_id = c1.id_str(),
                    excess_direction = "",
                    excess_level = "",
                    order_submission_type = OrderSubmissionType.DEFAULT,
                    order_submitted_by = "(default)",
                    order_volume_type = OrderVolumeType.NONE,
                    price_direction = null,
                    price_has_reversed = false,
                    price_suffix = "none",
                    round_number = "1",
                    round_price = "100.000",
                    value = "\$0.00",
                    volume = "0"
                )
            )
        }

        b2_store.apply {
            auction_row_set(de_row_announced)
        }

        expect_stores(true)
    }

    fun start_auction() {
        de_flow_control_command(sa1, de, DeFlowControlType.START_AUCTION)

        a1_store.apply {
            de_auctioneer_status_set(
                DeAuctioneerStatusValue(
                    announced = true,
                    auctioneer_state = DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN,
                    auctioneer_state_text = "Round 1 open, all orders not in.",
                    autopilot = AutopilotMode.DISENGAGED,
                    awardable = false,
                    controls = mapOf(
                        DeFlowControlType.SET_STARTING_PRICE to false,
                        DeFlowControlType.ANNOUNCE_STARTING_PRICE to false,
                        DeFlowControlType.START_AUCTION to false,
                        DeFlowControlType.CLOSE_ROUND to true,
                        DeFlowControlType.REOPEN_ROUND to false,
                        DeFlowControlType.NEXT_ROUND to false,
                        DeFlowControlType.AWARD_AUCTION to false
                    ),
                    excess_direction = "none",
                    excess_level = "0",
                    price_has_overshot = false,
                    round_open_min_secs = null,
                    starting_price = "100.000",
                    time_state = DeTimeState.AUCTION_HAS_STARTED
                )
            )
            de_common_status_set(de_status_round_open)
        }

        b1_store.apply {
            de_common_status_set(de_status_round_open)
        }

        b2_store.apply {
            auction_row_set(de_row_open)
        }
        expect_stores(true)
    }

    fun b2_selects_auction_after_auction_started() {

        shouldThrow<AlertException> {
            de_order_submit_command(sb2, de, OrderVolumeType.BUY, 1, "50")
        }.also {
            it.message.shouldBe("You can only submit orders from the auction page.")
        }

        auction_select_command(sb2, de)

        de_status_round_open.let {
            a1_store.de_common_status_set(it)
            b2_store.de_common_status_set(it)
        }

        a1_store.apply {
            // auction_rows_clear()
            de_blotter_trader_set(
                DeTraderElement(
                    id = "COMPANY.${c1.id_str()}",
                    has_seen_auction = true,
                    company_id = c1.id_str(),
                    shortname = "c1",
                    rank = null,
                )
            )
            de_blotter_trader_set(
                DeTraderElement(
                    id = "COMPANY.${c2.id_str()}",
                    has_seen_auction = true,
                    company_id = c2.id_str(),
                    shortname = "c2",
                    rank = null,
                )
            )
            user_has_seen_auction(sb1)
            user_has_seen_auction(sb2)
            user_set(
                UserElement(
                    company_id = c1.id_str(),
                    company_longname = "c1",
                    company_shortname = "c1",
                    current_auction_id = de.id_str(),
                    email = "",
                    has_connection_problem = false,
                    id = b1.id_str(),
                    isAuctioneer = false,
                    isObserver = false,
                    isOnline = true,
                    isTester = false,
                    password = "1",
                    phone = "",
                    role = AuUserRole.TRADER,
                    socket_state = AuSession.ClientSocketState.OPENED,
                    socket_state_last_closed = null,
                    termination_reason = null,
                    user_id = b1.id_str(),
                    username = "b1",
                )
            )
            user_set(
                UserElement(
                    company_id = c2.id_str(),
                    company_longname = "c2",
                    company_shortname = "c2",
                    current_auction_id = de.id_str(),
                    email = "",
                    has_connection_problem = false,
                    id = b2.id_str(),
                    isAuctioneer = false,
                    isObserver = false,
                    isOnline = true,
                    isTester = false,
                    password = "1",
                    phone = "",
                    role = AuUserRole.TRADER,
                    socket_state = AuSession.ClientSocketState.OPENED,
                    socket_state_last_closed = null,
                    termination_reason = null,
                    user_id = b2.id_str(),
                    username = "b2",
                )
            )
        }


        b2_store.apply {
            auction_rows_clear()
            de_auction_select(
                DeAuctionValue(
                    auction_id = de.id_str(),
                    auctioneer_info = null,
                    auctioneer_status = null,
                    award_value = null,
                    blotter = DeBlotter(
                        rounds = emptyList(), traders = emptyList(), round_traders = emptyList()
                    ),
                    matrix_last_round = null,
                    messages = emptyList(),
                    common_status = DeCommonStatusValue(
                        isClosed = false,
                        price_direction = null,
                        price_has_reversed = false,
                        round_number = 1,
                        round_price = "100.000",
                        round_seconds = 0,
                        starting_time_text = "Auction started",
                        starting_price_announced = true,
                        common_state = DeCommonState.ROUND_OPEN,
                        common_state_text = "Round 1 open for orders!"
                    ),
                    notice = "test2",
                    settings = settings,
                    trader_history_rows = listOf(
                        DeTraderHistoryRowElement(
                            id = "ROUND.1",
                            auction_id = de.id_str(),
                            bid_constraints = DeBidConstraints(
                                max_buy_volume = 50,
                                min_buy_volume = 0,
                                min_sell_volume = 0,
                                max_sell_volume = 50
                            ),
                            company_id = c2.id_str(),
                            excess_direction = "",
                            excess_level = "",
                            order_submission_type = OrderSubmissionType.DEFAULT,
                            order_submitted_by = "(default)",
                            order_volume_type = OrderVolumeType.NONE,
                            price_direction = null,
                            price_has_reversed = false,
                            price_suffix = "none",
                            round_number = "1",
                            round_price = "100.000",
                            value = "\$0.00",
                            volume = "0"
                        )
                    ),
                    trader_info = DeTraderInfoValue(
                        auction_id = de.id_str(),
                        award_direction = "TODO",
                        award_line = null,
                        awarded_price = "---",
                        awarded_round_number = "---",
                        awarded_value = "---",
                        awarded_volume = "---",
                        bid_constraints = DeBidConstraints(
                            max_buy_volume = 50,
                            min_buy_volume = 0,
                            min_sell_volume = 0,
                            max_sell_volume = 50
                        ),
                        company_id = c2.id_str(),
                        order_submission_type = OrderSubmissionType.DEFAULT,
                        order_volume_type = OrderVolumeType.NONE,
                        order_volume = 0,
                        price_label = "cpp",
                        round_number = 1,
                        round_price = "100.000",
                        value = "\$0.00",
                        volume_label = "MMlb"
                    ),
                    users_that_have_seen_auction = emptySet()
                )
            )
        }
        expect_stores(true)

        notice_save_command(sa1, de, "test2")
        all_stores().forEach { it.notice_set("test2") }
        expect_stores(true)

        notice_save_command(sa1, de, "")
        all_stores().forEach { it.notice_set("") }
        expect_stores(true)
    }

    fun message_broadcast() {
        message_send_command(sa1, de, "test message to all")
        all_stores().forEach {
            it.message_add(broadcast_message)
        }
        expect_stores(true)
    }

    fun first_round_b1_buys() {
        de_order_submit_command(sb1, de, OrderVolumeType.BUY, 1, "30")
        de_order_submit_command(sb2, de, OrderVolumeType.NONE, 1, "0")

        a1_store.apply {
            listOf(sb1, sb2).forEach {
                user_has_seen_auction(it)
            }
            de_auctioneer_info_set(
                DeAuctioneerInfoValue(
                    pen_round = "---",
                    last_round = 1,
                    pen_buyers = "---",
                    last_buyers = "1",
                    pen_sellers = "---",
                    last_sellers = "0",
                    pen_total_buy = "---",
                    last_total_buy = "30",
                    pen_total_sell = "---",
                    last_total_sell = "0",
                    pen_sell_dec = "",
                    last_sell_dec = "",
                    pen_match = "---",
                    last_match = "0",
                    pen_excess = "---",
                    last_excess = "30",
                    potential = "0"
                )
            )
            de_auctioneer_status_set(
                DeAuctioneerStatusValue(
                    announced = true,
                    auctioneer_state = DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN,
                    auctioneer_state_text = "Round 1 open, all orders in.",
                    autopilot = AutopilotMode.DISENGAGED,
                    awardable = false,
                    controls = mapOf(
                        DeFlowControlType.SET_STARTING_PRICE to false,
                        DeFlowControlType.ANNOUNCE_STARTING_PRICE to false,
                        DeFlowControlType.START_AUCTION to false,
                        DeFlowControlType.CLOSE_ROUND to true,
                        DeFlowControlType.REOPEN_ROUND to false,
                        DeFlowControlType.NEXT_ROUND to false,
                        DeFlowControlType.AWARD_AUCTION to false
                    ),
                    excess_direction = "Buy",
                    excess_level = "+++",
                    price_has_overshot = false,
                    round_open_min_secs = null,
                    starting_price = "100.000",
                    time_state = DeTimeState.AUCTION_HAS_STARTED,
                )
            )
            de_blotter_round_set(
                DeRoundElement(
                    id = "ROUND.1",
                    buy_volume = "30",
                    buyer_count = "1",
                    excess_direction = "Buy",
                    excess_level = "+++",
                    has_reversed = false,
                    match_changed = "0",
                    matched = "0",
                    potential = "0",
                    potential_changed = "0",
                    raw_matched = "0",
                    round_direction = null,
                    round_duration = "not implemented",
                    round_number = 1,
                    round_price = 100.0,
                    round_price_str = "100.000",
                    sell_change = 0,
                    sell_volume = "0",
                    seller_count = "0"
                )
            )
            de_blotter_trader_set(
                DeTraderElement(
                    id = "COMPANY.${c1.id_str()}",
                    has_seen_auction = true,
                    company_id = c1.id_str(),
                    shortname = "c1",
                    rank = 1,
                )
            )
            de_blotter_trader_set(
                DeTraderElement(
                    id = "COMPANY.${c2.id_str()}",
                    has_seen_auction = true,
                    company_id = c2.id_str(),
                    shortname = "c2",
                    rank = 2,
                )
            )

            de_blotter_round_trader_set(
                DeRoundTraderElement(
                    id = "ROUND.1.COMPANY.${c1.id_str()}",
                    changed = false,
                    cid = c1.id_str(),
                    order_submission_type = OrderSubmissionType.MANUAL,
                    order_volume_type = OrderVolumeType.BUY,
                    round = 1,
                    timestamp_formatted = "08:00:00",
                    order_submitted_by = "b1",
                    volume_int = 30,
                    volume_str = "30"
                )
            )

            de_blotter_round_trader_set(
                DeRoundTraderElement(
                    id = "ROUND.1.COMPANY.${c2.id_str()}",
                    changed = false,
                    cid = c2.id_str(),
                    order_submission_type = OrderSubmissionType.MANUAL,
                    order_volume_type = OrderVolumeType.NONE,
                    round = 1,
                    timestamp_formatted = "08:00:00",
                    order_submitted_by = "b2",
                    volume_int = 0,
                    volume_str = "0"
                )
            )

            de_award_value_set(
                DeAwardValue(
                    round_results = listOf(
                        DeRoundResultVM(
                            round_number = 1,
                            round_price = "100.000",
                            buy_total = "30",
                            sell_total = "0",
                            match_total = "0",
                            trader_flows = listOf(
                                DeTraderFlowVM(
                                    company_shortname = "c1",
                                    company_id = c1.id_str(),
                                    volume_type = OrderVolumeType.BUY,
                                    volume = "0"
                                ), DeTraderFlowVM(
                                    company_shortname = "c2",
                                    company_id = c2.id_str(),
                                    volume_type = OrderVolumeType.NONE,
                                    volume = "0"
                                )
                            ),
                            matches = listOf(
                                DeScenarioMatchVM(
                                    round_number = 1,
                                    buyer_id = c2.id_str(),
                                    buyer_shortname = "c2",
                                    seller_id = c1.id_str(),
                                    seller_shortname = "c1",
                                    actual_match = 0,
                                    actual_match_str = "0"
                                ),
                                DeScenarioMatchVM(
                                    round_number = 1,
                                    buyer_id = c1.id_str(),
                                    buyer_shortname = "c1",
                                    seller_id = c2.id_str(),
                                    seller_shortname = "c2",
                                    actual_match = 0,
                                    actual_match_str = "0"
                                )
                            )
                        )
                    )
                )
            )

            de_matrix_round_set(
                DeMatrixRoundElement(
                    id = "Round.1", round_number = 1, nodes = listOf(
                        DeMatrixNodeElement(
                            id = "R_1_T_${c1.id_str()}",
                            buy_match = 0,
                            buy_max = 51,
                            buy_min = 0,
                            buy_vol = 30,
                            cid = c1.id_str(),
                            round = 1,
                            sell_match = 0,
                            sell_max = 52,
                            sell_min = 0,
                            sell_vol = 0,
                            shortname = "c1"
                        ), DeMatrixNodeElement(
                            id = "R_1_T_${c2.id_str()}",
                            buy_match = 0,
                            buy_max = 50,
                            buy_min = 0,
                            buy_vol = 0,
                            cid = c2.id_str(),
                            round = 1,
                            sell_match = 0,
                            sell_max = 50,
                            sell_min = 0,
                            sell_vol = 0,
                            shortname = "c2"
                        )
                    ), edges = listOf(
                        DeMatrixEdgeElement(
                            id = DeMatrixEdgeElement.to_id(
                                round_num = 1,
                                b_cid = c2.id_str(),
                                s_cid = c1.id_str(),
                            ),
                            r = 1,
                            buyer_cid = c2.id_str(),
                            buyer_shortname = "c2",
                            seller_cid = c1.id_str(),
                            seller_shortname = "c1",
                            match = 0,
                            capacity = 50,

                            //     credit = 1_000_000.0,
                            credit_str = "no limit"
                        ),
                        DeMatrixEdgeElement(
                            id = DeMatrixEdgeElement.to_id(
                                round_num = 1,
                                b_cid = c1.id_str(),
                                s_cid = c2.id_str(),
                            ),
                            r = 1,
                            buyer_cid = c1.id_str(),
                            buyer_shortname = "c1",
                            seller_cid = c2.id_str(),
                            seller_shortname = "c2",
                            match = 0,
                            capacity = 20,
                            //   credit = null,
                            credit_str = "\$20,000,000.00"
                        )
                    )
                )
            )
        }

        b1_store.apply {
            de_trader_history_row_set(
                DeTraderHistoryRowElement(
                    id = "ROUND.1",
                    auction_id = de.id_str(),
                    bid_constraints = DeBidConstraints(
                        max_buy_volume = 51, min_buy_volume = 0, min_sell_volume = 0, max_sell_volume = 52
                    ),
                    company_id = c1.id_str(),
                    excess_direction = "",
                    excess_level = "",
                    order_submitted_by = "b1",
                    order_submission_type = OrderSubmissionType.MANUAL,
                    order_volume_type = OrderVolumeType.BUY,
                    price_direction = null,
                    price_has_reversed = false,
                    price_suffix = "or lower",
                    round_number = "1",
                    round_price = "100.000",
                    value = "\$30,000,000.00",
                    volume = "30"
                )
            )
            de_trader_info_set(
                DeTraderInfoValue(
                    auction_id = de.id_str(),
                    award_direction = "TODO",
                    award_line = null,
                    awarded_price = "---",
                    awarded_round_number = "---",
                    awarded_value = "---",
                    awarded_volume = "---",
                    bid_constraints = DeBidConstraints(
                        max_buy_volume = 51, min_buy_volume = 0, min_sell_volume = 0, max_sell_volume = 52
                    ),
                    company_id = c1.id_str(),
                    order_submission_type = OrderSubmissionType.MANUAL,
                    order_volume_type = OrderVolumeType.BUY,
                    order_volume = 30,
                    price_label = "cpp",
                    round_number = 1,
                    round_price = "100.000",
                    value = "\$30,000,000.00",
                    volume_label = "MMlb"
                )
            )
        }

        b2_store.apply {
            auction_rows_clear()
            de_trader_history_row_set(
                DeTraderHistoryRowElement(
                    id = "ROUND.1",
                    auction_id = de.id_str(),
                    bid_constraints = DeBidConstraints(
                        max_buy_volume = 50, min_buy_volume = 0, min_sell_volume = 0, max_sell_volume = 50
                    ),
                    company_id = c2.id_str(),
                    excess_direction = "",
                    excess_level = "",
                    order_submitted_by = "b2",
                    order_submission_type = OrderSubmissionType.MANUAL,
                    order_volume_type = OrderVolumeType.NONE,
                    price_direction = null,
                    price_has_reversed = false,
                    price_suffix = "none",
                    round_number = "1",
                    round_price = "100.000",
                    value = "\$0.00",
                    volume = "0"
                )
            )
            de_trader_info_set(
                DeTraderInfoValue(
                    auction_id = de.id_str(),
                    award_direction = "TODO",
                    award_line = null,
                    awarded_price = "---",
                    awarded_round_number = "---",
                    awarded_value = "---",
                    awarded_volume = "---",
                    bid_constraints = DeBidConstraints(
                        max_buy_volume = 50, min_buy_volume = 0, min_sell_volume = 0, max_sell_volume = 50
                    ),
                    company_id = c2.id_str(),
                    order_submission_type = OrderSubmissionType.MANUAL,
                    order_volume_type = OrderVolumeType.NONE,
                    order_volume = 0,
                    price_label = "cpp",
                    round_number = 1,
                    round_price = "100.000",
                    value = "\$0.00",
                    volume_label = "MMlb"
                )
            )
        }

        MessageElement(
            id = "AuctionMessage." + fixed_time.toDate().time,
            from = "System to c1",
            message = "b1 submitted buy order for 30 MMlb",
            message_type = AuMessageType.SYSTEM_TO_TRADER,
            message_type_label = "SYSTEM_TO_TRADER",
            timestamp = fixed_time.toDate().time,
            timestamp_label = "Dec 13, 08:00:00"
        ).also {
            a1_store.message_add(it)
            b1_store.message_add(it)
        }

        MessageElement(
            id = "AuctionMessage." + fixed_time.toDate().time,
            from = "System to c2",
            message = "b2 submitted order for 0 MMlb",
            message_type = AuMessageType.SYSTEM_TO_TRADER,
            message_type_label = "SYSTEM_TO_TRADER",
            timestamp = fixed_time.toDate().time,
            timestamp_label = "Dec 13, 08:00:00"
        ).also {
            a1_store.message_add(it)
            b2_store.message_add(it)
        }

        expect_stores(true)
    }

    fun close_round_1() {
        de_flow_control_command(sa1, de, DeFlowControlType.CLOSE_ROUND)

        a1_store.apply {
            de_auctioneer_state_set(
                DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE,
                "Round 1 closed, auction not awardable."
            )
            de_flow_controls_set(
                mapOf(
                    DeFlowControlType.SET_STARTING_PRICE to false,
                    DeFlowControlType.ANNOUNCE_STARTING_PRICE to false,
                    DeFlowControlType.START_AUCTION to false,
                    DeFlowControlType.CLOSE_ROUND to false,
                    DeFlowControlType.REOPEN_ROUND to true,
                    DeFlowControlType.NEXT_ROUND to true,
                    DeFlowControlType.AWARD_AUCTION to false
                )
            )
            de_common_state_set(DeCommonState.ROUND_CLOSED, "Round 1 closed")
        }

        listOf(b1_store, b2_store).forEach {
            it.de_common_state_set(DeCommonState.ROUND_CLOSED, "Round 1 closed")
        }

        expect_stores(true)
    }

    fun next_round() {
        de_flow_control_command(sa1, de, DeFlowControlType.NEXT_ROUND)

        a1_store.apply {
            de_auctioneer_info_set(
                DeAuctioneerInfoValue(
                    pen_round = "1",
                    last_round = 2,
                    pen_buyers = "1",
                    last_buyers = "0",
                    pen_sellers = "0",
                    last_sellers = "0",
                    pen_total_buy = "30",
                    last_total_buy = "0",
                    pen_total_sell = "0",
                    last_total_sell = "0",
                    pen_sell_dec = "",
                    last_sell_dec = "",
                    pen_match = "0",
                    last_match = "0",
                    pen_excess = "30",
                    last_excess = "0",
                    potential = "0"
                )
            )
            de_auctioneer_status_set(
                DeAuctioneerStatusValue(
                    announced = true,
                    auctioneer_state = DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN,
                    auctioneer_state_text = "Round 2 open, all orders not in.",
                    autopilot = AutopilotMode.DISENGAGED,
                    awardable = false,
                    controls = mapOf(
                        DeFlowControlType.SET_STARTING_PRICE to false,
                        DeFlowControlType.ANNOUNCE_STARTING_PRICE to false,
                        DeFlowControlType.START_AUCTION to false,
                        DeFlowControlType.CLOSE_ROUND to true,
                        DeFlowControlType.REOPEN_ROUND to false,
                        DeFlowControlType.NEXT_ROUND to false,
                        DeFlowControlType.AWARD_AUCTION to false
                    ),
                    excess_direction = "none",
                    excess_level = "0",
                    price_has_overshot = false,
                    round_open_min_secs = null,
                    starting_price = "100.000",
                    time_state = DeTimeState.AUCTION_HAS_STARTED,
                )
            )
            de_award_value_set(
                DeAwardValue(
                    round_results = listOf(
                        DeRoundResultVM(
                            round_number = 2,
                            round_price = "100.500",
                            buy_total = "0",
                            sell_total = "0",
                            match_total = "0",
                            trader_flows = listOf(
                                DeTraderFlowVM(
                                    company_shortname = "c1",
                                    company_id = c1.id_str(),
                                    OrderVolumeType.NONE,
                                    volume = "0"
                                ), DeTraderFlowVM(
                                    company_shortname = "c2",
                                    company_id = c2.id_str(),
                                    OrderVolumeType.NONE,
                                    volume = "0"
                                )
                            ),
                            matches = listOf(
                                DeScenarioMatchVM(
                                    round_number = 2,
                                    buyer_id = c2.id_str(),
                                    buyer_shortname = "c2",
                                    seller_id = c1.id_str(),
                                    seller_shortname = "c1",
                                    actual_match = 0,
                                    actual_match_str = "0"
                                ),
                                DeScenarioMatchVM(
                                    round_number = 2,
                                    buyer_id = c1.id_str(),
                                    buyer_shortname = "c1",
                                    seller_id = c2.id_str(),
                                    seller_shortname = "c2",
                                    actual_match = 0,
                                    actual_match_str = "0"
                                )
                            )
                        ),
                        DeRoundResultVM(
                            round_number = 1,
                            round_price = "100.000",
                            buy_total = "30",
                            sell_total = "0",
                            match_total = "0",
                            trader_flows = listOf(
                                DeTraderFlowVM(
                                    company_shortname = "c1",
                                    company_id = c1.id_str(),
                                    OrderVolumeType.BUY,
                                    volume = "0"
                                ), DeTraderFlowVM(
                                    company_shortname = "c2",
                                    company_id = c2.id_str(),
                                    OrderVolumeType.NONE,
                                    volume = "0"
                                )
                            ),
                            matches = listOf(
                                DeScenarioMatchVM(
                                    round_number = 1,
                                    buyer_id = c2.id_str(),
                                    buyer_shortname = "c2",
                                    seller_id = c1.id_str(),
                                    seller_shortname = "c1",
                                    actual_match = 0,
                                    actual_match_str = "0"
                                ),
                                DeScenarioMatchVM(
                                    round_number = 1,
                                    buyer_id = c1.id_str(),
                                    buyer_shortname = "c1",
                                    seller_id = c2.id_str(),
                                    seller_shortname = "c2",
                                    actual_match = 0,
                                    actual_match_str = "0"
                                )
                            )
                        )
                    )
                )
            )
            de_blotter_round_set(
                DeRoundElement(
                    id = "ROUND.2",
                    buy_volume = "0",
                    buyer_count = "0",
                    excess_direction = "none",
                    excess_level = "0",
                    has_reversed = false,
                    match_changed = "0",
                    matched = "0",
                    potential = "0",
                    potential_changed = "0",
                    raw_matched = "0",
                    round_direction = PriceDirection.UP,
                    round_duration = "not implemented",
                    round_number = 2,
                    round_price = 100.5,
                    round_price_str = "100.500",
                    sell_change = 0,
                    sell_volume = "0",
                    seller_count = "0"
                )
            )
            de_blotter_round_trader_set(
                DeRoundTraderElement(
                    id = "ROUND.2.COMPANY.${c1.id_str()}",
                    changed = false,
                    cid = c1.id_str(),
                    order_submitted_by = "default",
                    order_submission_type = OrderSubmissionType.DEFAULT,
                    order_volume_type = OrderVolumeType.NONE,
                    round = 2,
                    timestamp_formatted = "08:00:00",
                    volume_int = 0,
                    volume_str = "0"
                )
            )

            de_blotter_round_trader_set(
                DeRoundTraderElement(
                    id = "ROUND.2.COMPANY.${c2.id_str()}",
                    changed = false,
                    cid = c2.id_str(),
                    order_submitted_by = "default",
                    order_submission_type = OrderSubmissionType.DEFAULT,
                    order_volume_type = OrderVolumeType.NONE,
                    round = 2,
                    timestamp_formatted = "08:00:00",
                    volume_int = 0,
                    volume_str = "0"
                )
            )

            de_matrix_round_set(
                DeMatrixRoundElement(
                    id = "Round.2",
                    round_number = 2,
                    nodes = listOf(
                        DeMatrixNodeElement(
                            id = "R_2_T_${c1.id_str()}",
                            buy_match = 0,
                            buy_max = 30,
                            buy_min = 0,
                            buy_vol = 0,
                            cid = c1.id_str(),
                            round = 2,
                            sell_match = 0,
                            sell_max = 52,
                            sell_min = 0,
                            sell_vol = 0,
                            shortname = c1.shortname
                        ),
                        DeMatrixNodeElement(
                            id = "R_2_T_${c2.id_str()}",
                            buy_match = 0,
                            buy_max = 0,
                            buy_min = 0,
                            buy_vol = 0,
                            cid = c2.id_str(),
                            round = 2,
                            sell_match = 0,
                            sell_max = 50,
                            sell_min = 0,
                            sell_vol = 0,
                            shortname = c2.shortname
                        )
                    ),
                    edges = listOf(
                        DeMatrixEdgeElement(
                            id = DeMatrixEdgeElement.to_id(
                                round_num = 2,
                                b_cid = c2.id_str(),
                                s_cid = c1.id_str(),
                            ),
                            r = 2,
                            buyer_cid = c2.id_str(),
                            buyer_shortname = "c2",
                            seller_cid = c1.id_str(),
                            seller_shortname = "c1",
                            match = 0,
                            capacity = 0,
                            //     credit = 1_000_000.0,
                            credit_str = "no limit"
                        ),
                        DeMatrixEdgeElement(
                            id = DeMatrixEdgeElement.to_id(
                                round_num = 2,
                                b_cid = c1.id_str(),
                                s_cid = c2.id_str(),
                            ),
                            r = 2,
                            buyer_cid = c1.id_str(),
                            buyer_shortname = "c1",
                            seller_cid = c2.id_str(),
                            seller_shortname = "c2",
                            match = 0,
                            capacity = 19,
                            //   credit = null,
                            credit_str = "\$20,000,000.00"
                        ),
                    )
                )
            )

        }
        listOf(a1_store, b1_store, b2_store).forEach {
            it.de_common_status_set(
                DeCommonStatusValue(
                    isClosed = false,
                    price_direction = PriceDirection.UP,
                    price_has_reversed = false,
                    round_number = 2,
                    round_price = "100.500",
                    round_seconds = 0,
                    starting_price_announced = true,
                    starting_time_text = "Auction started",
                    common_state = DeCommonState.ROUND_OPEN,
                    common_state_text = "Round 2 open for orders!"
                )
            )
        }

        b1_store.apply {
            de_trader_history_row_excess_set(round_number = 1, "Buy", "+++")
            de_trader_history_row_set(
                DeTraderHistoryRowElement(
                    id = "ROUND.2",
                    auction_id = de.id_str(),
                    bid_constraints = DeBidConstraints(
                        max_buy_volume = 30,
                        min_buy_volume = 0,
                        min_sell_volume = 0,
                        max_sell_volume = 52
                    ),
                    company_id = c1.id_str(),
                    excess_direction = "",
                    excess_level = "",
                    order_submitted_by = "(default)",
                    order_submission_type = OrderSubmissionType.DEFAULT,
                    order_volume_type = OrderVolumeType.NONE,
                    price_direction = PriceDirection.UP,
                    price_has_reversed = false,
                    price_suffix = "none",
                    round_number = "2",
                    round_price = "100.500",
                    value = "\$0.00",
                    volume = "0"
                )
            )
            de_trader_info_set(
                DeTraderInfoValue(
                    auction_id = de.id_str(),
                    award_direction = "TODO",
                    award_line = null,
                    awarded_price = "---",
                    awarded_round_number = "---",
                    awarded_value = "---",
                    awarded_volume = "---",
                    bid_constraints = DeBidConstraints(
                        max_buy_volume = 30,
                        min_buy_volume = 0,
                        min_sell_volume = 0,
                        max_sell_volume = 52
                    ),
                    company_id = c1.id_str(),
                    order_submission_type = OrderSubmissionType.DEFAULT,
                    order_volume_type = OrderVolumeType.NONE,
                    order_volume = 0,
                    price_label = "cpp",
                    round_number = 2,
                    round_price = "100.500",
                    value = "\$0.00",
                    volume_label = "MMlb"
                )
            )
        }

        b2_store.apply {
            //auction_rows_clear()
            de_trader_history_row_excess_set(round_number = 1, "Buy", "+++")
            de_trader_history_row_set(
                DeTraderHistoryRowElement(
                    id = "ROUND.2",
                    auction_id = de.id_str(),
                    bid_constraints = DeBidConstraints(
                        max_buy_volume = 0,
                        min_buy_volume = 0,
                        min_sell_volume = 0,
                        max_sell_volume = 50
                    ),
                    company_id = c2.id_str(),
                    excess_direction = "",
                    excess_level = "",
                    order_submitted_by = "(default)",
                    order_submission_type = OrderSubmissionType.DEFAULT,
                    order_volume_type = OrderVolumeType.NONE,
                    price_direction = PriceDirection.UP,
                    price_has_reversed = false,
                    price_suffix = "none",
                    round_number = "2",
                    round_price = "100.500",
                    value = "\$0.00",
                    volume = "0"
                )
            )
            de_trader_info_set(
                DeTraderInfoValue(
                    auction_id = de.id_str(),
                    award_direction = "TODO",
                    award_line = null,
                    awarded_price = "---",
                    awarded_round_number = "---",
                    awarded_value = "---",
                    awarded_volume = "---",
                    bid_constraints = DeBidConstraints(
                        max_buy_volume = 0,
                        min_buy_volume = 0,
                        min_sell_volume = 0,
                        max_sell_volume = 50
                    ),
                    company_id = c2.id_str(),
                    order_submission_type = OrderSubmissionType.DEFAULT,
                    order_volume_type = OrderVolumeType.NONE,
                    order_volume = 0,
                    price_label = "cpp",
                    round_number = 2,
                    round_price = "100.500",
                    value = "\$0.00",
                    volume_label = "MMlb"
                )
            )
        }

        expect_stores(true)
    }

    fun second_round_b1_same_b2_sells() {

        shouldThrow<AlertException> {
            de_order_submit_command(sb2, de, OrderVolumeType.SELL, 1, "30")
        }.message.shouldBe("Order isn't for round 2")

        de_order_submit_command(sb1, de, OrderVolumeType.BUY, 2, "30")
        de_order_submit_command(sb2, de, OrderVolumeType.SELL, 2, "30")

        a1_store.apply {
            de_auctioneer_info_set(
                DeAuctioneerInfoValue(
                    pen_round = "1",
                    last_round = 2,
                    pen_buyers = "1",
                    last_buyers = "1",
                    pen_sellers = "0",
                    last_sellers = "1",
                    pen_total_buy = "30",
                    last_total_buy = "30",
                    pen_total_sell = "0",
                    last_total_sell = "30",
                    pen_sell_dec = "",
                    last_sell_dec = "",
                    pen_match = "0",
                    last_match = "19",
                    pen_excess = "30",
                    last_excess = "0",
                    potential = "0"
                )
            )
            de_auctioneer_status_set(
                DeAuctioneerStatusValue(
                    announced = true,
                    auctioneer_state = DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN,
                    auctioneer_state_text = "Round 2 open, all orders in.",
                    autopilot = AutopilotMode.DISENGAGED,
                    awardable = false,
                    controls = mapOf(
                        DeFlowControlType.SET_STARTING_PRICE to false,
                        DeFlowControlType.ANNOUNCE_STARTING_PRICE to false,
                        DeFlowControlType.START_AUCTION to false,
                        DeFlowControlType.CLOSE_ROUND to true,
                        DeFlowControlType.REOPEN_ROUND to false,
                        DeFlowControlType.NEXT_ROUND to false,
                        DeFlowControlType.AWARD_AUCTION to false
                    ),
                    excess_direction = "none",
                    excess_level = "0",
                    price_has_overshot = false,
                    round_open_min_secs = null,
                    starting_price = "100.000",
                    time_state = DeTimeState.AUCTION_HAS_STARTED,
                )
            )
            de_award_value_set(
                DeAwardValue(
                    round_results = listOf(
                        DeRoundResultVM(
                            round_number = 2,
                            round_price = "100.500",
                            buy_total = "30",
                            sell_total = "30",
                            match_total = "19",
                            trader_flows = listOf(
                                DeTraderFlowVM(
                                    company_shortname = "c1",
                                    company_id = c1.id_str(),
                                    OrderVolumeType.BUY,
                                    volume = "19"
                                ), DeTraderFlowVM(
                                    company_shortname = "c2",
                                    company_id = c2.id_str(),
                                    OrderVolumeType.SELL,
                                    volume = "19"
                                )
                            ),
                            matches = listOf(
                                DeScenarioMatchVM(
                                    round_number = 2,
                                    buyer_id = c2.id_str(),
                                    buyer_shortname = "c2",
                                    seller_id = c1.id_str(),
                                    seller_shortname = "c1",
                                    actual_match = 0,
                                    actual_match_str = "0"
                                ),
                                DeScenarioMatchVM(
                                    round_number = 2,
                                    buyer_id = c1.id_str(),
                                    buyer_shortname = "c1",
                                    seller_id = c2.id_str(),
                                    seller_shortname = "c2",
                                    actual_match = 19,
                                    actual_match_str = "19"
                                )
                            )
                        ),
                        DeRoundResultVM(
                            round_number = 1,
                            round_price = "100.000",
                            buy_total = "30",
                            sell_total = "0",
                            match_total = "0",
                            trader_flows = listOf(
                                DeTraderFlowVM(
                                    company_shortname = "c2",
                                    company_id = c2.id_str(),
                                    OrderVolumeType.NONE,
                                    volume = "0"
                                ),
                                DeTraderFlowVM(
                                    company_shortname = "c1",
                                    company_id = c1.id_str(),
                                    OrderVolumeType.BUY,
                                    volume = "0"
                                )
                            ),
                            matches = listOf(
                                DeScenarioMatchVM(
                                    round_number = 1,
                                    buyer_id = c2.id_str(),
                                    buyer_shortname = "c2",
                                    seller_id = c1.id_str(),
                                    seller_shortname = "c1",
                                    actual_match = 0,
                                    actual_match_str = "0"
                                ),
                                DeScenarioMatchVM(
                                    round_number = 1,
                                    buyer_id = c1.id_str(),
                                    buyer_shortname = "c1",
                                    seller_id = c2.id_str(),
                                    seller_shortname = "c2",
                                    actual_match = 0,
                                    actual_match_str = "0"
                                )
                            )
                        )
                    )
                )
            )
            de_blotter_round_set(
                DeRoundElement(
                    id = "ROUND.2",
                    buy_volume = "30",
                    buyer_count = "1",
                    excess_direction = "none",
                    excess_level = "0",
                    has_reversed = false,
                    match_changed = "19",
                    matched = "19",
                    potential = "0",
                    potential_changed = "0",
                    raw_matched = "19",
                    round_direction = PriceDirection.UP,
                    round_duration = "not implemented",
                    round_number = 2,
                    round_price = 100.5,
                    round_price_str = "100.500",
                    sell_change = 30,
                    sell_volume = "30",
                    seller_count = "1"
                )
            )
            de_blotter_round_trader_set(
                DeRoundTraderElement(
                    id = "ROUND.2.COMPANY.${c1.id_str()}",
                    changed = false,
                    cid = c1.id_str(),
                    order_submitted_by = "b1",
                    order_submission_type = OrderSubmissionType.MANUAL,
                    order_volume_type = OrderVolumeType.BUY,
                    round = 2,
                    timestamp_formatted = "08:00:00",
                    volume_int = 30,
                    volume_str = "30"
                )
            )

            de_blotter_round_trader_set(
                DeRoundTraderElement(
                    id = "ROUND.2.COMPANY.${c2.id_str()}",
                    changed = false,
                    cid = c2.id_str(),
                    order_submitted_by = "b2",
                    order_submission_type = OrderSubmissionType.MANUAL,
                    order_volume_type = OrderVolumeType.SELL,
                    round = 2,
                    timestamp_formatted = "08:00:00",
                    volume_int = 30,
                    volume_str = "30"
                )
            )


            de_matrix_round_set(
                DeMatrixRoundElement(
                    id = "Round.2",
                    round_number = 2,
                    nodes = listOf(
                        DeMatrixNodeElement(
                            id = "R_2_T_${c2.id_str()}",
                            buy_match = 0,
                            buy_max = 0,
                            buy_min = 0,
                            buy_vol = 0,
                            cid = c2.id_str(),
                            round = 2,
                            sell_match = 19,
                            sell_max = 50,
                            sell_min = 0,
                            sell_vol = 30,
                            shortname = c2.shortname
                        ),
                        DeMatrixNodeElement(
                            id = "R_2_T_${c1.id_str()}",
                            buy_match = 19,
                            buy_max = 30,
                            buy_min = 0,
                            buy_vol = 30,
                            cid = c1.id_str(),
                            round = 2,
                            sell_match = 0,
                            sell_max = 52,
                            sell_min = 0,
                            sell_vol = 0,
                            shortname = c1.shortname
                        )
                    ),
                    edges = listOf(
                        DeMatrixEdgeElement(
                            id = DeMatrixEdgeElement.to_id(
                                round_num = 2,
                                b_cid = c2.id_str(),
                                s_cid = c1.id_str(),
                            ),
                            r = 2,
                            buyer_cid = c2.id_str(),
                            buyer_shortname = "c2",
                            seller_cid = c1.id_str(),
                            seller_shortname = "c1",
                            match = 0,
                            capacity = 0,
                            //     credit = 1_000_000.0,
                            credit_str = "no limit"
                        ),
                        DeMatrixEdgeElement(
                            id = DeMatrixEdgeElement.to_id(
                                round_num = 2,
                                b_cid = c1.id_str(),
                                s_cid = c2.id_str(),
                            ),
                            r = 2,
                            buyer_cid = c1.id_str(),
                            buyer_shortname = "c1",
                            seller_cid = c2.id_str(),
                            seller_shortname = "c2",
                            match = 19,
                            capacity = 19,
                            //   credit = null,
                            credit_str = "\$20,000,000.00"
                        )
                    )
                )
            )
        }

        MessageElement(
            id = "AuctionMessage." + fixed_time.toDate().time,
            from = "System to c1",
            message = "b1 submitted buy order for 30 MMlb",
            message_type = AuMessageType.SYSTEM_TO_TRADER,
            message_type_label = "SYSTEM_TO_TRADER",
            timestamp = fixed_time.toDate().time,
            timestamp_label = "Dec 13, 08:00:00"
        ).also {
            a1_store.message_add(it)
            b1_store.message_add(it)
        }

        MessageElement(
            id = "AuctionMessage." + fixed_time.toDate().time,
            from = "System to c2",
            message = "b2 submitted sell order for 30 MMlb",
            message_type = AuMessageType.SYSTEM_TO_TRADER,
            message_type_label = "SYSTEM_TO_TRADER",
            timestamp = fixed_time.toDate().time,
            timestamp_label = "Dec 13, 08:00:00"
        ).also {
            a1_store.message_add(it)
            b2_store.message_add(it)
        }

        b1_store.apply {
            de_trader_history_row_set(
                DeTraderHistoryRowElement(
                    id = "ROUND.2",
                    auction_id = de.id_str(),
                    bid_constraints = DeBidConstraints(
                        max_buy_volume = 30,
                        min_buy_volume = 0,
                        min_sell_volume = 0,
                        max_sell_volume = 52
                    ),
                    company_id = c1.id_str(),
                    excess_direction = "",
                    excess_level = "",
                    order_submitted_by = "b1",
                    order_submission_type = OrderSubmissionType.MANUAL,
                    order_volume_type = OrderVolumeType.BUY,
                    price_direction = PriceDirection.UP,
                    price_has_reversed = false,
                    price_suffix = "or lower",
                    round_number = "2",
                    round_price = "100.500",
                    value = "\$30,150,000.00",
                    volume = "30"
                )
            )
            de_trader_info_set(
                DeTraderInfoValue(
                    auction_id = de.id_str(),
                    award_direction = "TODO",
                    award_line = null,
                    awarded_price = "---",
                    awarded_round_number = "---",
                    awarded_value = "---",
                    awarded_volume = "---",
                    bid_constraints = DeBidConstraints(
                        max_buy_volume = 30,
                        min_buy_volume = 0,
                        min_sell_volume = 0,
                        max_sell_volume = 52
                    ),
                    company_id = c1.id_str(),
                    order_submission_type = OrderSubmissionType.MANUAL,
                    order_volume_type = OrderVolumeType.BUY,
                    order_volume = 30,
                    price_label = "cpp",
                    round_number = 2,
                    round_price = "100.500",
                    value = "\$30,150,000.00",
                    volume_label = "MMlb"
                )
            )
        }

        b2_store.apply {
            de_trader_history_row_set(
                DeTraderHistoryRowElement(
                    id = "ROUND.2",
                    auction_id = de.id_str(),
                    bid_constraints = DeBidConstraints(
                        max_buy_volume = 0,
                        min_buy_volume = 0,
                        min_sell_volume = 0,
                        max_sell_volume = 50
                    ),
                    company_id = c2.id_str(),
                    excess_direction = "",
                    excess_level = "",
                    order_submitted_by = "b2",
                    order_submission_type = OrderSubmissionType.MANUAL,
                    order_volume_type = OrderVolumeType.SELL,
                    price_direction = PriceDirection.UP,
                    price_has_reversed = false,
                    price_suffix = "or higher",
                    round_number = "2",
                    round_price = "100.500",
                    value = "\$30,150,000.00",
                    volume = "30"
                )
            )
            de_trader_info_set(
                DeTraderInfoValue(
                    auction_id = de.id_str(),
                    award_direction = "TODO",
                    award_line = null,
                    awarded_price = "---",
                    awarded_round_number = "---",
                    awarded_value = "---",
                    awarded_volume = "---",
                    bid_constraints = DeBidConstraints(
                        max_buy_volume = 0,
                        min_buy_volume = 0,
                        min_sell_volume = 0,
                        max_sell_volume = 50
                    ),
                    company_id = c2.id_str(),
                    order_submission_type = OrderSubmissionType.MANUAL,
                    order_volume_type = OrderVolumeType.SELL,
                    order_volume = 30,
                    price_label = "cpp",
                    round_number = 2,
                    round_price = "100.500",
                    value = "\$30,150,000.00",
                    volume_label = "MMlb"
                )
            )
        }

        expect_stores(true)
    }

    fun close_round_2() {
        de_flow_control_command(sa1, de, DeFlowControlType.CLOSE_ROUND)
        a1_store.apply {
            de_auctioneer_state_set(
                DeAuctioneerState.ROUND_CLOSED_AWARDABLE,
                "Round 2 closed, auction awardable."
            )
            de_flow_controls_set(
                mapOf(
                    DeFlowControlType.SET_STARTING_PRICE to false,
                    DeFlowControlType.ANNOUNCE_STARTING_PRICE to false,
                    DeFlowControlType.START_AUCTION to false,
                    DeFlowControlType.CLOSE_ROUND to false,
                    DeFlowControlType.REOPEN_ROUND to false,
                    DeFlowControlType.NEXT_ROUND to false,
                    DeFlowControlType.AWARD_AUCTION to true
                )
            )
            de_award_value_set(
                DeAwardValue(
                    round_results = listOf(
                        DeRoundResultVM(
                            round_number = 2,
                            round_price = "100.500",
                            buy_total = "30",
                            sell_total = "30",
                            match_total = "19",
                            trader_flows = listOf(
                                DeTraderFlowVM(
                                    company_shortname = "c2",
                                    company_id = c2.id_str(),
                                    OrderVolumeType.SELL,
                                    volume = "19"
                                ),
                                DeTraderFlowVM(
                                    company_shortname = "c1",
                                    company_id = c1.id_str(),
                                    OrderVolumeType.BUY,
                                    volume = "19"
                                ),
                            ),
                            matches = listOf(
                                DeScenarioMatchVM(
                                    round_number = 2,
                                    buyer_id = c2.id_str(),
                                    buyer_shortname = "c2",
                                    seller_id = c1.id_str(),
                                    seller_shortname = "c1",
                                    actual_match = 0,
                                    actual_match_str = "0"
                                ),
                                DeScenarioMatchVM(
                                    round_number = 2,
                                    buyer_id = c1.id_str(),
                                    buyer_shortname = "c1",
                                    seller_id = c2.id_str(),
                                    seller_shortname = "c2",
                                    actual_match = 19,
                                    actual_match_str = "19"
                                )
                            )
                        ),
                        DeRoundResultVM(
                            round_number = 1,
                            round_price = "100.000",
                            buy_total = "30",
                            sell_total = "0",
                            match_total = "0",
                            trader_flows = listOf(
                                DeTraderFlowVM(
                                    company_shortname = "c1",
                                    company_id = c1.id_str(),
                                    OrderVolumeType.BUY,
                                    volume = "0"
                                ), DeTraderFlowVM(
                                    company_shortname = "c2",
                                    company_id = c2.id_str(),
                                    OrderVolumeType.NONE,
                                    volume = "0"
                                )
                            ),
                            matches = listOf(
                                DeScenarioMatchVM(
                                    round_number = 1,
                                    buyer_id = c2.id_str(),
                                    buyer_shortname = "c2",
                                    seller_id = c1.id_str(),
                                    seller_shortname = "c1",
                                    actual_match = 0,
                                    actual_match_str = "0"
                                ),
                                DeScenarioMatchVM(
                                    round_number = 1,
                                    buyer_id = c1.id_str(),
                                    buyer_shortname = "c1",
                                    seller_id = c2.id_str(),
                                    seller_shortname = "c2",
                                    actual_match = 0,
                                    actual_match_str = "0"
                                )
                            )
                        )
                    )
                )
            )
        }

        listOf(a1_store, b1_store, b2_store).forEach {
            it.de_common_state_set(DeCommonState.ROUND_CLOSED, "Round 2 closed")
        }

        expect_stores(true)

    }

    fun award_auction() {
        de_auction_award_command(sa1, de, round_number = 2)
        expect_stores(true)
    }

}







