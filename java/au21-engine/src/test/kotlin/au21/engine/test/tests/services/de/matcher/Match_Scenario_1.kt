package au21.engine.test.tests.services.de.matcher

import au21.engine.domain.common.model.*
import au21.engine.domain.common.services.find_auction_by_name
import au21.engine.domain.common.services.traders
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeFlowControlType
import au21.engine.framework.PageName
import au21.engine.test.helpers.base.command.CommandTestBase
import com.github.shiguruikai.combinatoricskt.permutations
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.quarkus.test.junit.QuarkusTest
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

//@Disabled
@QuarkusTest
//@TestProfile(ObjectdbClientServerProfile::class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
//TODO: Fix this as soon dene with passing pipeline
@Disabled
class Match_Scenario_1 : CommandTestBase() {

    lateinit var a1: Person

    lateinit var b1: Person
    lateinit var b2: Person
    lateinit var b3: Person
//    lateinit var b4: Person

    lateinit var c1: Company
    lateinit var c2: Company
    lateinit var c3: Company
    //   lateinit var c4:Company

    lateinit var de: DeAuction
    lateinit var sa1: AuSession

    lateinit var sb1: AuSession
    lateinit var sb2: AuSession
    lateinit var sb3: AuSession
    //   lateinit var sb4:AuSession

    val auction_name = "Auction 1"

    @Test
    fun run() {
        last_stores().shouldContainExactlyInAnyOrder(emptyList())
        db_init_command()

        sa1 = create_session()

        login_command(sa1, "a1", "1")
        a1 = sa1.user!!

        create_default_de_auction(sa1, auction_name)
        db.find_auction_by_name<DeAuction>(auction_name).let {
            it.shouldNotBeNull()
            de = it
        }

        page_set_command(sa1, PageName.USER_PAGE)

        listOf("c1", "c2", "c3", "c4").forEach {
            company_save_command(sa1, null, it, "$it-long")
        }

        val companies = db.findAll<Company>()
        c1 = companies[0]
        c2 = companies[1]
        c3 = companies[2]
        // c4 = companies[3]

        create_user(sa1, AuUserRole.TRADER, "b1", c1)
        create_user(sa1, AuUserRole.TRADER, "b2", c2)
        create_user(sa1, AuUserRole.TRADER, "b3", c3)
        // create_user(sa1, AuUserRole.TRADER, "b4", c4)

        val trader_users: List<Person> = db.traders()
        b1 = trader_users[0]
        b2 = trader_users[1]
        b3 = trader_users[2]
        // b4 = trader_users[3]

        b1.shouldNotBeNull()
        b2.shouldNotBeNull()
        b3.shouldNotBeNull()
        // b4.shouldNotBeNull()

        listOf(1, 2, 3, 4)
            .map {
                create_session().also { it.shouldNotBeNull() }
            }
            .also {
                sb1 = it[0]
                sb2 = it[1]
                sb3 = it[2]
                //    sb4 = it[3]
            }

        listOf(c1, c2, c3 /* c4 */)
            .permutations(2)
            .forEach { (a, b) ->
                counterparty_credit_set(
                    de,
                    sa1,
                    seller = a,
                    buyer =
                    b, "\$100,000,000")
            }

        mapOf(
            sb1 to "b1",
            sb2 to "b2",
            sb3 to "b3",
            //   sb4 to "b4"
        ).forEach { (s, uname) ->
            login_command(s, uname, "1")
        }

        // TODO: test with non-null auction (after bidders added!)
        auction_select_command(sa1, de)
        de_traders_add_command(sa1, de, listOf(c1, c2, c3 /* c4 */))

        listOf(sb1, sb2, sb3/* sb4 */).forEach {
            auction_select_command(it, de)
        }

        de_flow_control_command(sa1, de, DeFlowControlType.SET_STARTING_PRICE, "100.000")
        de_flow_control_command(sa1, de, DeFlowControlType.ANNOUNCE_STARTING_PRICE)
        de_flow_control_command(sa1, de, DeFlowControlType.START_AUCTION)

        de_order_submit_command(sb1, de, OrderType.BUY, 1, "44")
        de_order_submit_command(sb2, de, OrderType.SELL, 1, "33")
        de_order_submit_command(sb3, de, OrderType.NONE, 1, "0")
        // de_order_submit_command(sb4, de, OrderVolumeType.NONE, 1, "0")

        de_flow_control_command(sa1, de, DeFlowControlType.CLOSE_ROUND)
        de.rounds[0].match_vol().shouldBe(33)

        val a1_store = last_stores().find { it.store.session_user?.username == "a1" }
        println(a1_store)

//        de_flow_control_command(sa1, de, DeFlowControlType.NEXT_ROUND)
//        de_order_submit_command(sb1, de, OrderVolumeType.BUY, 2, "30")
//        de_order_submit_command(sb2, de, OrderVolumeType.SELL, 2, "30")
//        de_flow_control_command(sa1, de, DeFlowControlType.CLOSE_ROUND)
//        de.rounds[1].match_vol().shouldBe(30)

        // de_auction_award_command(sa1, de, round_number = 2)
    }

}


//        @Test
//        fun test() {
//            call_fn(::when_no_bids_are_submitted_match_should_be_zero)
//            call_fn(::when_1_buy_order_is_submitted_match_should_be_zero)
//        }
//
//
//        fun when_no_bids_are_submitted_match_should_be_zero() {
//            db.transact {
//                val n: Round = de.lastround()
//                de.traders.forEach { t: DeAuction.Trader ->
//                    val x: List<Person> = db.company_users(t.company)
//                    val u: Person = db.company_users(t.company).first()
//                    de.create_manual_order(n, t, u, NONE, 0)
//                }
//                n.buy_orders().none { it.volume == 0 }
//                n.sell_orders().none { it.volume == 0 }
//            }
//        }
//
//        fun when_1_buy_order_is_submitted_match_should_be_zero() {
//            val n: Round = de.lastround()
//            n.price.shouldBe(100.0)
//            de.rounds.shouldHaveSize(1)
//            de.traders.shouldHaveSize(trading_people.size)
//
//            val order = de.create_manual_order(
//                r = n,
//                t = de.traders[0],
//                u = trading_people[0],
//                order_volume_type = BUY,
//                order_volume = 10
//            )
//
//            order.volume_type.shouldBe(BUY)
//            // order.volume_type.shouldBe(SELL)
//
//            //     assertThat(order.withdrawal_reason).isNull()
//
//            // NB: MUST be done before calcualting actuan_maxflow !!
//            de.calculate_and_set_matches()
//
//            n.match_vol().shouldBe(0)
//        }
//    }
//}


//    @Test
//    fun should_not_match_due_to_lack_of_credits() {
//        val (de, companies, people) = de_basic(trader_names, 100.0)
//        val n: Round = de.lastround() ?: throw Error("auction has no rounds")
//
//        de.create_manual_order(
//            r = n,
//            t = de.traders[0],
//            u = people[0],
//            order_volume_type = BUY,
//            order_volume = 10
//        )
//
//        de.create_manual_order(
//            r = n,
//            t = de.traders[1],
//            u = people[1],
//            order_volume_type = SELL,
//            order_volume = 20
//        )
//        // NB: MUST be done before calcualting actuan_maxflow !!
//        de.set_counterparty_volume_limits(companies)
//        de.calculate_and_set_matches()
//
//        assertThat(n.match_vol()).isEqualTo(0)
//    }
//
//    @Test
//    fun should_match_2() {
//        val scenario = de_basic(trader_names, 100.0)
//        val de: DeAuction = scenario.first
//        val n: Round = de.lastround() ?: throw Error("auction has no rounds")
//
//        val companies: List<Company> = scenario.second
//        companies.forEach { seller ->
//            companies.forEach { buyer ->
//                if (seller != buyer)
//                    seller.set_credit_limit(buyer, 5_000_000.0)
//            }
//        }
//        de.create_manual_order(
//            r = n,
//            t = de.traders[0],
//            u = scenario.third[0],
//            order_volume_type = BUY,
//            order_volume = 10
//        )
//
//        de.create_manual_order(
//            r = n,
//            t = de.traders[1],
//            u = scenario.third[1],
//            order_volume_type = SELL,
//            order_volume = 20
//        )
//        // NB: MUST be done before calcualting actuan_maxflow !!
//        de.set_counterparty_volume_limits(scenario.second)
//        de.calculate_and_set_matches()
//
//        assertThat(n.match_vol()).isEqualTo(5)
//    }
//    }


