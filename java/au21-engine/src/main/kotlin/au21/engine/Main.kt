// File: Main.kt
package au21.engine

import au21.engine.framework.commands.EngineCommandHandler
import io.quarkus.logging.Log
import io.quarkus.runtime.Quarkus
import io.quarkus.runtime.QuarkusApplication
import io.quarkus.runtime.ShutdownEvent
import io.quarkus.runtime.StartupEvent
import io.quarkus.runtime.annotations.QuarkusMain
import jakarta.enterprise.context.ApplicationScoped
import jakarta.enterprise.event.Observes
import jakarta.inject.Inject
import java.lang.management.ManagementFactory
import javax.persistence.EntityManagerFactory


@ApplicationScoped
class Au21Engine : QuarkusApplication {

//    @Inject
//    lateinit var tracer: AuTracer

    // adding this here to force creation at startup
    // - otherwise will have to send a test request
    //   to check if its working
    // - but note: db won't actually be created until first request!
    // OR: use a singleton ?
    // TODO: need to do a db request, in order to create the db
    @Inject
    lateinit var emf: EntityManagerFactory

    @Inject
    lateinit var handler: EngineCommandHandler

    @Throws(Exception::class)
    override fun run(vararg args: String): Int {
        Log.info("created: $emf") // added to force creating at startup

//        Unirest.get("http://localhost:4040/ENGINE_INPUT_CHANNEL")
//            .queryString(
//                mapOf(
//                    Pair(
//                        "message", EngineCommandEnvelope(
//                            session_id = "",
//                            simplename = CreateSampleDbCommand::class.java.simpleName,
//                            command = CreateSampleDbCommand()
//                        ).to_json()
//                    )
//                )
//            )
//            .asJson()

        Quarkus.waitForExit()
        return 0
    }

    fun onStart(@Observes ev: StartupEvent?) {
        ///  Log.info("The application is starting...")
    }

    fun onStop(@Observes ev: ShutdownEvent?) {
        //   Log.info("The application is stopping...")
        //   MetricManager.shutdown()
    }
}

fun show_memory() {
    // from: https://www.baeldung.com/ops/docker-jvm-heap-size
    val mb = 1024 * 1024
    val memoryBean = ManagementFactory.getMemoryMXBean()
    val xmx = memoryBean.heapMemoryUsage.max / mb
    val xms = memoryBean.heapMemoryUsage.init / mb
    Log.info("Initial Memory (xms) : ${xms}mb, Max Memory (xmx) : ${xmx}mb")
}

@QuarkusMain
class Main {


    companion object {


        @JvmStatic
        fun main(args: Array<String>) {
//            System.setProperty("kamon.apm.api-key","7v711sdsuijkivrlpvqve84sk6")
//            Kamon.init();

            show_memory()

//            TreeMap(System.getProperties())
//                .forEach { (key, value) ->
//                    println("$key=$value")
//                }
            System
                .getProperties()
                .keys
                .map { it.toString() }
                .sortedBy { it }
                .joinToString("\n") { it + ":" + System.getProperty(it) }.also {
                    Log.info(it)
                }

            Quarkus.run(Au21Engine::class.java, *args)
        }

    }

}
