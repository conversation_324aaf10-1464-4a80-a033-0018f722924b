// File: handler.kt
package au21.engine.framework.commands

//import org.eclipse.microprofile.opentracing.Traced
//import io.opentracing.Tracer
//import org.eclipse.microprofile.opentracing.Traced
//import sun.util.logging.PlatformLogger.ConfigurableBridge.LoggerConfiguration
import au21.engine.domain.common.commands.ErrorsSendCommand
import au21.engine.domain.common.model.AuSession.SessionTerminationReason.SERVER_REBOOT
import au21.engine.domain.common.services.sessions_non_terminated
import au21.engine.domain.de.commands.DeOrderSubmitCommand
import au21.engine.framework.client.BrowserMessageKind
import au21.engine.framework.client.ClientCommand
import au21.engine.framework.client.ClientsManager
import au21.engine.framework.client.SocketHandler
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.metrics.AuMetrics
import au21.engine.framework.utils.gzipClientCommand
import au21.engine.framework.utils.jsonToPrettyFormat
import au21.engine.framework.utils.json_to_yaml
import au21.engine.framework.utils.objToPrettyFormat
import au21.engine.framework.utils.prettyYaml
import au21.engine.framework.utils.to_json
import com.jsoniter.JsonIterator
import io.quarkus.logging.Log
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import java.util.concurrent.atomic.AtomicBoolean


/*
 * =====================================================================================================
 *                                              MISC
 *   TODO: add version properties to Results and Messages
 *    version will just be the timestamp that this file was created.
 * =====================================================================================================
 */

// NEED TO FIND A PLACE FOR THESE I GUESS:

//typealias String = String

class CommandParams(
    val session_id: String?,
    val command_json: Any?,
    val simplename: String,
    val classname: String,
)

//@Traced
@ApplicationScoped
class EngineCommandHandler {

    val debug = true // TODO make this a feature switch for dev

    var trace_heartbeat = false

    @Inject
    lateinit var metrics: AuMetrics

//    @Inject
//    lateinit var tracer: Tracer

    // not sure this is needed, first run of YourKit had 500k of these objects!
    val heartbeat_simplename: String = HeartbeatCommand::class.java.simpleName
    val heartbeat_classname: String = HeartbeatCommand::class.java.canonicalName

    @Inject
    lateinit var session_manager: ClientsManager

    @Inject
    lateinit var session_socket: SocketHandler

    @Inject
    lateinit var command_deserializer: EngineCommandDeserializer

    @Inject // used to send the ErrorsMessageCommand below
    lateinit var dispatcher: Dispatcher

    private val has_run = AtomicBoolean()

//    @Inject
//    lateinit var configuredTracer: Tracer
//

//    @Inject
//    lateinit var auTracer: AuTracer

    var error: Throwable? = null // used for testing

    // @WithSpan
    @Synchronized
    @Transactional // https://www.objectdb.com/tutorial/jpa/netbeans/spring/dao
    fun handle(
        db: AuEntityManager, // will be created per request
        //  @SpanAttribute("event")
        json: String,
        // store_state: (session_id: String, zipped_array: ByteBuffer) -> Unit,
        // showMessage: (session_id: String, showMessage: ClientCommand.ShowMessage) -> Unit
    ): String {

        error = null // clear last error if any

        val start_command = System.nanoTime()

        val is_heartbeat = json == "TICK"

        if (!is_heartbeat) {
            //Log.info(jsonToPrettyFormat(json))
            //Log.info("======= handler ========")
            Log.info(json_to_yaml(json))
        }

        val commandParams: CommandParams = commandParameters(is_heartbeat, json)

        val command: EngineCommand =
            deserializeToCommand(is_heartbeat, commandParams)

        if (!is_heartbeat) {
            Log.info(command.to_json())
        }

        val cmd_json = saveCommandJson(command, is_heartbeat, commandParams, db)

        //   val action: EngineAction = try {
        try {
//            Log.setLogger(
//                LoggerConfiguration()
//                    .writeTo(coloredConsole())
//                    .writeTo(
//                        rollingFile("test-{Date}.log"),
//                        LogEventLevel.Information
//                    )
//                    .writeTo(seq("http://localhost:5341/"))
//                    .setMinimumLevel(LogEventLevel.Verbose)
//                    .createLogger()
//            )
            val action = validateCommand(command, db, commandParams.session_id)

            //LoggingUtil.logObjectMessage(LOG, action)

            mutateAction(db, action)

            val start_stores = System.nanoTime()

            // events:
            updateStore(is_heartbeat, db, action, commandParams)
            metrics.add_request_latency(cmd_json, start_command, start_stores)

            return "Succeeded"
        } catch (e: Throwable) {

            error = e

            // only makes sense to send the error back if we have a sid:

            if (e is AlertException) {
                Log.error("AlertException: ${e.message}")
                handle_alert(e, commandParams, command)
                return "AlertException handled: ${e.message}"
            } else {
                Log.error("Error: ${e.message}", e)
            }

            e.cause?.let { ee: Throwable ->
                if (ee is AlertException) {
                    Log.error("AlertException cause: ${ee.message}")
                    handle_alert(ee, commandParams, command)
                    return "AlertException cause handler: ${ee.message}"
                } else {
                    Log.error("Error cause: ${e.message}", ee)
                    throw ee
                }
            }

            throw e // ie: neither e nor e.cause is an AlertException

        }
    }

    // @WithSpan
    fun updateStore(
        is_heartbeat: Boolean,
        db: AuEntityManager,
        action: EngineAction,
        commandParams: CommandParams,
    ) {
        when {
            is_heartbeat -> {
                session_manager.handle(db, action)
            }

            else -> {
                Log.info(">>>")
                Log.info("session_id = " + commandParams.session_id)
                session_manager.handle(db, action)
                Log.info("<<<")

            }
        }
    }

    // @WithSpan
    fun mutateAction(
        db: AuEntityManager,
        action: EngineAction,
    ) {
        db.transact {
            action.mutate()
        }
    }

    // @WithSpan
    fun validateCommand(
        command: EngineCommand,
        db: AuEntityManager,
        session_id: String?,
    ): EngineAction {
        val action = command.validate(db, session_id)

        if (!has_run.get()) {
            has_run.set(true)
            db.transact {
                db.sessions_non_terminated().forEach {
                    it.terminate(SERVER_REBOOT)
                    db.save(it)
                }
            }
        }
        return action
    }

    //@WithSpan
    fun saveCommandJson(
        command: EngineCommand,
        is_heartbeat: Boolean,
        commandParams: CommandParams,
        db: AuEntityManager,
    ): String {
        val cmd_json = command::class.java.simpleName +
                if (is_heartbeat) ""
                else ": " + commandParams.command_json!!.toString()

        if (!is_heartbeat) {
            try {
                db.transact {
                    db.save(CommandJson(cmd_json))
                }
            } catch (t: Throwable) {
                t.printStackTrace()
            }
        }
        return cmd_json
    }

    // @WithSpan
    fun deserializeToCommand(
        is_heartbeat: Boolean,
        commandParams: CommandParams,
    ): EngineCommand {
        val command: EngineCommand = when {
            is_heartbeat -> HeartbeatCommand.instance
            else -> try {
                command_deserializer.to_command(
                    commandParams.command_json!!,
                    commandParams.classname
                )
            } catch (t: Throwable) {
                t.printStackTrace()
                throw t
            }
        }
        return command
    }

    // @WithSpan
    fun commandParameters(
        is_heartbeat: Boolean,
        json: String,
    ): CommandParams {
        val commandParams: CommandParams =
            when {
                is_heartbeat -> CommandParams(
                    session_id = null,
                    command_json = null,
                    simplename = heartbeat_simplename,
                    classname = heartbeat_classname
                )

                else -> {
                    val any = JsonIterator.deserialize(json)
                    CommandParams(
                        session_id = any.toString("session_id"),
                        command_json = any.get("command"),
                        simplename = any.toString("simplename"),
                        classname = any.toString("classname")
                    )
                }
            }
        return commandParams
    }

    fun handle_alert(
        e: AlertException,
        commandParams: CommandParams,
        command: EngineCommand
    ) {
        when (val sid = commandParams.session_id) {
            null -> {
                e.printStackTrace()
                throw e
            } // can't send back to user, so this is thrown
            else -> {
                session_socket.publish(
                    sid, gzipClientCommand(
                        ClientCommand.ShowMessage(
                            BrowserMessageKind.ALERT,
                            listOf(e.message!!)
                        )
                    )
                )

                e.message.let {
                    if (it?.trim() != "" && command is DeOrderSubmitCommand) {
                        dispatcher.send_message(
                            mapOf(
                                "session_id" to null,
                                "simplename" to ErrorsSendCommand::class.simpleName,
                                "classname" to ErrorsSendCommand::class.qualifiedName,
                                "command" to mapOf(
                                    "auction_id" to command.auction_id,
                                    "error" to e.message,
                                    "trader_session_id" to sid
                                )
                            ).to_json()
                        )
                    }
                }
            }
        }
    }

}


