package au21.engine.framework.commands.client.manager

import au21.engine.domain.common.commands.client.*
import au21.engine.domain.common.commands.engine.AuctionSelectAction
import au21.engine.domain.common.commands.engine.LoginAction
import au21.engine.domain.common.commands.engine.MessageSendAction
import au21.engine.domain.common.commands.engine.SessionTerminateAction
import au21.engine.domain.common.model.AuSession
import au21.engine.domain.de.commands.client.DeAuctionValue
import au21.engine.domain.de.commands.client.DeTraderBuffer
import au21.engine.domain.de.commands.engine.DeOrderSubmitAction
import au21.engine.domain.de.model.DeAuction
import au21.engine.framework.commands.client.*
import au21.engine.framework.commands.client.ClientCommand.*
import au21.engine.framework.commands.client.ClientCommand.StoreCommand.*
import au21.engine.framework.commands.client.old.ClientBuffers
import au21.engine.framework.commands.client.helper.CommandHelper
import au21.engine.framework.commands.engine.EngineAction
import au21.engine.framework.commands.engine.HeartbeatAction
import au21.engine.framework.database.AuEntityManager
import au21.engine.framework.utils.gzipClientCommand
import org.eclipse.microprofile.opentracing.Traced
import java.nio.ByteBuffer
import javax.inject.Inject

/**
 * class AddDeMatrixRound(val de_matrix_round: DeMatrixRoundElement)
 * class AddMessage(val message: MessageElement)
 * class SetDeStatus(val de_status: DeStatus)
 * // class SetSession(val session:UserSessionValue)
 * class SetStore(val store: au21.engine.framework.session.ClientStore) // default one
 * class SetTime(val time: TimeValue)
 */

// @ApplicationScoped
class ClientsManager1 :IClientsManager {

    @Inject
    lateinit var client_buffers: ClientBuffers


    @Inject
    lateinit var socket: ClientSocket

    @Traced
    override fun handle(db: AuEntityManager, action: EngineAction) {

        val h = CommandHelper(db, action.session)
        val sid: String? = action.sid() // NB:  when CreateSessionCommand -> new_session_id
        val session: AuSession? = action.get_session()

        // TODO: handle TerminateSession

        start_commands(socket, h.non_terminated_sids)

        session?.let { s ->
            println("session id: $sid")
            socket.publish(s.session_id, gzipClientCommand(SetSessionUser(s)))
        }

        if (client_buffers.companies_buffer.buffer == null) {
            client_buffers.companies_buffer.set_buffer(CompanyElement.command(db))
        }

        if (client_buffers.auctioneer_counterparties_buffer.buffer == null) {
            client_buffers.auctioneer_counterparties_buffer.set_buffer(CounterpartyCreditElement.command_for_auctioneer(db))
        }

//        if (users_buffer.buffer == null) {
//            users_buffer.set_buffer(UserElement.set_all(db, h.sessions_logged_in))
//        }

        if (client_buffers.auctioneer_auction_rows.buffer == null) {
            client_buffers.auctioneer_auction_rows.set_buffer(AuctionRowElement.command_for_auctioneers(db))
        }

        when (action) {
            is HeartbeatAction -> {

                // 1) Time signal
                // - all sessions get the time signal:

                if (h.non_terminated_sids.isNotEmpty()) {
                    val buf = gzipClientCommand(SetTime(TimeValue.now("Houston")))
                    h.non_terminated_sids.forEach { _ -> socket.publish(sid, buf) }
                }

                // 2) Heartbeats actions where auctions are 'ticked', but round not closed
                // - for these we just send auction state.
                // - if there are no auctions in the heartbeat action in which rounds were closed,
                //   - then we are done for HeartbeatActions

                action.de_auctions_ticked.forEach { de ->
                    h.auction_sids(de).let { sids ->
                        if (sids.isNotEmpty()) {
                            val buf = gzipClientCommand(SetDeStatus(de))
                            sids.forEach { sid -> socket.publish(sid, buf) }
                        }
                    }
                }
                action.de_auctions_round_closed.forEach { de ->
                    client_buffers.get_de_auction(de).update_auctioneers(socket, de, h)
                }
            }
//            is MessageSendAction -> {
//                // TODO: 7/13/21
//                TODO()
//            }
//            is DeOrderSubmitAction -> {
//                TODO()
//                // for order submits we want to be efficient,
//                // - due to 'deadline effects', where bidders feel they all need to submit an order at some point in time
//                // - usually it's the end of the round, but can be beginning of round.
//
//            }
//            is DeRoundControllerAction -> {
//                TODO()
//            }

            is LoginAction -> {
                when {
                    h.is_auctioneer -> {
                        client_buffers.auctioneer_auction_rows.init_session(socket, sid)
                        client_buffers.auctioneer_counterparties_buffer.init_session(socket, sid)
                        client_buffers.companies_buffer.init_session(socket, sid)
                    }
                    else -> AuctionRowElement.publish(socket, db, session)
                }
            }

            is AuctionSelectAction -> {
                when (val de = action.a) {
                    is DeAuction ->
                        when {
                            h.is_auctioneer -> client_buffers.get_de_auction(de).init_auctioneer(socket, session)
                            else -> {
                                DeTraderBuffer.init_trader(socket, session)
                            }
                        }
                }
            }


            else -> {
                // OTHERWISE WE PRETTY MUCH GO THROUGH EVERYTHING, in alphabetical order
                // - these are events that occur once every 30-60 seconds at the least

                // AUCTION ROWS
                // - if auctioneer rows have changed then send to all
                // - else if auctioneer login -> latest
                // for all traders
                // - send if changed

                client_buffers.auctioneer_auction_rows.update_sessions(
                    socket,
                    h.auctioneer_sids,
                    force = true
                ) {
                    AuctionRowElement.command_for_auctioneers(db)
                }
                h.trader_sessions.forEach { s: AuSession -> AuctionRowElement.publish(socket, db, s) }

                // COMPANIES
                // - if changed -> send to all auctioneers
                // - else if this is an auctioneer login -> send to session
                // Note: possible unneccessary check, if auctioneer login, then they won't have changed fyi
                // - but time to create and json may only be a couple ms


                client_buffers.companies_buffer.update_sessions(socket, h.auctioneer_sids, force = true) {
                    CompanyElement.command(db)
                }


                // COUNTERPARTY_CREDITS:
                // - if changed -> all
                // - else if auctioneer login -> send to session

                // THE NEXT 3 ARE SENT TOGETHER:

                // DEAUCTION
                // for all auctioneers on that auction:
                //  - send if changed
                //  - otherwise if this is an auction select then send latest
                // for all logged in traders:
                //  - send if changed
                //  - otherwise if this is an auction select then send latest

                h.auctions_logged_in.forEach { a ->
                    when (a) {
                        is DeAuction -> {
                            client_buffers.get_de_auction(a).update_auctioneers(socket, a, h)
                            gzipClientCommand(SetDeStatus(a)).let { buf ->
                                h.auction_sids(a).forEach { socket.publish(it, buf) }
                            }
                        }
                    }
                }

                // DESTATUS
                // - send a new one to all on auction


                // DEMATRIXROUNDS
                // - if latest round changed -> send
                // - else if auctioneer login -> send to auctioneer
                // - else if for prev round -> send to auctioneer (from prev round cache)

                h.auctions_logged_in.forEach { a ->
                    when (a) {
                        is DeAuction -> {
                            client_buffers.get_de_auction(a).update_auctioneers(socket, a, h)
                            h.trader_auction_sessions(a).forEach { s ->
                                a.trader_by_company_id(s.company_id)?.let { t: DeAuction.Trader ->
                                    socket.publish(s.session_id, gzipClientCommand(DeAuctionValue.command_for_trader(a, t)))
                                }
                            }
                        }
                    }
                }

                // SESSION USER
                // - send to all sessions

                h.sessions_logged_in.forEach { s ->
                    socket.publish(s.session_id, gzipClientCommand(SetSessionUser(SessionUserValue(s))))
                }

                // TIME - sent with heartbeat
                // - send to all sessions

                // USERS:
                // - if changed -> send to all auctioneers
                // - else if this is an auctioneer login -> send to session

            }

        }


        /**
         * COMMON:
         * - Things to run always unless is [Heartbeat, Status, Message]
         */

        when (action) {
            is HeartbeatAction -> {
            } // this is where both Time and DeStatus are sent.
            is MessageSendAction -> {
            }
            else -> {
                client_buffers.users_buffer.update(socket, db, h.auctioneer_sids)
            }
        }

        when (action) {
            is DeOrderSubmitAction -> {
                notify(
                    socket,
                    MessageElement.recipient_sids_for_message(h.auction_sessions(action.de), action.de, action.message),
                    MessageElement(action.message)
                )
            }
            is MessageSendAction -> {
                notify(
                    socket,
                    MessageElement.recipient_sids_for_message(
                        h.auction_sessions(action.auction),
                        action.auction,
                        action.message
                    ),
                    MessageElement(action.message)
                )
            }
            is LoginAction -> {

                action.existing_session?.let { existing ->
                    terminate_session(
                        socket,
                        existing,
                        "You have logged in from another browser or device.",
                    )
                }
                // send the companies:
            }
            else -> {
                // send companies of changed

            }

        }

        when (action) {
            is SessionTerminateAction -> {
                terminate_session(socket, action.session, "")
            }
        }

        succeeded(socket, h.non_terminated_sids)


    }

// START_COMMANDS
// ADD MESSAGE
// TERMINATE
// NOTIFY
// SUCCEEDED

    fun start_commands(socket: ClientSocket, sids: List<String>) {
        gzipClientCommand(StartStoreCommands()).also { buf ->
            sids.forEach { socket.publish(it, buf) }
        }
    }

    fun succeeded(socket: ClientSocket, sids: List<String>) {
        gzipClientCommand(CommandSucceeded()).also { buf ->
            sids.forEach { socket.publish(it, buf) }
        }
    }

    fun notify(
        socket: ClientSocket,
        recipient_sids: List<String>,
        m: MessageElement
//        sessions: List<AuSession>,
//        a: Auction,
//        m: AuctionMessage
    ) {
        if (recipient_sids.isEmpty())
            return
        gzipClientCommand(ShowMessage(BrowserMessageKind.NOTIFICATION, listOf(m.message))).also { buf: ByteBuffer ->
            recipient_sids.forEach { socket.publish(it, buf) }
        }

//        sessions
//            .filter { s -> MessageElement.is_for(a, m, s) }
//            .map { it.session_id }
//            .filter { it != sender_sid }
//            .forEach { sid ->
//                socket.publish(sid, buffer)
//            }
    }

    fun terminate_session(socket: ClientSocket, s: AuSession?, message: String) {
        when (s) {
            null -> return
            else -> socket.publish(s.session_id, gzipClientCommand(TerminateSession(message)))
        }
    }
}

//    fun create_store_buffers(
//        db: AuEntityManager,
//        action: EngineAction
//    ): Map<String, ByteBuffer> {
//
//        return duration_ms("SESSION_MANAGER") {
//            val h = SessionStoreHelper(db, action)
//
//            db.sessions_non_terminated().associate { s: AuSession ->
//                val session_store: ClientStore = h.session_store(s)
//                val cmd = ClientCommand.StoreCommand.SetStore(session_store)
//                s.session_id to gzip(cmd.to_json())
//            }.toMap()
//        }
//    }

//    ): Map<String, ByteBuffer> {
//
//        val store_map: Map<String, ByteBuffer> =
//            duration_ms("SESSION_MANAGER.HANDLE") {
//                val h = SessionStoreHelper(db, action)
//                db.sessions_non_terminated().associate { s: AuSession ->
//                    val session_store: ClientStore = h.session_store(s)
//                    val cmd = ClientCommand.StoreCommand.SetStore(session_store)
//                    s.session_id to gzip(cmd.to_json())
//                }
//            }
//
//        return store_map

//        val sessions: MutableList<Pair<TargetList, ByteBuffer>> = mutableListOf()
//        when (action) {
//            is HeartbeatAction -> {
//
//            }
//            is MessageSendAction -> {
//
//            }
//            is DeFlowControlAction -> {
//
//            }
//            is DeRoundControllerAction -> {
//
//            }
//            else -> {
//            }
//
//
//    }
//
//}
//
