package au21.engine.domain.de.commands

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.session_non_terminated_or_alert
import au21.engine.domain.de.model.DeAuction
import au21.engine.domain.de.model.DeTradingCompany
import au21.engine.framework.commands.AlertException
import au21.engine.framework.commands.EngineAction
import au21.engine.framework.commands.EngineCommand
import au21.engine.framework.commands.trim_commas_and_underscores
import au21.engine.framework.database.AuEntityManager

//TODO: Need to implement
class DeSetLenderSetCreditCommand(
    val auction_id: String,
    val lender_id: String,
    val borrower_ids: List<String>,
    val buying_credit_limits: List<String>,
) : EngineCommand() {

    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        val session = db.session_non_terminated_or_alert(session_id)

        val credit_limit_absolute: List<Double> = buying_credit_limits.map {
            it.trim_commas_and_underscores().replace("$", "").toDoubleOrNull()
                ?: throw AlertException("Credit limit is not a valid number: $it")
        }.toList()

        val de: DeAuction =
            db.byId<DeAuction>(auction_id) ?: throw AlertException("Cannot find auction without an id: $auction_id")

        val lender = de.de_trading_companies.firstOrNull { it.company_id == lender_id.toLong() }
            ?: throw AlertException("lender not in auction")

        val borrowers = borrower_ids.map { bId ->
            de.de_trading_companies.firstOrNull { it.company_id == bId.toLong() }
                ?: throw AlertException("borrower with id: $bId not in auction")
        }.toList()


        return DeSetLenderSetCreditAction(
            command = this,
            db = db,
            session = session,
            de = de,
            lender = lender,
            borrowerCredits = emptyList()
        )
    }
}

class BorrowerCredit(
    val borrower: DeTradingCompany,
    val credit_limit: Double,
)

class DeSetLenderSetCreditAction(
    override val command: DeSetLenderSetCreditCommand,
    override val db: AuEntityManager,
    override val session: AuSession,
    val de: DeAuction,
    val lender: DeTradingCompany,
    val borrowerCredits: List<BorrowerCredit>,
) : EngineAction {
    override fun mutate() {

        borrowerCredits.forEach {
            de.set_counterparty_credit_limit(lender, it.borrower, it.credit_limit)
        }
        //TODO: Find minimum and set it for lender
        db.save(de)
    }

}
