package au21.engine.domain.common.commands.engine

import au21.engine.domain.common.model.AuSession
import au21.engine.domain.common.services.session_by_sid
import au21.engine.framework.commands.engine.EngineAction
import au21.engine.framework.commands.engine.EngineCommand
import au21.engine.framework.commands.engine.alert
import au21.engine.framework.database.AuEntityManager


class SessionCreateCommand : EngineCommand() {
    override fun validate(db: AuEntityManager, session_id: String?): EngineAction {
        // session above should be null
        //val is_debug = session_id in listOf("1", "2", "3", "4")

        if (session_id == null)
            alert("No session id.")

        // this causes problems with hot-reload
//        fail_if(
//            (db.session_by_sid(session_id) != null) && !is_debug,
//            "Session id already exists: $session_id"
//        )

        val existing_session = db.session_by_sid(session_id) // != null) // && !is_debug

        return SessionCreateAction(this, db, existing_session, session_id)
    }
}


class SessionCreateAction(
    override val command: SessionCreateCommand,
    override val db: AuEntityManager,
    override val session: AuSession? = null,
    val session_id: String,
) : EngineAction {

    lateinit var new_session: AuSession

    override fun mutate() {
        new_session = session ?: run {
            AuSession(session_id).also {
                db.save(it)
            }
        }

    }
}
