// File: Auction.kt
package au21.engine.domain.common.model

import au21.engine.framework.database.AuEntity
import java.util.*
import javax.jdo.annotations.Index
import javax.persistence.Entity

// no longer abstract, so can find it more easily
@Entity
abstract class Auction(
    var auction_name: String,
) : AuEntity() {

    @Index
    var closed: Boolean = false
        protected set

    @Index
    var hidden: Boolean = false

    var notice: String = ""

    var starting_time: Date? = null

    var auction_has_started: Boolean = false

    var common_state_text: String = ""
    var auctioneer_state_text: String = ""

    abstract fun starting_time_text(): String

    protected var trading_companies: MutableList<CompanyProxy> = mutableListOf()
        private set

    fun has_trader(c: Company?): Boolean =
        when (c) {
            null -> false
            else -> trading_companies.any { it.company_id == c.id }
        }

    fun show_auction(s: AuSession?): Boolean =
        when {
            s == null -> false
            s.user?.role == AuUserRole.AUCTIONEER -> true
            this.hidden -> false
            this.has_trader(s.user?.company) -> true
            else -> false
        }

    var messages: MutableList<AuctionMessage> = mutableListOf()
        private set

    var users_that_have_seen_auction: MutableSet<PersonProxy> = mutableSetOf()
        private set


    var companies_that_have_seen_auction: MutableSet<CompanyProxy> = mutableSetOf()
        private set

    fun add_trader_that_has_seen_auction(s: AuSession) {
        s.user?.let { u: Person ->
            users_that_have_seen_auction.add(PersonProxy(u))
            u.company?.let { c: Company ->
                trading_companies.find { it.company_id == c.id }
                    ?.let {
                        companies_that_have_seen_auction.add(it)
                    }
            }
        }
    }

    // Counterparty credit:
    var counterparty_credit_limits: MutableList<CounterpartyCreditLimit> = mutableListOf()
        private set

    fun get_counterparty_credit_limit(lender: CompanyProxy, borrower: CompanyProxy): CounterpartyCreditLimit? =
        counterparty_credit_limits.find {
            it.lender == lender && it.borrower == borrower
        }

    fun set_counterparty_credit_limit(lender: CompanyProxy, borrower: CompanyProxy, limit: Double?) {
        (get_counterparty_credit_limit(lender, borrower)
            ?: run {
                CounterpartyCreditLimit(lender, borrower).also {
                    counterparty_credit_limits.add(it)
                }
            }).set_credit_limit(limit)
    }

    //     var state : IAuctionState
//        get() = enumValueOf(state_label) // enumValueOrNull<TeAuctionState>(state_string)!!
//        set(value) {
//            state_label = value.toString()
//        }

    // Another way of doing enums in general
//    var state_label: String = DeState.AUCTION_INIT.toString()
//        private set
//
//    var state: DeState
//        get() = DeState.valueOf(state_label)
//        set(it) {
//            this.state_label = it.toString()
//        }


    // 2) state label (ie: state could be open_for_bidding, and label could be "open for 20:00:00" etc
    // abstract var state_label: String


}
