// File: DePriceRule.kt
package au21.engine.domain.de.model

import au21.engine.domain.common.model.AuUserRole
import javax.persistence.Embeddable


@Embeddable
class DePriceRule(
    var price_change_initial: Double = 0.5,
    var price_change_post_reversal: Double = 0.125,
    var excess_level_1_quantity: Int = 10,
    var excess_level_2_quantity: Int = 20,
    var excess_level_3_quantity: Int = 30,
    var excess_level_4_quantity: Int = 40,
    var excess_level_0_label: String = "1+",
    var excess_level_1_label: String = "2+",
    var excess_level_2_label: String = "3+",
    var excess_level_3_label: String = "4+",
    var excess_level_4_label: String = "5+",
) {

    // NOTE: Can be either excess demand (UP), or excess supply (DOWN):

    fun get_excess_level(excess: Int, role: AuUserRole): String =
        when {
            excess > excess_level_4_quantity -> excess_level_4_label
            excess > excess_level_3_quantity -> excess_level_3_label
            excess > excess_level_2_quantity -> excess_level_2_label
            excess > excess_level_1_quantity -> excess_level_1_label
            excess > 0 -> excess_level_0_label
            excess == 0 ->
                if (role == AuUserRole.AUCTIONEER) {
                    "0"
                } else {
                    excess_level_0_label
                }
            else ->
                if (role == AuUserRole.AUCTIONEER) {
                    "-"
                } else {
                    excess_level_0_label
                }
        }

//    fun get_excess_label(excess: Int): String =
//        when {
//            excess <= excess_level_1_volume -> excess_level_0_label
//            excess <= excess_level_2_volume -> excess_level_1_label
//            excess <= excess_level_3_volume -> excess_level_2_label
//            excess <= excess_level_4_volume -> excess_level_3_label
//            else -> excess_level_4_label
//        }

//    fun get_price_change_for_excess_level(level: ExcessLevel): Double =
//        when (level) {
//            ExcessLevel.NONE -> 0.0
//            ExcessLevel.ONE_PLUS -> price_level_1
//            ExcessLevel.TWO_PLUS -> price_level_2
//            ExcessLevel.THREE_PLUS -> price_level_3
//            ExcessLevel.FOUR_PLUS -> price_level_4
//        }
}
