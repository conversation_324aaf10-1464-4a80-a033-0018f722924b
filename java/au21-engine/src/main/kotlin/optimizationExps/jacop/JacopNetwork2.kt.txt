package optimizationExps.jacop

import org.jacop.constraints.netflow.NetworkBuilder
import org.jacop.constraints.netflow.NetworkFlow
import org.jacop.constraints.netflow.simplex.Node
import org.jacop.core.IntVar
import org.jacop.core.Store
import org.jacop.search.*


class JacopNetwork2(
    val size: Int = 4
) {
    val start = System.nanoTime()

    val store = Store();

    val net: NetworkBuilder = NetworkBuilder()

    val source: Node = net.addNode("source", 5)
    val sink: Node = net.addNode("sink", -5)

    val A: Node = net.addNode("A", 0)
    val B: Node = net.addNode("B", 0)
    val C: Node = net.addNode("C", 0)
    val D: Node = net.addNode("D", 0)

    val X0 = IntVar(store, "x_0", 0, 5)
    val X1 = IntVar(store, "x_1", 0, 5)

    init {
        net.addArc(source, A, 0, X0)
        net.addArc(source, C, 0, X1)
    }

    val X2 = IntVar(store, "a->b", 0, 5)
    val X3 = IntVar(store, "a->d", 0, 5)
    val X4 = IntVar(store, "c->b", 0, 5)
    val X5 = IntVar(store, "c->d", 0, 5)

    init {
        net.addArc(A, B, 3, X2);
        net.addArc(A, D, 2, X3);
        net.addArc(C, B, 5, X4);
        net.addArc(C, D, 6, X5);
    }

    val X6 = IntVar(store, "x_6", 0, 5)
    val X7 = IntVar(store, "x_7", 0, 5)

    init {
        net.addArc(B, sink, 2, X6);
        net.addArc(D, sink, 5, X7);
    }

    val cost = IntVar(store, "cost", 0, 1000)

    init {
        net.setCostVariable(cost);
        store.impose(NetworkFlow(net));

        if (!store.consistency()) {
            throw Error("store inconsistent")
        } else {
            println("store consistent")

        }

        val label: Search<IntVar> = DepthFirstSearch<IntVar>()
        val select: SelectChoicePoint<IntVar> = InputOrderSelect(
            store,
            arrayOf(X0, X1, X2, X3, X4, X5, X6, X7),
            IndomainMax()
        )
        val result: Boolean = label.labeling(store, select)

    }

}

fun main(args: Array<String>) {
    JacopNetwork2()
}
