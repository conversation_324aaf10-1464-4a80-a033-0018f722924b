I'm proposing the following directory structure:
- this is a polyrepo, mixing:
  - nodejs packages
  - python modules and jupyter notebooks
- shared data, refencences, docs, and databases:
  

```sh
project-root/
|-- data/                  # Top-level directory for all data (versioned with LakeFS, excluded from git)
|   |-- raw/           # Scripts for data processing, etc.
|   |-- processing/           
|   |-- processing/           
|   `-- databases/         # sqlite databases, and postgress compressed sql dumps
|-- references/            # Top-level directory for reference documents
|-- nodejs/                # Node.js top-level directory
|   |-- packageA/          # Package A under Node.js
|   |-- packageB/          # Package B under Node.js
|   `-- common/            # Common utilities or shared code
|-- python/                # Python top-level directory, using UV
|   |-- notebooks/          # Jupyter notebooks
|   |-- modules/           # Python modules
|   |-- scripts/           # Scripts for data processing, etc.
|   `-- requirements.txt    # Dependencies for Python projects
```