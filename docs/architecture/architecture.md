# AU25 Auction System Architecture

## System Overview
The AU25 Auction system is a comprehensive platform designed to facilitate complex auction processes through a distributed architecture with decoupled backend and frontend components. The system employs a unique full state reconstruction approach optimized for real-time auction operations.

## Core Architecture

### Backend Architecture
- **Core Technologies**: 
  - Java/Kotlin with Quarkus
  - ObjectDB for persistent storage
  - Gradle for build management
  - GraphQL API
  - WebSocket support for real-time updates

### Frontend Architecture
- **Framework**: React with Shadcn UI
- **State Management**: Valtio
- **Component Design**: Modular and reusable components
- **Responsive Design**: Adaptive to multiple device types

## State Management Architecture

### Full State Reconstruction Approach
The AU25 platform uses a unique approach to state management that prioritizes reliability and simplicity:

- Instead of traditional event sourcing with incremental updates, we reconstruct and send complete state
- Powered by ObjectDB's in-memory cache for efficient reconstruction
- Every client receives their entire state tree, serialized by role and permissions

#### Key Advantages
- Eliminates complex state synchronization issues
- Avoids edge cases common in event-based systems
- Simplifies client-side state management
- Reduces CPU usage compared to computing delta updates

#### Performance Characteristics
- Supports 200+ simultaneous clients watching 10 different auctions
- Each auction can handle 50 rounds
- Achieves sub-100ms response times
- Bandwidth increase is negligible for our use case

### Role-Based State Generation
```kotlin
fun create(s: AuSession, cache: DbSessionCache): LiveClientStore {
    val role: AuUserRole? = s.user?.role
    return LiveClientStore(
        auction_rows = when (s.page) {
            PageName.HOME_PAGE ->
                when (role) {
                    AuUserRole.AUCTIONEER -> cache.auctioneer_row_elements
                    AuUserRole.TRADER -> AuctionRowElement.elements_for_session(
                        cache.auctions,
                        s
                    )
                    else -> emptyList()
                }
            else -> emptyList()
        }
    )
}
```

This role-based approach ensures:
- Each user only sees appropriate data
- State is filtered at generation time
- Access control is consistently enforced

## Communication Architecture

### Integration Mechanisms
- **GraphQL Endpoint**: `http://localhost:4040/graphql`
  - Supports complex, flexible data querying
  - Provides GraphQL UI at `http://localhost:4040/q/graphql-ui/`
- **WebSocket**: `ws://dev1.auctionologies.com:4040/socket`
  - Enables live updates and event-driven interactions
  - Handles session lifecycle and state updates
- **Static Resource Serving**:
  - Frontend build output served from backend resources
  - Path: `./src/main/resources/META-INF/resources`

### Command Processing System
```kotlin
sealed class ClientCommand {
    abstract val command: CommandType
    
    // Browser commands for UI updates
    class ShowMessage(
        val browser_message_kind: BrowserMessageKind,
        val message: List<String>
    ) : ClientCommand()
    
    // Store commands for state updates
    sealed class StoreCommand : ClientCommand() {
        class SetLiveStore(
            val store: LiveClientStore
        ) : StoreCommand()
        
        class AddElements(
            element_class: Class<out StoreElement>,
            val elements: List<StoreElement>?
        ) : StoreCommand()
    }
}
```

The command system provides:
- Type-safe command handling
- Clear separation between browser and store commands
- Support for both full store updates and incremental additions

## System Flow

### Command Processing Flow
```mermaid
sequenceDiagram
    participant C as Client
    participant W as WebSocket
    participant P as Command Processor
    participant D as ObjectDB
    participant S as State Generator
    
    C->>W: Send Command
    W->>P: Process Command
    P->>D: Update Objects
    P->>S: Request State Update
    S->>D: Read Objects
    S->>S: Generate Client State
    S->>W: Send Full State
    W->>C: Update Client
```

### State Generation Flow
```mermaid
graph LR
    subgraph ObjectDB
        OBJ[In-Memory Objects]
    end
    
    subgraph StateGenerator
        ROLE[Role Filter]
        PAGE[Page Context]
        GEN[State Builder]
    end
    
    subgraph ClientState
        AUCT[Auction Data]
        COMP[Company Data]
        USER[User Data]
        CRED[Credit Data]
    end
    
    OBJ --> ROLE
    OBJ --> PAGE
    ROLE --> GEN
    PAGE --> GEN
    GEN --> AUCT
    GEN --> COMP
    GEN --> USER
    GEN --> CRED
```

## Error Handling Strategy
```mermaid
flowchart TB
    E[Error Occurs]
    D{Error Type}
    V[Validation Error]
    N[Network Error]
    S[State Error]
    C[Command Error]
    
    E --> D
    D -->|Invalid Input| V
    D -->|Connection| N
    D -->|State Sync| S
    D -->|Processing| C
    
    V -->|Return Error| Client
    N -->|Reconnect| Client
    S -->|Resend State| Client
    C -->|Rollback| Client
```

## Security Architecture
- Secure authentication mechanisms
- Role-based access control
- Data encryption
- Audit logging

## Monitoring and Observability
- Jaeger for distributed tracing
- Seq for centralized logging
- OpenTelemetry integration
- Performance metrics tracking
- Error monitoring

## Deployment Architecture
- Docker-based deployment
- CI/CD pipeline support
- Flexible environment configuration
- Infrastructure as Code (IaC)

## Future Roadmap
- Microservices transition planning
- Enhanced real-time capabilities
- Machine learning integration opportunities
- Scaling considerations:
  - Current architecture has 2x headroom
  - No immediate need for sharding
  - Focus on optimization before scaling

## Version Information
- Architecture Version: 2.0
- Last Updated: January 7, 2025
- Previous Versions:
  - 1.0: Initial architecture (2024)
  - 0.1: Legacy system architecture (2021)