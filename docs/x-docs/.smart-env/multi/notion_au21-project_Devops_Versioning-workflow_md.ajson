
"smart_sources:notion/au21-project/Devops/Versioning-workflow.md": {"path":"notion/au21-project/Devops/Versioning-workflow.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"c144de550697f9bd67694981bc46d0dc93e9ab531a671cccd26cb2f2a24756ad","at":1736406297048},"class_name":"SmartSource","outlinks":[{"title":"Gitlab versioning examples and libraries","target":"https://www.notion.so/Gitlab-versioning-examples-and-libraries-5bf9990f00064bd4a6ecd6066b0f315a?pvs=21","line":31},{"title":"https://git-scm.com/book/en/v2/Git-Basics-Tagging","target":"https://git-scm.com/book/en/v2/Git-Basics-Tagging","line":35},{"title":"https://axion-release-plugin.readthedocs.io/en/latest/configuration/ci_servers/#gitlab-ci","target":"https://axion-release-plugin.readthedocs.io/en/latest/configuration/ci_servers/#gitlab-ci","line":59},{"title":"https://github.com/jozala/blog-post-axion-release/blob/master/build.gradle","target":"https://github.com/jozala/blog-post-axion-release/blob/master/build.gradle","line":61},{"title":"https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Gradle.gitlab-ci.yml","target":"https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Gradle.gitlab-ci.yml","line":63},{"title":"https://gitlab.com/gitlab-org/gitlab/-/tree/master/lib/gitlab/ci/templates?_gl=1*10mhcse*_ga*NzQzOTM0NTIzLjE2ODIxMDkxOTE.*_ga_ENFH3X7M5Y*MTY4Mjk5MDEzMi4xNi4xLjE2ODI5OTE5NjQuMC4wLjA","target":"https://gitlab.com/gitlab-org/gitlab/-/tree/master/lib/gitlab/ci/templates?_gl=1%2a10mhcse%2a_ga%2aNzQzOTM0NTIzLjE2ODIxMDkxOTE.%2a_ga_ENFH3X7M5Y%2aMTY4Mjk5MDEzMi4xNi4xLjE2ODI5OTE5NjQuMC4wLjA","line":65},{"title":"https://about.gitlab.com/blog/2017/11/02/automating-boring-git-operations-gitlab-ci/","target":"https://about.gitlab.com/blog/2017/11/02/automating-boring-git-operations-gitlab-ci/","line":69},{"title":"https://marcosschroh.github.io/posts/autobumping-with-gitlab/","target":"https://marcosschroh.github.io/posts/autobumping-with-gitlab/","line":70},{"title":"https://forum.gitlab.com/t/is-it-possible-to-commit-artifacts-into-the-git/22218/7","target":"https://forum.gitlab.com/t/is-it-possible-to-commit-artifacts-into-the-git/22218/7","line":71},{"title":"https://threedots.tech/post/automatic-semantic-versioning-in-gitlab-ci/","target":"https://threedots.tech/post/automatic-semantic-versioning-in-gitlab-ci/","line":72},{"title":"https://quarkus.io/blog/*********************quarkus-with-gitlab/","target":"https://quarkus.io/blog/*********************quarkus-with-gitlab/","line":73},{"title":"https://blog.jdriven.com/2021/11/reuse-gradle-build-cache-on-gitlab/","target":"https://blog.jdriven.com/2021/11/reuse-gradle-build-cache-on-gitlab/","line":74},{"title":"https://neron-joseph.medium.com/tag-your-git-repo-using-gitlabs-ci-cd-*********************","target":"https://neron-joseph.medium.com/tag-your-git-repo-using-gitlabs-ci-cd-*********************","line":78},{"title":"https://dev.to/vumdao/config-gitlab-runner-to-push-a-tag-3l5g","target":"https://dev.to/vumdao/config-gitlab-runner-to-push-a-tag-3l5g","line":79},{"title":"https://github.com/semantic-release/gitlab","target":"https://github.com/semantic-release/gitlab#readme","line":159},{"title":"https://github.com/semantic-release/semantic-release","target":"https://github.com/semantic-release/semantic-release","line":160},{"title":"https://github.com/npm/node-semver","target":"https://github.com/npm/node-semver","line":165},{"title":"Screenshot 2023-04-28 at 10.36.02 AM.png","target":"Versioning-workflow/Screenshot2023-04-28at10.36.02AM.png","line":171}],"last_import":{"mtime":1736337795343,"size":9521,"at":1736406297048,"hash":"c144de550697f9bd67694981bc46d0dc93e9ab531a671cccd26cb2f2a24756ad"},"blocks":{"#Versioning workflow":[1,5],"#Versioning workflow#{1}":[3,5],"#Versioning requirements":[6,26],"#Versioning requirements#{1}":[8,26],"#Notes":[27,237],"#Notes#Libraries":[29,32],"#Notes#Libraries#{1}":[31,32],"#Notes#Git":[33,47],"#Notes#Git#{1}":[35,36],"#Notes#Git#{2}":[37,39],"#Notes#Git#{3}":[40,47],"#Notes#NPM":[48,55],"#Notes#NPM#{1}":[50,51],"#Notes#NPM#{2}":[52,53],"#Notes#NPM#{3}":[54,55],"#Notes#Gradle":[56,66],"#Notes#Gradle#{1}":[58,61],"#Notes#Gradle#{2}":[62,62],"#Notes#Gradle#{3}":[63,63],"#Notes#Gradle#{4}":[64,66],"#Notes#Gitlab other":[67,155],"#Notes#Gitlab other#{1}":[69,69],"#Notes#Gitlab other#{2}":[70,70],"#Notes#Gitlab other#{3}":[71,71],"#Notes#Gitlab other#{4}":[72,72],"#Notes#Gitlab other#{5}":[73,73],"#Notes#Gitlab other#{6}":[74,75],"#Notes#Gitlab other#Gitlab tagging":[76,155],"#Notes#Gitlab other#Gitlab tagging#{1}":[78,78],"#Notes#Gitlab other#Gitlab tagging#{2}":[79,80],"#Notes#Gitlab other#Gitlab tagging#{3}":[81,155],"#Notes#Semantic release":[156,161],"#Notes#Semantic release#{1}":[158,158],"#Notes#Semantic release#{2}":[159,161],"#Notes#Version tools":[162,166],"#Notes#Version tools#{1}":[164,164],"#Notes#Version tools#{2}":[165,166],"#Notes#Notes from Zoom call Friday Apr 28, 2023":[167,237],"#Notes#Notes from Zoom call Friday Apr 28, 2023#{1}":[169,237]}},
"smart_sources:notion/au21-project/Devops/Versioning-workflow.md": {"path":"notion/au21-project/Devops/Versioning-workflow.md","last_embed":{"hash":"c144de550697f9bd67694981bc46d0dc93e9ab531a671cccd26cb2f2a24756ad","tokens":477},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07197062,-0.05245326,0.01402189,-0.02092437,-0.03096887,-0.02476352,-0.11888714,0.00987267,-0.01006517,-0.0206773,0.01207095,-0.01405457,0.03878018,0.03607231,0.01461062,0.00463239,-0.02976888,0.05282667,-0.01505988,-0.02194257,0.08037924,-0.04490654,0.04119511,-0.01163419,0.01069166,0.06076455,-0.00554715,-0.02211525,-0.00584361,-0.27799165,-0.00929237,0.04824151,-0.0311779,0.06438775,-0.03371459,0.00897751,-0.03796636,0.04726468,-0.04457875,0.07083502,0.02877329,0.03893127,-0.02544415,-0.01921188,-0.06655788,-0.03181324,-0.04343654,0.02061645,-0.02696542,0.00152748,0.02209147,-0.02933864,-0.05112495,0.0784214,-0.01085561,0.03109784,0.04404282,0.07311543,0.03088961,-0.01966481,0.08365162,-0.00456177,-0.19963269,0.10908114,0.0181734,0.03739256,-0.00581865,-0.05393155,0.05275508,0.04513596,-0.04119529,0.02670567,-0.02731258,0.05461055,0.00792538,0.02931659,0.00834698,0.01378803,0.03816012,0.01737056,-0.03265581,-0.00213995,-0.05233774,-0.01124484,0.01639516,-0.06030079,0.03099598,0.0234987,0.05027318,-0.03319866,-0.03926554,-0.0639187,0.07829113,0.00258158,-0.0222823,0.03021359,0.06038503,0.03252783,-0.03661011,0.11650334,-0.02098588,0.01866828,0.04864909,0.04575718,0.04574335,0.00578941,-0.06133035,-0.06526274,0.02714137,-0.02164287,0.01830063,0.03960099,0.0768306,-0.03771502,0.0435078,0.00584059,-0.03196786,-0.01894841,0.02594587,0.01800456,0.05494433,0.00765551,0.09469081,-0.04327927,-0.00338474,0.03537438,0.03205523,-0.01715075,-0.0068113,0.02811439,0.00406929,-0.00112909,-0.07967292,-0.0410321,-0.02046636,-0.01425087,0.03516034,-0.03978539,-0.01666365,-0.03706929,-0.05951526,-0.00203339,0.03140962,-0.02916892,-0.01961523,0.12941404,-0.01036317,0.0573698,-0.06660794,-0.08727552,-0.02651513,0.0409104,-0.06141125,-0.00061062,0.04238292,0.03001534,0.00345527,0.04729217,-0.08958698,0.06132985,0.04383952,-0.01368884,-0.05374485,0.11396091,0.02116292,-0.06360158,-0.07249185,-0.00625103,0.02966911,-0.01252015,0.00027536,0.00711606,0.02081077,-0.01382359,0.07521915,-0.01625986,-0.01333102,0.00697758,0.03586511,0.08830819,0.08463982,-0.03493524,-0.03073364,0.02103386,0.00022592,-0.09140995,0.00489245,0.01085484,0.02874378,0.05107136,-0.05350246,0.04526159,0.06884286,0.03702676,0.06141513,-0.03207984,0.00043063,0.00589951,0.02304441,-0.06086446,0.0530801,0.12659207,0.02095975,-0.01426097,-0.10697629,-0.01829831,-0.03055411,-0.06012612,0.00439286,0.01926454,-0.08413297,-0.039798,0.05931916,0.02625581,-0.03932584,0.02866755,0.0003895,0.00161324,0.0302974,0.04488588,-0.00745916,0.02609268,0.00605201,-0.19823736,-0.01579926,0.03446061,-0.05245138,0.01536242,-0.04577873,0.03936991,-0.05576162,-0.00800132,0.05814123,0.05018427,-0.02327818,-0.0564977,-0.00118074,-0.0341995,0.00850919,-0.04811499,-0.00770665,-0.01789508,-0.01603582,-0.02318744,-0.00974739,-0.05769669,-0.05673664,0.05837033,-0.03834225,0.13887504,0.00308569,0.00158444,-0.0310326,0.00638169,0.02470105,0.01073779,-0.12799178,0.0201427,0.03529866,-0.06638675,-0.0152204,0.03486535,-0.00325741,0.03885656,0.00012543,-0.01609563,-0.12260168,-0.0251902,-0.01655993,-0.04006389,-0.00950631,-0.00882117,0.0166434,0.00429476,0.02426132,-0.01583346,0.00733676,-0.01831122,-0.04302277,-0.05631015,0.06378306,0.03743321,-0.03453555,0.05773524,-0.01342666,0.00040232,0.00659516,0.09279849,-0.03694193,-0.01519514,-0.04959678,0.01122264,-0.08044147,0.02905663,0.10184406,-0.00367444,-0.0209343,-0.01067263,0.05463775,-0.02524109,-0.0304852,-0.01448464,-0.01283302,-0.01435098,-0.0414881,0.01052369,0.02373384,-0.01449107,0.05698206,-0.00741732,-0.00412611,-0.00969743,-0.00238092,-0.02534723,-0.01575907,-0.02137079,-0.01624227,0.09527282,-0.00906711,-0.20209764,0.03710418,0.04412369,-0.03399759,-0.0224186,0.04204619,0.05415795,-0.01132357,-0.06601185,-0.01538746,0.02912544,0.01320431,0.01651888,-0.00409035,0.01674411,0.02932179,0.07004084,-0.05464607,0.05337599,-0.08831393,0.07292214,0.05370226,0.21273829,-0.00477325,-0.02036446,-0.02324138,-0.01513698,0.05640237,0.0167089,0.00154626,-0.04575589,-0.00427091,0.15032214,0.01459585,-0.00668505,0.06102184,-0.02662461,-0.039725,0.00827777,0.05125318,-0.04432807,-0.00746947,-0.04682675,0.0330488,0.08086021,-0.03702999,-0.0547296,-0.02244319,-0.02579914,-0.05054894,0.00352712,-0.04406833,0.00944522,0.01049201,0.07362654,0.06441113,0.03245908,0.01634408,-0.12837461,0.04862074,-0.00011224,0.02140199,0.0438663,0.03215819,-0.06470238]}},"last_read":{"hash":"c144de550697f9bd67694981bc46d0dc93e9ab531a671cccd26cb2f2a24756ad","at":1736406355064},"class_name":"SmartSource","outlinks":[{"title":"Gitlab versioning examples and libraries","target":"https://www.notion.so/Gitlab-versioning-examples-and-libraries-5bf9990f00064bd4a6ecd6066b0f315a?pvs=21","line":31},{"title":"https://git-scm.com/book/en/v2/Git-Basics-Tagging","target":"https://git-scm.com/book/en/v2/Git-Basics-Tagging","line":35},{"title":"https://axion-release-plugin.readthedocs.io/en/latest/configuration/ci_servers/#gitlab-ci","target":"https://axion-release-plugin.readthedocs.io/en/latest/configuration/ci_servers/#gitlab-ci","line":59},{"title":"https://github.com/jozala/blog-post-axion-release/blob/master/build.gradle","target":"https://github.com/jozala/blog-post-axion-release/blob/master/build.gradle","line":61},{"title":"https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Gradle.gitlab-ci.yml","target":"https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Gradle.gitlab-ci.yml","line":63},{"title":"https://gitlab.com/gitlab-org/gitlab/-/tree/master/lib/gitlab/ci/templates?_gl=1*10mhcse*_ga*NzQzOTM0NTIzLjE2ODIxMDkxOTE.*_ga_ENFH3X7M5Y*MTY4Mjk5MDEzMi4xNi4xLjE2ODI5OTE5NjQuMC4wLjA","target":"https://gitlab.com/gitlab-org/gitlab/-/tree/master/lib/gitlab/ci/templates?_gl=1%2a10mhcse%2a_ga%2aNzQzOTM0NTIzLjE2ODIxMDkxOTE.%2a_ga_ENFH3X7M5Y%2aMTY4Mjk5MDEzMi4xNi4xLjE2ODI5OTE5NjQuMC4wLjA","line":65},{"title":"https://about.gitlab.com/blog/2017/11/02/automating-boring-git-operations-gitlab-ci/","target":"https://about.gitlab.com/blog/2017/11/02/automating-boring-git-operations-gitlab-ci/","line":69},{"title":"https://marcosschroh.github.io/posts/autobumping-with-gitlab/","target":"https://marcosschroh.github.io/posts/autobumping-with-gitlab/","line":70},{"title":"https://forum.gitlab.com/t/is-it-possible-to-commit-artifacts-into-the-git/22218/7","target":"https://forum.gitlab.com/t/is-it-possible-to-commit-artifacts-into-the-git/22218/7","line":71},{"title":"https://threedots.tech/post/automatic-semantic-versioning-in-gitlab-ci/","target":"https://threedots.tech/post/automatic-semantic-versioning-in-gitlab-ci/","line":72},{"title":"https://quarkus.io/blog/*********************quarkus-with-gitlab/","target":"https://quarkus.io/blog/*********************quarkus-with-gitlab/","line":73},{"title":"https://blog.jdriven.com/2021/11/reuse-gradle-build-cache-on-gitlab/","target":"https://blog.jdriven.com/2021/11/reuse-gradle-build-cache-on-gitlab/","line":74},{"title":"https://neron-joseph.medium.com/tag-your-git-repo-using-gitlabs-ci-cd-*********************","target":"https://neron-joseph.medium.com/tag-your-git-repo-using-gitlabs-ci-cd-*********************","line":78},{"title":"https://dev.to/vumdao/config-gitlab-runner-to-push-a-tag-3l5g","target":"https://dev.to/vumdao/config-gitlab-runner-to-push-a-tag-3l5g","line":79},{"title":"https://github.com/semantic-release/gitlab","target":"https://github.com/semantic-release/gitlab#readme","line":159},{"title":"https://github.com/semantic-release/semantic-release","target":"https://github.com/semantic-release/semantic-release","line":160},{"title":"https://github.com/npm/node-semver","target":"https://github.com/npm/node-semver","line":165},{"title":"Screenshot 2023-04-28 at 10.36.02 AM.png","target":"Versioning-workflow/Screenshot2023-04-28at10.36.02AM.png","line":171}],"last_import":{"mtime":1736337795343,"size":9521,"at":1736406297048,"hash":"c144de550697f9bd67694981bc46d0dc93e9ab531a671cccd26cb2f2a24756ad"},"blocks":{"#Versioning workflow":[1,5],"#Versioning workflow#{1}":[3,5],"#Versioning requirements":[6,26],"#Versioning requirements#{1}":[8,26],"#Notes":[27,237],"#Notes#Libraries":[29,32],"#Notes#Libraries#{1}":[31,32],"#Notes#Git":[33,47],"#Notes#Git#{1}":[35,36],"#Notes#Git#{2}":[37,39],"#Notes#Git#{3}":[40,47],"#Notes#NPM":[48,55],"#Notes#NPM#{1}":[50,51],"#Notes#NPM#{2}":[52,53],"#Notes#NPM#{3}":[54,55],"#Notes#Gradle":[56,66],"#Notes#Gradle#{1}":[58,61],"#Notes#Gradle#{2}":[62,62],"#Notes#Gradle#{3}":[63,63],"#Notes#Gradle#{4}":[64,66],"#Notes#Gitlab other":[67,155],"#Notes#Gitlab other#{1}":[69,69],"#Notes#Gitlab other#{2}":[70,70],"#Notes#Gitlab other#{3}":[71,71],"#Notes#Gitlab other#{4}":[72,72],"#Notes#Gitlab other#{5}":[73,73],"#Notes#Gitlab other#{6}":[74,75],"#Notes#Gitlab other#Gitlab tagging":[76,155],"#Notes#Gitlab other#Gitlab tagging#{1}":[78,78],"#Notes#Gitlab other#Gitlab tagging#{2}":[79,80],"#Notes#Gitlab other#Gitlab tagging#{3}":[81,155],"#Notes#Semantic release":[156,161],"#Notes#Semantic release#{1}":[158,158],"#Notes#Semantic release#{2}":[159,161],"#Notes#Version tools":[162,166],"#Notes#Version tools#{1}":[164,164],"#Notes#Version tools#{2}":[165,166],"#Notes#Notes from Zoom call Friday Apr 28, 2023":[167,237],"#Notes#Notes from Zoom call Friday Apr 28, 2023#{1}":[169,237]}},"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Versioning workflow": {"path":null,"last_embed":{"hash":"87f7907e7cc826797cc51cb0dcedbda58ffe65cf55759b4861aa9ba241821d5d","tokens":35},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03926505,-0.03195081,0.01840974,-0.04232341,0.00418933,-0.0392344,-0.07782198,0.043816,-0.01330338,-0.03013015,0.0168371,-0.05199609,0.01151834,0.03814272,0.01267858,0.00158688,-0.03867685,0.01317957,-0.03757415,0.00204775,0.09272002,-0.03906612,-0.01561591,-0.02618532,0.03198521,0.08247543,0.03506253,0.00086874,-0.02760668,-0.20078959,-0.05168131,0.03719806,-0.01358424,0.02566758,0.02312101,-0.02415304,-0.00786143,0.06147395,-0.05050263,0.02927969,0.02527799,0.03337268,-0.03740538,-0.00675547,-0.06405295,-0.04428897,-0.02303595,0.01407889,-0.05949629,0.00972291,0.01699492,-0.07928377,-0.00586647,0.05933002,0.01272567,0.04606331,-0.00012142,0.05211005,0.03223849,0.01368879,0.07349715,-0.03338467,-0.2523019,0.0603162,0.00034147,0.03752646,-0.00287233,-0.10069498,0.02520479,0.03082877,-0.02001439,0.04577795,0.00925822,0.02885388,0.0513242,0.05591553,0.03758157,-0.03105157,0.03927545,0.00351221,-0.0082578,-0.01370637,-0.02121169,0.01106831,-0.02256938,-0.01637379,0.00663619,0.01414234,0.07334957,-0.01003308,-0.07734527,-0.09655397,0.09706788,0.00666505,-0.05927014,0.03251313,0.04102498,-0.01703025,-0.02299007,0.13993117,-0.05216144,0.04255909,0.02382205,-0.00333315,0.02656554,0.00127683,-0.04681666,-0.0597717,0.05527815,-0.01337244,-0.01636119,0.03272557,0.11018842,-0.06054236,0.07626398,-0.00714165,-0.035145,-0.00631247,-0.00845629,0.00036316,0.06326399,0.00387591,0.09000939,-0.02118392,-0.03908515,0.02194113,0.05906766,0.00123384,-0.03078269,0.0071805,0.00354666,0.01391999,-0.0521025,-0.05780581,0.03590721,-0.01548298,-0.02070281,-0.05173921,0.00543967,-0.02825683,-0.08228986,-0.00703454,0.0024965,-0.05174713,-0.01810861,0.1119975,-0.01493008,0.03643126,-0.02142157,-0.05779074,-0.06162375,0.038619,0.00480685,0.00018399,0.04988317,0.04470836,0.05618213,0.03793398,-0.089809,0.04417496,0.04809818,-0.03613168,-0.07484146,0.10527037,0.01609336,-0.12722898,-0.02602573,-0.02378008,0.01470495,0.03522351,-0.01218816,-0.04886789,0.010838,-0.02912414,0.09153346,0.01997494,-0.00889556,0.01619835,0.02699177,0.05835455,0.07300841,-0.03436491,-0.02050021,0.00734396,0.01724005,-0.07167415,0.03300192,0.01628515,0.00924766,0.01293308,-0.07092609,0.04752721,0.01935866,-0.00627545,0.06119059,-0.03032267,0.01550251,0.01214748,0.00269873,-0.04002878,0.03745726,0.13461913,0.01558978,-0.00155269,-0.06923954,0.01006367,-0.03721098,0.02376244,0.04088571,0.04345711,-0.05487385,-0.01664061,0.06220229,0.05135599,-0.00495509,0.06925801,-0.01463168,-0.00773938,-0.01646064,0.02738886,0.00541139,0.04188148,-0.02170784,-0.20817758,-0.01004333,0.00477525,-0.12681444,0.00444873,0.0011294,0.04183497,-0.04639741,0.03553474,0.05332059,0.06851009,0.0007494,-0.00827498,-0.00631228,-0.01708486,0.00285291,-0.01716738,-0.03489967,-0.01314174,0.03280417,0.00794653,-0.03043652,-0.00411417,-0.07080014,-0.00545206,-0.01525857,0.17134456,0.03399275,0.03090762,-0.03253005,0.03408133,-0.01649654,0.00363446,-0.19273615,-0.00424486,0.04879813,-0.02825295,-0.05926235,-0.03124513,-0.01103646,0.04117056,0.0101212,-0.02228025,-0.10680781,-0.00243286,-0.04844319,0.0119063,0.00719316,-0.04127216,0.04082489,-0.00167398,0.03868946,0.005009,0.03149199,0.0121729,-0.03485032,-0.02915194,0.06244623,0.05154311,-0.04025052,0.07436574,-0.03914884,-0.00791987,0.00662243,0.0670791,-0.05512759,-0.01911732,-0.04177047,-0.01993287,-0.06644017,0.03448208,0.03645539,-0.02732193,-0.00756251,0.0343516,0.03850245,-0.01312326,-0.02146646,0.00272293,-0.05085024,0.01966614,-0.04965862,0.01183451,-0.00285592,-0.00923178,0.0554267,0.02106094,0.00216369,0.03984112,-0.0618103,-0.0483852,-0.04027452,-0.03657935,-0.00516431,0.08225646,0.04400552,-0.21946847,0.01218581,0.04610225,-0.02182253,-0.00677419,0.0766321,0.06644218,0.01938811,-0.09284984,-0.01619183,-0.02031645,0.0205984,0.00033564,0.027858,0.04329067,0.03385985,0.07042417,-0.03528577,0.04800773,-0.09620579,0.03908451,0.04921437,0.20023406,0.01151543,-0.0172537,0.01795198,-0.00597167,0.00778201,0.0690437,-0.02765865,0.00393244,0.02173679,0.11206732,-0.00126653,-0.02063227,0.04782248,-0.01732612,-0.0263074,0.01614936,0.00451884,-0.04732056,-0.01834747,-0.0049843,-0.00190868,0.0306438,-0.0354266,-0.066489,-0.01485644,-0.0094641,-0.08022741,0.04270944,-0.07116342,0.0202793,0.02181119,0.05853954,0.06933472,0.05481237,-0.01076929,-0.10002211,-0.00761497,0.017419,0.01972608,0.04506271,-0.01630539,0.00296167]}},"text":null,"length":0,"last_read":{"hash":"87f7907e7cc826797cc51cb0dcedbda58ffe65cf55759b4861aa9ba241821d5d","at":1736406354692},"key":"notion/au21-project/Devops/Versioning-workflow.md#Versioning workflow","lines":[1,5],"size":64,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Versioning workflow#{1}": {"path":null,"last_embed":{"hash":"53954b0e86f21b5d082cc2423b3cfce77d05804743f5fee4986e05ef17dfffa4","tokens":35},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03936922,-0.03038086,0.01913956,-0.04300023,0.00521375,-0.04046014,-0.07542847,0.04430752,-0.01181483,-0.02967245,0.01671623,-0.05224027,0.01296995,0.03959642,0.01341996,0.00221887,-0.03916174,0.0119708,-0.03379153,0.00361833,0.09239034,-0.03983418,-0.01813021,-0.02560833,0.03130423,0.08247188,0.0345483,0.00485872,-0.02785736,-0.1992579,-0.05157257,0.03242479,-0.01667538,0.02522531,0.02179717,-0.02594581,-0.00669533,0.06204313,-0.04887483,0.02800239,0.02534158,0.03281319,-0.03371978,-0.00748825,-0.06406069,-0.04387864,-0.0219654,0.01350273,-0.05878919,0.0091105,0.01765225,-0.07899745,-0.00372281,0.06091583,0.01210241,0.04276997,-0.00173248,0.05387308,0.03247006,0.0156527,0.07147722,-0.03397253,-0.25261056,0.06017183,-0.00176966,0.03806753,-0.00138844,-0.10449924,0.02316258,0.03068308,-0.01981269,0.04420591,0.00745984,0.02648064,0.05364051,0.05557613,0.03766551,-0.03143845,0.03705382,0.00433773,-0.00974467,-0.01676204,-0.02049848,0.01208771,-0.0214678,-0.01421477,0.0089562,0.0117682,0.07312796,-0.00917374,-0.07806298,-0.09635331,0.09586737,0.00539396,-0.05822125,0.03211985,0.04232614,-0.0145912,-0.02456928,0.13859275,-0.05164257,0.0442881,0.02101595,-0.0038374,0.02504927,0.00095625,-0.0496334,-0.05851619,0.05509079,-0.01457579,-0.01760629,0.03497503,0.10816535,-0.06034367,0.07796315,-0.00511122,-0.0370726,-0.00569711,-0.00715542,-0.00055984,0.06525315,0.00287338,0.08919886,-0.02077761,-0.04157106,0.0219725,0.05722245,0.00171992,-0.03513071,0.0059074,0.00344177,0.00922082,-0.05287126,-0.05812883,0.03639418,-0.0142385,-0.02318406,-0.05255866,0.00564883,-0.02790964,-0.08438306,-0.00706042,0.00501707,-0.04769689,-0.01816947,0.1103125,-0.01668504,0.03747797,-0.02082244,-0.05773455,-0.06455041,0.03862162,0.00497134,-0.00043809,0.05032824,0.04407234,0.05588012,0.03674534,-0.09036057,0.04609752,0.04493741,-0.03476731,-0.07377478,0.1068574,0.01626601,-0.12607141,-0.02695067,-0.02282427,0.01592153,0.03588799,-0.01156141,-0.05021079,0.01284815,-0.02982899,0.09122679,0.02071613,-0.01299774,0.01730389,0.02239056,0.05700615,0.07272049,-0.03499668,-0.02259446,0.00760302,0.01665818,-0.07094181,0.03584512,0.01534185,0.01090542,0.01481257,-0.0730584,0.05332975,0.0163049,-0.00747888,0.06257504,-0.03085478,0.01336372,0.01382018,0.00496495,-0.04105281,0.03901019,0.13426006,0.01624795,-0.00185021,-0.06748115,0.00915109,-0.03584192,0.02441509,0.04140154,0.04119053,-0.05325339,-0.01776717,0.05848014,0.05061811,-0.0041762,0.07132583,-0.01646544,-0.00840385,-0.01537547,0.02719006,0.00390236,0.04128888,-0.0193901,-0.20903176,-0.00729671,0.00307786,-0.12712093,0.00297121,0.0002558,0.04347704,-0.04546568,0.0382599,0.0498474,0.06662978,0.00087456,-0.00917635,-0.00393767,-0.01808657,0.00541767,-0.01584855,-0.03262872,-0.01434252,0.03206582,0.00661164,-0.03133356,-0.00448644,-0.06676522,-0.0015386,-0.01329179,0.1717739,0.03056563,0.03128972,-0.03604419,0.03260806,-0.01727677,0.00114487,-0.19299343,-0.00399733,0.04958813,-0.02627382,-0.05977345,-0.03064295,-0.01107272,0.04030241,0.01051304,-0.02091322,-0.10494008,-0.0000342,-0.04801219,0.0167558,0.00910739,-0.04228166,0.04059925,0.00083376,0.03766511,0.00498824,0.03378884,0.0120965,-0.03656301,-0.02887805,0.06441659,0.05316065,-0.04118321,0.07788712,-0.04046834,-0.01010366,0.00659655,0.06731499,-0.05922959,-0.02012177,-0.04347977,-0.02225051,-0.06408442,0.03244332,0.03450478,-0.0299868,-0.00837183,0.03275221,0.03736326,-0.01515294,-0.02257858,0.00355191,-0.05129211,0.0188636,-0.04896038,0.01097631,-0.00190627,-0.0081355,0.05330395,0.02290163,0.00435689,0.0411926,-0.06209858,-0.04599514,-0.03921575,-0.03543607,-0.00328511,0.08183035,0.04258735,-0.2230348,0.01165494,0.04301786,-0.02121318,-0.00487361,0.07514566,0.06713223,0.02190449,-0.09291074,-0.01517777,-0.01722565,0.0191934,-0.00143657,0.02998097,0.04544367,0.03268177,0.06919232,-0.03562548,0.04795332,-0.09535684,0.03818199,0.05104987,0.20004246,0.01337761,-0.0156852,0.01779757,-0.00517869,0.00559627,0.06511313,-0.0258948,0.00595093,0.02101099,0.11444442,-0.00161048,-0.01765203,0.05082149,-0.01926371,-0.02759014,0.01663429,0.00421554,-0.04631796,-0.0187798,-0.00517543,-0.00369844,0.03361009,-0.03333186,-0.06808382,-0.01248935,-0.00710119,-0.08151141,0.0448015,-0.06948034,0.01880928,0.02240457,0.05653539,0.0683651,0.05591974,-0.00825434,-0.09998796,-0.00953424,0.01946811,0.02375466,0.04344877,-0.01775549,0.00288713]}},"text":null,"length":0,"last_read":{"hash":"53954b0e86f21b5d082cc2423b3cfce77d05804743f5fee4986e05ef17dfffa4","at":1736406354698},"key":"notion/au21-project/Devops/Versioning-workflow.md#Versioning workflow#{1}","lines":[3,5],"size":41,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Versioning requirements": {"path":null,"last_embed":{"hash":"c7741bb6610b33893add30670ceb500e192233b03e088bd4afd91468e66a51ea","tokens":334},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08394345,-0.0398393,0.00005211,-0.03839855,-0.04472357,-0.01327098,-0.1209449,0.00851204,-0.01272322,-0.01328239,0.02437137,-0.02311158,0.04014266,0.03807838,0.01127928,0.00920716,-0.02822379,0.05343884,-0.00308287,-0.01717892,0.0661241,-0.02514828,0.0522635,-0.01683636,0.00749995,0.0701274,-0.00845346,-0.0266776,-0.00344159,-0.29243836,0.01307517,0.04030018,-0.01959284,0.05195823,-0.03854335,0.0121265,-0.03889945,0.0412887,-0.02867709,0.07457702,0.01498851,0.04259333,-0.03132121,-0.02541049,-0.0571854,-0.03695647,-0.03831312,0.01608891,-0.00404624,-0.01121834,0.01603737,-0.01328022,-0.03967222,0.06670318,-0.00581965,0.03374422,0.0464238,0.08254355,0.0284123,-0.00869127,0.06564625,-0.00121884,-0.17706314,0.09650646,0.03559382,0.03143536,0.0034043,-0.05931953,0.02489931,0.05128413,-0.04897122,0.02542968,-0.0283642,0.06477373,0.00286976,0.02299121,0.00331367,0.00011588,0.03640526,0.01158453,-0.04962908,-0.01906471,-0.05280025,-0.02907406,0.03079829,-0.07137086,0.0439631,0.02141337,0.03768334,-0.03164463,-0.03848605,-0.04977853,0.06900641,0.01435445,-0.01941533,0.03572283,0.06734002,0.02677386,-0.05283622,0.12211541,0.00281874,0.0180224,0.03511887,0.03093039,0.03636342,0.01432582,-0.06256802,-0.06178506,0.01110985,-0.02301886,0.02976295,0.04303536,0.07730775,-0.03327165,0.03519494,0.00631652,-0.03649058,-0.01216563,0.0339376,0.00189394,0.06710014,0.00577122,0.09463511,-0.04507848,-0.00024539,0.03288459,0.03014564,-0.00453737,0.00700037,0.04335999,-0.00009209,-0.01844178,-0.07412009,-0.04608867,-0.02152566,-0.00739355,0.02542542,-0.02403296,0.00035477,-0.02772021,-0.05286952,-0.02008791,0.04640469,-0.02043385,-0.00304744,0.13740897,-0.01404033,0.06139527,-0.05915244,-0.0942957,-0.02745257,0.04712223,-0.07335263,-0.00812107,0.04211878,0.01552486,-0.00800434,0.02927228,-0.08924834,0.05779585,0.03980863,-0.01118224,-0.02787925,0.13159716,0.01993616,-0.05223049,-0.070489,-0.00304907,0.03309871,-0.02749788,0.00917555,0.01406763,0.01940598,-0.00732588,0.08749617,-0.02539539,-0.02508388,0.00232995,0.01407375,0.07816894,0.08698299,-0.04577278,-0.05728408,0.03419694,0.01285971,-0.09910063,-0.00020724,0.0057922,0.03157537,0.04919647,-0.06564716,0.04753977,0.06966259,0.0493491,0.05766108,-0.03725827,-0.00565784,0.0071544,0.03041835,-0.06298625,0.05735838,0.11706129,0.01653559,-0.01060774,-0.110773,-0.02896731,-0.0469101,-0.06029646,-0.00183932,0.01324594,-0.09292319,-0.03443445,0.05994395,0.02001502,-0.03563438,0.03132064,0.00970384,0.0032974,0.04107374,0.03561528,-0.00916191,0.02787993,0.00838575,-0.19471973,-0.00335153,0.02712708,-0.05025748,-0.00512896,-0.06233208,0.03674298,-0.05756142,-0.00999153,0.05859293,0.03844259,-0.02115708,-0.05679381,0.00316146,-0.03764281,0.01018879,-0.03387134,-0.01208321,-0.01183705,-0.00874742,-0.03265976,-0.01352226,-0.07295927,-0.07325039,0.07817636,-0.03967061,0.12916055,0.00137253,-0.0029684,-0.04554104,0.0094828,0.03724362,-0.00851667,-0.11367541,0.02470569,0.02779933,-0.05592927,0.00088359,0.0331305,0.0003096,0.04404764,-0.01099464,-0.01338862,-0.11673351,-0.0122312,-0.01340024,-0.04596808,-0.0064935,0.00784983,0.02199689,-0.00430773,0.02641557,-0.01090899,0.01545149,-0.0226868,-0.02067128,-0.06252971,0.05106628,0.03346914,-0.02954552,0.04458923,-0.00716164,-0.00029942,0.01388681,0.09921481,-0.02366282,-0.01674546,-0.05016325,0.02135547,-0.08001292,0.01661981,0.08892632,0.00124968,-0.01506343,-0.02149954,0.03888569,-0.03408765,-0.03405685,-0.01017523,-0.00661017,0.01402823,-0.04467207,0.00515795,0.02267571,-0.01079149,0.06926481,-0.00554719,0.00562887,-0.02785756,0.0095311,-0.01590377,-0.02041281,-0.01922419,-0.01836347,0.10338136,-0.01923797,-0.19597423,0.02100106,0.04947235,-0.00923796,-0.02292238,0.04574158,0.05318953,-0.0051113,-0.06862877,-0.00744061,0.01490508,0.01150958,0.01908698,-0.00818583,0.00586393,0.02868121,0.07111643,-0.04674694,0.04498931,-0.09947111,0.06624456,0.04513378,0.20505159,-0.01408753,-0.01486582,-0.01109004,-0.02321206,0.06190128,0.00923769,0.01459485,-0.04436925,-0.0133503,0.16119403,0.01354385,-0.00692696,0.06419748,-0.03768255,-0.04322927,0.00860164,0.0617069,-0.04767307,-0.00641176,-0.04763367,0.04035876,0.0791242,-0.02607975,-0.05180011,-0.01805062,-0.02940685,-0.03868392,-0.01692094,-0.04106534,0.0096231,0.01960582,0.06828404,0.06640431,0.01743568,0.01122401,-0.12118893,0.06168591,0.00626514,0.04013935,0.04365002,0.04732263,-0.08434445]}},"text":null,"length":0,"last_read":{"hash":"c7741bb6610b33893add30670ceb500e192233b03e088bd4afd91468e66a51ea","at":1736406354704},"key":"notion/au21-project/Devops/Versioning-workflow.md#Versioning requirements","lines":[6,26],"size":1217,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Versioning requirements#{1}": {"path":null,"last_embed":{"hash":"b830e6dce1527203633d77ebcfc4cfb5b68dd25e0728a4a9e8c23651ef8da022","tokens":334},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08539803,-0.03984854,0.00035787,-0.03808645,-0.04559009,-0.01339873,-0.12097366,0.00695968,-0.01268319,-0.01149945,0.025715,-0.02384225,0.03947165,0.03816128,0.0130979,0.01118288,-0.02714892,0.05490211,-0.00093275,-0.01463187,0.0653415,-0.02245459,0.05434262,-0.01842985,0.00595894,0.06760544,-0.00949315,-0.02467607,-0.00401484,-0.29326954,0.01551749,0.03607625,-0.01938185,0.05095626,-0.03966406,0.01285166,-0.04090384,0.03867199,-0.0260275,0.07502128,0.0148661,0.04383162,-0.03116944,-0.02701127,-0.05713435,-0.03787198,-0.03856957,0.01653672,-0.00042627,-0.01316589,0.01810978,-0.01081604,-0.04071663,0.0640625,-0.00647323,0.03369021,0.04835904,0.08396291,0.02719314,-0.00615638,0.06395385,-0.00041685,-0.17652713,0.09465683,0.03639528,0.03124191,0.00530671,-0.0578716,0.02405793,0.05400117,-0.04982383,0.02454534,-0.03065521,0.06625823,0.00091939,0.02089799,0.00251307,0.0006632,0.03436064,0.01088838,-0.05187894,-0.02035808,-0.05179428,-0.03095269,0.03185595,-0.07151657,0.04653121,0.01918895,0.03726733,-0.03005737,-0.03654097,-0.04633208,0.06809815,0.01394943,-0.01941573,0.03595481,0.066796,0.03044225,-0.05487712,0.12313345,0.00737235,0.01573898,0.03445113,0.03152896,0.0363875,0.01501512,-0.06251512,-0.0615644,0.01000293,-0.02476731,0.02920933,0.04174498,0.07528415,-0.03177661,0.03371811,0.0064904,-0.03467494,-0.01344166,0.03897377,0.00042927,0.06670818,0.00675593,0.09255102,-0.04571238,0.00090282,0.03272739,0.02868184,-0.00368641,0.00981552,0.04538997,-0.00022203,-0.02038853,-0.07414332,-0.04543414,-0.02253394,-0.00836587,0.02474714,-0.02374591,0.00066186,-0.02777687,-0.05245455,-0.02329716,0.05074267,-0.01801901,-0.00124219,0.13675824,-0.01317152,0.06176001,-0.06090501,-0.09437239,-0.02746195,0.04659946,-0.0756811,-0.00990714,0.04259377,0.01349804,-0.01027942,0.02668698,-0.08928523,0.05856399,0.03974678,-0.01052939,-0.0242373,0.13398086,0.02078343,-0.0504953,-0.07021656,-0.00140394,0.03693989,-0.0303653,0.01071232,0.01632222,0.01974476,-0.00571359,0.08872081,-0.02696994,-0.02670801,0.00557493,0.0083099,0.07627463,0.08813109,-0.04524117,-0.05827686,0.03590708,0.01229018,-0.10126828,0.00091952,0.00460429,0.03145748,0.04847435,-0.06437885,0.04835556,0.06945131,0.04916954,0.05623761,-0.03845722,-0.00886208,0.00820092,0.03361832,-0.06458573,0.05891323,0.11426919,0.01658035,-0.01093403,-0.10949067,-0.03004251,-0.0460172,-0.05993779,-0.00301609,0.0108362,-0.09299605,-0.03514937,0.05889934,0.01821338,-0.03650142,0.02931282,0.00987044,0.0039672,0.04322186,0.0369949,-0.01016058,0.0280733,0.00744085,-0.19442403,-0.00106829,0.02570848,-0.05022781,-0.00619533,-0.0624468,0.03560814,-0.05699557,-0.01108115,0.05774466,0.03608897,-0.02272445,-0.0586508,0.00439705,-0.03941278,0.01056004,-0.03277123,-0.01205557,-0.01172201,-0.0097959,-0.03490694,-0.01071487,-0.07342645,-0.0691479,0.08180038,-0.03776067,0.1261313,0.00180844,-0.00349071,-0.04604909,0.00821581,0.03763523,-0.00966246,-0.11331352,0.02466525,0.02724347,-0.05372587,0.00194725,0.03497725,-0.00045719,0.04328517,-0.01050831,-0.01335204,-0.11501154,-0.01201953,-0.01198567,-0.04443631,-0.0085778,0.00897613,0.02101787,-0.0043136,0.02562532,-0.01003879,0.01751088,-0.02567907,-0.01957827,-0.06332994,0.04876497,0.03480286,-0.02963612,0.04424866,-0.00690513,-0.00193711,0.01174827,0.09832224,-0.02385061,-0.0177748,-0.0508478,0.02138614,-0.07978392,0.01469287,0.08797751,0.00166386,-0.017015,-0.02653028,0.03601634,-0.03364005,-0.03404551,-0.00987653,-0.00417977,0.01566501,-0.04262445,0.00533882,0.02414345,-0.01140653,0.06727809,-0.00619466,0.00703746,-0.02930276,0.01230483,-0.01434525,-0.02143389,-0.01899095,-0.01752478,0.10660152,-0.02465151,-0.19572832,0.02211614,0.04983126,-0.00619754,-0.02275408,0.04320746,0.05073211,-0.00241815,-0.06936178,-0.00462295,0.01631838,0.00906771,0.01943674,-0.00860101,0.00558257,0.02851989,0.07040307,-0.0458027,0.04431839,-0.10127306,0.06450299,0.04412783,0.20439345,-0.01576798,-0.01342808,-0.00964941,-0.02274104,0.06295456,0.00651741,0.01787798,-0.04551565,-0.01530342,0.16045639,0.01334084,-0.00541127,0.06697945,-0.03887546,-0.04529712,0.00941965,0.0632525,-0.04564745,-0.00530392,-0.04662603,0.0414323,0.08368021,-0.02450963,-0.05100372,-0.01865283,-0.03017333,-0.03661823,-0.01954817,-0.04040389,0.00810565,0.01782062,0.06771161,0.0648603,0.01639448,0.01111273,-0.12026403,0.0617498,0.00758718,0.04128481,0.04264981,0.04934505,-0.08415309]}},"text":null,"length":0,"last_read":{"hash":"b830e6dce1527203633d77ebcfc4cfb5b68dd25e0728a4a9e8c23651ef8da022","at":1736406354721},"key":"notion/au21-project/Devops/Versioning-workflow.md#Versioning requirements#{1}","lines":[8,26],"size":1190,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes": {"path":null,"last_embed":{"hash":"8ae8356a18d7ae5071a9c20c8bef5ae6f8da976f1ba2d9cfd209c00e8514f460","tokens":406},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04095038,-0.06184375,0.00404697,0.0127323,0.03152603,-0.02364487,-0.0346477,0.00826242,0.01316289,-0.04632284,0.01264445,-0.00821329,0.01361758,-0.00306073,0.03720161,0.01806657,-0.03515058,0.07254487,-0.03747091,-0.03787338,0.09028698,-0.02345615,0.03197895,-0.01271272,0.02240648,0.04398148,0.01027544,-0.04769992,-0.03413304,-0.2077975,-0.05117125,0.00148515,-0.03285594,0.06597711,-0.03955197,0.04188273,-0.03218138,0.02782998,-0.05070515,0.05759658,0.063053,0.00747571,-0.02211406,0.01096038,-0.04557212,-0.02397912,-0.04218855,-0.02763686,-0.05331808,-0.0009675,0.00669561,-0.06172533,-0.03722678,0.04833337,-0.0136429,0.06163944,0.03289884,0.0415213,0.02698882,-0.02979593,0.0879383,0.00667531,-0.1879354,0.08781131,0.04468578,0.02966816,-0.02571139,0.01985887,0.05608569,0.06903262,0.0217677,0.03464977,-0.04334701,0.087883,-0.00858946,0.02894319,-0.00433024,0.04467724,0.01315584,0.00148576,-0.03831129,0.03585814,-0.0300734,0.00781385,-0.02478871,-0.02314587,0.03541639,-0.01433085,0.08400042,0.01731599,-0.00865106,-0.08095907,0.04811804,0.0057614,-0.04748474,0.02454716,0.02920412,0.02815201,-0.05189536,0.10552083,-0.08479115,0.02933492,0.05338826,0.03781981,0.07775207,-0.01912979,-0.00532498,-0.06957965,0.0326482,0.0036719,-0.00526898,0.03057006,0.04046443,-0.08239207,0.07227904,-0.01064639,0.00774595,-0.01473432,0.01092616,0.02063071,0.02423151,-0.00172491,0.07885288,-0.02309676,0.0004937,0.01872763,0.01584169,-0.01465147,0.01115702,0.01766291,0.04846255,0.04993594,-0.114029,0.03072336,-0.01776254,-0.03026965,0.06012147,-0.05976218,-0.03796838,-0.05495126,-0.02418304,0.03787473,0.00618487,-0.05346049,-0.08772099,0.11604467,-0.02286333,0.08679963,-0.07929293,-0.05295632,0.00700574,0.0385577,-0.03826398,-0.00585823,0.01440487,0.05285657,0.04609011,0.05013187,-0.02417155,0.017417,0.03460366,-0.04519589,-0.06764919,0.0856731,0.02390298,-0.09623808,-0.05875188,-0.05009827,0.03227758,-0.02447779,-0.0071522,0.02874926,0.03835227,-0.00458062,0.03441713,-0.01266583,0.02078633,-0.01614539,0.03839333,0.09913276,0.06783089,-0.01347566,-0.01669911,0.03848285,-0.0122184,-0.06751051,-0.02463085,-0.00801645,0.00800429,0.01437624,-0.05956039,0.01911058,0.06773336,-0.01843916,0.03799038,0.00905868,0.01324401,-0.01306071,0.00332061,-0.04763559,0.07324534,0.08653263,0.00589596,0.01083008,-0.06703121,-0.03949442,0.01640239,-0.08134268,-0.01189795,0.02990591,-0.02702904,0.00201325,0.01794259,0.02651653,-0.08182782,-0.02447698,-0.05044235,0.05826334,0.01555191,0.05101634,-0.03269491,-0.00247351,-0.03658321,-0.18525277,0.00403653,0.05824127,-0.05589899,0.05431738,-0.04366019,0.05634037,-0.02652447,-0.00664189,0.03838047,0.08930292,-0.05037597,-0.06876487,-0.01440303,0.000261,0.05561269,-0.08058882,0.01417849,0.00898174,0.02105201,-0.04260619,-0.03009463,-0.05915949,-0.0265968,0.01171723,-0.02295482,0.15975945,0.0543251,-0.01121575,0.00390297,0.02024937,0.00199235,0.03676156,-0.17847937,0.02649397,0.03626567,-0.06442517,0.00728104,0.04899911,-0.00384166,0.00139382,-0.00596536,-0.00182971,-0.09141941,-0.01851338,-0.01772492,-0.03182259,-0.06966005,0.01295041,-0.03342121,0.02950979,0.01859663,-0.00673134,0.0030539,0.01628602,-0.03926992,-0.01597125,0.01634267,0.0369215,-0.02614048,0.01298753,-0.01428374,-0.00847822,0.00382815,0.07101288,-0.02495653,-0.03227972,-0.0162539,-0.03299138,-0.07158737,0.05552176,0.12146167,0.03413777,-0.04949563,0.00854677,0.03535549,-0.0362983,-0.00802288,-0.05935969,-0.01131008,-0.0375413,-0.03400699,0.02774181,-0.03881483,-0.03090051,0.06030694,0.0136687,-0.04832734,0.02409847,-0.00613888,-0.03528633,0.02415663,-0.0174459,-0.02453951,0.07110575,-0.03575073,-0.22809246,0.08616199,0.06095332,-0.08150999,0.00188624,0.04022425,0.07657906,-0.03015605,-0.08046339,0.0245571,0.05227657,0.04239145,0.00015366,-0.02625258,-0.00346144,0.05720884,0.05770723,-0.06225049,0.03757662,-0.05782495,0.04528943,0.0457478,0.21596614,-0.03773133,0.01815346,0.00483091,-0.06008964,0.06691273,0.07294147,-0.00258867,-0.02534566,0.01798083,0.09368625,-0.01529924,0.00606203,0.07070585,0.02771141,-0.03961616,-0.00404846,-0.00099408,-0.01665083,0.00111047,-0.03223056,0.00005635,0.07112567,-0.10325203,-0.05027991,-0.04771109,-0.02173223,-0.04625209,-0.00671469,-0.03706785,-0.03504867,-0.00273567,0.12200177,0.03298065,0.03648887,0.03361437,-0.13333549,0.01962034,-0.00227344,-0.02696,0.04632943,0.0226192,0.02091214]}},"text":null,"length":0,"last_read":{"hash":"8ae8356a18d7ae5071a9c20c8bef5ae6f8da976f1ba2d9cfd209c00e8514f460","at":1736406354739},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes","lines":[27,237],"size":8217,"outlinks":[{"title":"Gitlab versioning examples and libraries","target":"https://www.notion.so/Gitlab-versioning-examples-and-libraries-5bf9990f00064bd4a6ecd6066b0f315a?pvs=21","line":5},{"title":"https://git-scm.com/book/en/v2/Git-Basics-Tagging","target":"https://git-scm.com/book/en/v2/Git-Basics-Tagging","line":9},{"title":"https://axion-release-plugin.readthedocs.io/en/latest/configuration/ci_servers/#gitlab-ci","target":"https://axion-release-plugin.readthedocs.io/en/latest/configuration/ci_servers/#gitlab-ci","line":33},{"title":"https://github.com/jozala/blog-post-axion-release/blob/master/build.gradle","target":"https://github.com/jozala/blog-post-axion-release/blob/master/build.gradle","line":35},{"title":"https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Gradle.gitlab-ci.yml","target":"https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Gradle.gitlab-ci.yml","line":37},{"title":"https://gitlab.com/gitlab-org/gitlab/-/tree/master/lib/gitlab/ci/templates?_gl=1*10mhcse*_ga*NzQzOTM0NTIzLjE2ODIxMDkxOTE.*_ga_ENFH3X7M5Y*MTY4Mjk5MDEzMi4xNi4xLjE2ODI5OTE5NjQuMC4wLjA","target":"https://gitlab.com/gitlab-org/gitlab/-/tree/master/lib/gitlab/ci/templates?_gl=1%2a10mhcse%2a_ga%2aNzQzOTM0NTIzLjE2ODIxMDkxOTE.%2a_ga_ENFH3X7M5Y%2aMTY4Mjk5MDEzMi4xNi4xLjE2ODI5OTE5NjQuMC4wLjA","line":39},{"title":"https://about.gitlab.com/blog/2017/11/02/automating-boring-git-operations-gitlab-ci/","target":"https://about.gitlab.com/blog/2017/11/02/automating-boring-git-operations-gitlab-ci/","line":43},{"title":"https://marcosschroh.github.io/posts/autobumping-with-gitlab/","target":"https://marcosschroh.github.io/posts/autobumping-with-gitlab/","line":44},{"title":"https://forum.gitlab.com/t/is-it-possible-to-commit-artifacts-into-the-git/22218/7","target":"https://forum.gitlab.com/t/is-it-possible-to-commit-artifacts-into-the-git/22218/7","line":45},{"title":"https://threedots.tech/post/automatic-semantic-versioning-in-gitlab-ci/","target":"https://threedots.tech/post/automatic-semantic-versioning-in-gitlab-ci/","line":46},{"title":"https://quarkus.io/blog/*********************quarkus-with-gitlab/","target":"https://quarkus.io/blog/*********************quarkus-with-gitlab/","line":47},{"title":"https://blog.jdriven.com/2021/11/reuse-gradle-build-cache-on-gitlab/","target":"https://blog.jdriven.com/2021/11/reuse-gradle-build-cache-on-gitlab/","line":48},{"title":"https://neron-joseph.medium.com/tag-your-git-repo-using-gitlabs-ci-cd-*********************","target":"https://neron-joseph.medium.com/tag-your-git-repo-using-gitlabs-ci-cd-*********************","line":52},{"title":"https://dev.to/vumdao/config-gitlab-runner-to-push-a-tag-3l5g","target":"https://dev.to/vumdao/config-gitlab-runner-to-push-a-tag-3l5g","line":53},{"title":"https://github.com/semantic-release/gitlab","target":"https://github.com/semantic-release/gitlab#readme","line":133},{"title":"https://github.com/semantic-release/semantic-release","target":"https://github.com/semantic-release/semantic-release","line":134},{"title":"https://github.com/npm/node-semver","target":"https://github.com/npm/node-semver","line":139},{"title":"Screenshot 2023-04-28 at 10.36.02 AM.png","target":"Versioning-workflow/Screenshot2023-04-28at10.36.02AM.png","line":145}],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Libraries": {"path":null,"last_embed":{"hash":"f95828ab391b59b28b088aa65585c3ab7d69117755669445f313a1c75257cfc3","tokens":86},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03704156,-0.04475657,0.02742677,-0.00846092,-0.01423817,-0.02419881,-0.08814081,0.02225804,0.00692412,-0.03861382,-0.01059315,-0.02746615,0.01801459,0.00818074,0.03291626,-0.00721162,0.00113833,0.06813248,-0.04361928,0.01956358,0.11360418,-0.01437684,0.02054688,-0.02645501,0.01881415,0.05313346,0.02510961,-0.01954902,-0.00649494,-0.19339041,-0.08246095,0.0429893,-0.0446807,0.05208447,0.00179189,0.01539874,-0.03213309,0.04063912,-0.09615652,0.06345287,0.06187911,0.04233027,-0.02832313,0.01593145,-0.06583568,-0.04193165,-0.06479307,0.03129898,-0.032711,-0.02794202,0.00099295,-0.04731368,-0.01978039,0.04262861,-0.02305371,0.01989549,0.0382777,0.05866884,0.02932474,-0.00396412,0.08646972,-0.00887063,-0.2288069,0.08727477,0.0087491,0.06119794,-0.00920328,-0.04283902,0.05704258,0.04353807,-0.02312234,0.02756782,-0.01147441,0.08462912,0.00822482,0.05944079,-0.00454744,0.00130247,0.02725902,-0.0110234,-0.02335898,-0.0044326,0.01163092,0.04030087,-0.00028359,-0.01425522,-0.00896889,0.01940309,0.0992961,-0.01851939,-0.04647658,-0.07300253,0.08324938,0.0153288,-0.06382507,0.04986325,0.04535066,-0.003298,-0.01992912,0.12721802,-0.04924005,0.01520086,0.0634945,0.02791224,0.03756794,-0.00911517,-0.01102176,-0.08215536,0.07288541,-0.01493193,0.00154696,0.00209267,0.05663742,-0.05282507,0.05894368,-0.02320584,-0.01190622,-0.0218107,-0.00743683,0.01554709,0.01684368,-0.02953336,0.08839646,-0.00074559,0.00484403,-0.00990807,0.00695868,-0.01265862,-0.01932958,0.02874595,0.00883328,0.03231999,-0.0606404,-0.04328394,-0.00629447,-0.01814113,0.03696486,-0.05082664,-0.02016512,-0.01164078,-0.04081258,0.02049937,0.00548874,-0.02934341,-0.05914254,0.11916717,-0.02300559,0.07502389,-0.05143031,-0.02644306,0.02586496,0.03194163,-0.02683178,0.02359852,0.03730664,0.0616601,0.03188208,0.05559164,-0.05935874,0.01754638,0.04637305,-0.02062102,-0.07370552,0.10311826,0.00367455,-0.07960769,-0.05806289,-0.03935483,-0.00222089,0.03171798,-0.00148283,-0.00570854,0.03473105,-0.00373142,0.07466333,0.0231498,0.02485595,0.01048984,0.03998179,0.08170336,0.07683171,-0.01708212,-0.03847542,0.01663883,0.00819569,-0.06091138,0.00440669,0.01029706,0.0321881,0.04165037,-0.07978342,0.02205093,0.0518224,-0.03203532,0.03753236,-0.01355585,0.01105591,-0.01126521,-0.00829012,-0.03425283,0.08687218,0.13869791,0.00309388,-0.00084535,-0.06358435,0.00668298,0.0077022,-0.04963085,0.01824214,0.03414151,-0.04416854,-0.02642351,0.04679093,0.05646623,-0.05551545,0.00219807,-0.02350206,0.01689734,-0.01406367,0.04189035,0.00466995,-0.04510422,-0.0325188,-0.21007511,-0.00090825,0.01011579,-0.11847328,0.03243522,-0.02774682,0.01809607,-0.05203102,-0.01390899,0.05654165,0.06766084,-0.01315373,-0.04326629,-0.01530881,-0.01693623,0.00497712,-0.07313935,-0.02253233,0.00883294,0.02936363,-0.01801345,-0.02466844,-0.01118935,-0.04579824,0.03019799,-0.03419849,0.16747519,0.02653134,0.032015,0.01206819,0.00839921,-0.00721953,0.00399326,-0.16347535,0.01197228,0.05436703,-0.04648246,0.00687087,0.04693082,-0.03123983,0.01695519,0.04377727,-0.03671525,-0.09925423,-0.05164023,-0.03992394,-0.04670057,-0.02487715,-0.00362566,0.02074756,-0.01797927,0.03912307,-0.01699502,0.01065768,0.0227416,-0.05464322,-0.05398099,0.06263933,0.04606128,-0.01814422,0.05629081,-0.04157363,-0.01405432,0.00372922,0.05224707,-0.04512057,-0.03032469,-0.02715164,-0.02260035,-0.04770014,0.04072533,0.09191807,-0.00486028,-0.03632383,-0.01622991,0.06071943,0.0049365,-0.03951572,-0.02375999,-0.02081705,-0.0579311,-0.01993966,0.00715631,-0.04906561,-0.0161677,0.05424337,0.01041761,-0.02625774,0.03617239,-0.03708163,-0.06444448,0.00068658,-0.01945437,0.00177649,0.0684092,0.01074934,-0.2229315,0.03160866,0.04807556,-0.05910165,-0.0231371,0.07754704,0.04997972,-0.03060631,-0.10198034,-0.01774392,0.02372569,0.00307322,0.01738469,-0.01874334,0.03173275,0.02486339,0.09295419,-0.05100211,0.06645583,-0.0915826,0.0729929,0.05711481,0.21442264,-0.01044186,-0.00833216,-0.00934293,-0.02669666,0.03199949,0.05112834,-0.00807838,-0.02239019,0.01323729,0.10136753,0.00111928,-0.02990064,0.10349894,0.00571373,-0.04141115,0.02613071,0.01207882,-0.03121989,-0.00439846,-0.05081755,0.01152452,0.08675142,-0.04334186,-0.04439301,-0.04218779,-0.0280418,-0.05993292,0.02192614,-0.05679756,-0.01369906,-0.01265004,0.09807977,0.05491671,0.03091515,0.003951,-0.12723805,-0.00135141,0.00403968,-0.00734522,0.03944933,0.00047301,0.01388507]}},"text":null,"length":0,"last_read":{"hash":"f95828ab391b59b28b088aa65585c3ab7d69117755669445f313a1c75257cfc3","at":1736406354762},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Libraries","lines":[29,32],"size":161,"outlinks":[{"title":"Gitlab versioning examples and libraries","target":"https://www.notion.so/Gitlab-versioning-examples-and-libraries-5bf9990f00064bd4a6ecd6066b0f315a?pvs=21","line":3}],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Libraries#{1}": {"path":null,"last_embed":{"hash":"be02097ff8232815e64d64932e58481204d3a3a9dd13b84c125103e4055f6666","tokens":85},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03302983,-0.04283954,0.02892641,-0.00819802,-0.01168991,-0.02263643,-0.08987253,0.01800706,0.00400078,-0.03855101,-0.00838512,-0.02944706,0.02187019,0.0112653,0.03453949,0.0009727,0.00575033,0.06935816,-0.04323027,0.01820686,0.11452649,-0.01361872,0.02018833,-0.0257013,0.02097177,0.04834297,0.02363108,-0.01764707,-0.00374499,-0.19086796,-0.08373844,0.039283,-0.04548704,0.05186824,-0.00044745,0.01467025,-0.03220698,0.04160199,-0.09805103,0.06009589,0.06224731,0.04410073,-0.03103026,0.01876222,-0.06972323,-0.0395637,-0.0658183,0.03231476,-0.03377315,-0.02830139,0.00336142,-0.04369847,-0.01646427,0.04569141,-0.02620922,0.02170675,0.03625123,0.0639703,0.02925243,-0.00293261,0.08504226,-0.0076133,-0.22628638,0.088315,0.00857737,0.06467069,-0.0097407,-0.04311518,0.06145301,0.04273165,-0.02382646,0.02516344,-0.00927909,0.08528522,0.00925195,0.06104639,-0.00285066,0.00522173,0.02432268,-0.00995737,-0.02305222,-0.00445303,0.01611897,0.03576533,-0.00305673,-0.01613256,-0.00772898,0.02018478,0.10200964,-0.01724151,-0.04783221,-0.07120951,0.08422794,0.01241797,-0.0647421,0.04922627,0.04107508,-0.00012129,-0.01295166,0.12612285,-0.04875363,0.01867862,0.06156408,0.02844924,0.03724134,-0.01207657,-0.00818197,-0.08118152,0.07069061,-0.01734749,-0.00211091,0.00211745,0.05819168,-0.05214422,0.06126393,-0.02250239,-0.01227414,-0.02412524,-0.00771954,0.01377834,0.01388888,-0.03111751,0.08766395,-0.00208933,0.00161594,-0.01137616,0.00647432,-0.01071753,-0.02083873,0.03005804,0.00913526,0.03256574,-0.06191097,-0.04425371,-0.00550383,-0.01604617,0.03606017,-0.04710399,-0.0248148,-0.00840238,-0.04041443,0.01840763,0.00755227,-0.02585411,-0.06086462,0.11459118,-0.02429023,0.07745624,-0.05046954,-0.02637058,0.02941346,0.03455733,-0.02964324,0.02470547,0.03587683,0.06454659,0.02882016,0.05533526,-0.05814728,0.01604147,0.04639003,-0.01820976,-0.07486967,0.10219605,0.00194865,-0.07499054,-0.05546498,-0.0379178,-0.00223823,0.03652166,0.00092209,-0.00044201,0.03554024,-0.00314045,0.07214057,0.02596806,0.01932366,0.01554408,0.03547565,0.07824507,0.07644472,-0.0180564,-0.03796098,0.01483797,0.006348,-0.06354297,0.00835895,0.01366667,0.03150889,0.04368082,-0.08165854,0.02398166,0.05003017,-0.03365496,0.04162384,-0.01158083,0.01038465,-0.0141708,-0.00445394,-0.03815033,0.08829235,0.1400152,0.00304218,-0.00087108,-0.06586659,0.00171043,0.00998228,-0.05109712,0.01907333,0.0345481,-0.04542354,-0.02947786,0.04372814,0.05406411,-0.05372465,-0.00394832,-0.02673161,0.01493657,-0.011735,0.04421296,0.00541411,-0.04549099,-0.02803606,-0.21103789,0.00708985,0.00851843,-0.12112655,0.03121831,-0.02958362,0.0169581,-0.05188173,-0.01366653,0.0613271,0.06854974,-0.01719555,-0.0430162,-0.01721154,-0.01822293,0.00297463,-0.07280757,-0.02041693,0.00658901,0.02728065,-0.01899313,-0.02462162,-0.00785108,-0.04468852,0.02761531,-0.03104146,0.16421753,0.02477708,0.03667521,0.01225335,0.00625829,-0.00761209,0.00174237,-0.16300291,0.00833063,0.05245386,-0.04333389,0.00582377,0.04648061,-0.02952014,0.01467703,0.04464405,-0.03487315,-0.10351599,-0.04722377,-0.03837141,-0.04419405,-0.02007975,-0.00284745,0.0170106,-0.02027816,0.03724224,-0.01533348,0.0095996,0.01982985,-0.05803014,-0.05475553,0.06298439,0.04669416,-0.01638415,0.05866239,-0.04340885,-0.01925279,0.00165499,0.05456376,-0.04572238,-0.03157718,-0.03050849,-0.02192883,-0.04588345,0.03849775,0.09276094,-0.00638187,-0.03598742,-0.01769594,0.06415253,0.00821951,-0.0368603,-0.02379251,-0.02063836,-0.05943897,-0.01392084,0.00746698,-0.04810971,-0.01305395,0.04957375,0.01144485,-0.02642063,0.03725138,-0.03587446,-0.064617,-0.00037049,-0.0220785,0.00329733,0.06813134,0.00729513,-0.22343968,0.03435894,0.04798166,-0.05771521,-0.02314672,0.07493734,0.05200558,-0.03138819,-0.09915728,-0.01992875,0.02276107,0.00044006,0.01918733,-0.01929292,0.03418237,0.02369693,0.09362604,-0.05465064,0.06505113,-0.09034757,0.07507785,0.05506743,0.21327864,-0.01284697,-0.00621868,-0.01201364,-0.02670476,0.02993872,0.04580589,-0.01236399,-0.02314602,0.01011461,0.10185175,-0.00384744,-0.03259355,0.10765492,0.00622051,-0.04051999,0.02627197,0.01341357,-0.02867281,-0.00124965,-0.05488257,0.01291362,0.09127074,-0.04001018,-0.04513122,-0.04402764,-0.03097188,-0.05883585,0.02585916,-0.05682601,-0.01508761,-0.01721369,0.09486534,0.05238451,0.02778,0.00583228,-0.1316487,-0.00285434,0.00330058,-0.00760077,0.03778623,-0.00158777,0.01505399]}},"text":null,"length":0,"last_read":{"hash":"be02097ff8232815e64d64932e58481204d3a3a9dd13b84c125103e4055f6666","at":1736406354769},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Libraries#{1}","lines":[31,32],"size":147,"outlinks":[{"title":"Gitlab versioning examples and libraries","target":"https://www.notion.so/Gitlab-versioning-examples-and-libraries-5bf9990f00064bd4a6ecd6066b0f315a?pvs=21","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Git": {"path":null,"last_embed":{"hash":"22ab964e4b71030edcade29fa7de1fb14741e0190234a0b5bf37aa90f5bd0f43","tokens":244},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03604968,-0.06041949,-0.00094144,0.01906811,0.06153288,-0.01509671,0.01231267,0.00623853,0.02388551,-0.0524698,0.01267524,-0.00285736,0.00234428,0.00008544,0.02664469,0.0105298,-0.04722095,0.06828991,-0.0318199,-0.06158378,0.09757292,-0.01464241,0.0190588,-0.01155439,0.02961364,0.07067607,0.01377594,-0.0574319,-0.03962656,-0.19756675,-0.01211424,-0.0191503,0.00065707,0.05350141,-0.0260032,0.02040157,-0.00998889,0.04427905,-0.01205266,0.04035877,0.04039339,-0.03083851,-0.02143633,-0.00826936,-0.02056405,-0.02904943,-0.01933581,-0.04548242,-0.05455611,0.00600925,0.02827378,-0.07330333,-0.0338373,0.0318005,-0.01050898,0.08990782,0.03651252,0.02691951,0.01598017,-0.04060414,0.08511674,0.01746484,-0.16986015,0.05262527,0.04826193,0.01588166,-0.03814054,0.040256,0.02834535,0.08674516,0.02791709,0.03957376,-0.05849277,0.07812283,-0.01202885,0.01252485,0.00697295,0.04637055,-0.00029682,-0.0039851,-0.03504481,0.05203533,-0.05630735,-0.00285828,-0.04934485,-0.02256982,0.04457827,-0.03031006,0.06091318,0.01939392,-0.00131025,-0.06830271,0.02712195,0.01265239,-0.04602349,-0.00469312,0.02613257,0.0265164,-0.05443224,0.12414119,-0.10368639,0.03628121,0.0408284,0.03139669,0.08839731,-0.01052647,0.0009242,-0.04606367,0.01386825,0.02581109,-0.01996602,0.03180426,0.01410573,-0.08429018,0.05719194,-0.00802425,0.01695411,0.01291148,0.0294228,0.02267534,0.02878956,0.00015605,0.04669727,-0.04196989,-0.00213505,0.02452832,0.02776378,-0.00132483,0.03296302,0.01161577,0.0508908,0.02687351,-0.13473147,0.04950046,-0.01546503,-0.02075884,0.07274041,-0.03079364,-0.03605137,-0.0673922,-0.0062911,0.03277227,-0.00229861,-0.07252002,-0.0795963,0.13610031,-0.04258242,0.06332359,-0.06079752,-0.05178935,-0.02441842,0.01577064,-0.01486806,-0.01561692,-0.01960322,0.02991745,0.07600246,0.02857065,-0.01852133,-0.00008919,0.02939556,-0.04530967,-0.06598271,0.08835551,0.05840332,-0.09402508,-0.04916703,-0.05439086,0.04147774,-0.05989701,0.00361966,0.03830424,0.02488203,-0.0032843,0.03336617,-0.01542682,-0.00286216,-0.02373326,0.03206699,0.08230375,0.06197286,-0.02953103,-0.00594849,0.05843502,-0.0344593,-0.06207059,-0.05900422,-0.01925101,-0.00890327,0.01586919,-0.03939348,0.00836634,0.05643949,-0.00723853,0.00865829,0.01126049,0.01131864,0.00287398,0.00418655,-0.05452884,0.05428058,0.04141382,0.01590534,0.01940507,-0.04905015,-0.03844641,0.01890733,-0.06661202,-0.04118731,0.0277483,-0.02361861,0.02740281,0.02307982,0.00399917,-0.08473327,-0.02142169,-0.05810685,0.06979284,0.01361117,0.02255659,-0.04572142,0.03101733,-0.0475526,-0.18263894,-0.01172124,0.06195212,-0.0241438,0.04712598,-0.0432496,0.08308958,-0.00077616,0.01909671,0.03220176,0.10306149,-0.04551378,-0.07386855,-0.00847808,0.01831205,0.09313662,-0.05507884,0.00884599,0.02855477,0.01692961,-0.04160295,-0.02475179,-0.08166504,-0.0250638,0.01350355,-0.01097352,0.17208083,0.06738211,-0.02745417,-0.01722653,0.04337224,0.00191584,0.02412677,-0.16012989,0.04833282,0.0205731,-0.06228925,-0.0048292,0.05093374,0.00661833,0.00310322,-0.04301821,0.02333057,-0.0660997,0.00484497,-0.00617851,0.00116641,-0.08425438,0.02626013,-0.03750175,0.05709156,-0.01097341,-0.01608261,0.02095582,0.04070567,-0.0209043,-0.01910444,0.00477452,0.01552485,-0.01710498,-0.02258751,0.00657301,0.00494075,-0.00407895,0.06937186,-0.02689831,-0.02393412,-0.00081539,-0.03789578,-0.06677494,0.04588175,0.10988837,0.04297384,-0.05484616,0.02252806,0.00721567,-0.05346879,-0.00471942,-0.07439368,-0.01606383,-0.00095923,-0.0604476,0.04091156,-0.03459775,-0.00470719,0.07102083,0.01812474,-0.06158225,0.00723279,-0.01605899,-0.00961025,0.01714979,-0.03153512,-0.01512671,0.06357311,-0.05732914,-0.23457554,0.09935258,0.05876347,-0.04897832,0.00672768,0.01900181,0.09707295,-0.04110744,-0.08088922,0.04157631,0.02759972,0.05850302,0.01335189,-0.01397245,-0.04316651,0.07645137,0.03929755,-0.05047452,0.0324646,-0.03568435,0.03032805,0.02979572,0.21813953,-0.03023907,0.03494416,0.01131473,-0.05957919,0.05333077,0.09643526,0.01353835,-0.01113192,0.02029555,0.0894405,-0.03790561,0.04757292,0.03185172,0.05052395,-0.02005512,-0.02836182,-0.03915019,-0.02222892,-0.02763606,-0.02250776,-0.02864879,0.04994854,-0.12098013,-0.04634136,-0.06156586,-0.03875713,-0.03579522,-0.0471024,-0.03534023,-0.0399975,0.00540278,0.10857987,0.01881779,0.03945355,0.02802933,-0.09722614,0.02215663,0.00440368,-0.01381361,0.04737167,0.0442456,0.02300401]}},"text":null,"length":0,"last_read":{"hash":"22ab964e4b71030edcade29fa7de1fb14741e0190234a0b5bf37aa90f5bd0f43","at":1736406354776},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Git","lines":[33,47],"size":781,"outlinks":[{"title":"https://git-scm.com/book/en/v2/Git-Basics-Tagging","target":"https://git-scm.com/book/en/v2/Git-Basics-Tagging","line":3}],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Git#{1}": {"path":null,"last_embed":{"hash":"6954d68a7882540c8500ba0f13b5eaf17c72b080bd533776bbffd68cdf6eb49a","tokens":113},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03042868,-0.06038569,0.01853462,0.01317317,0.02990206,-0.01025446,-0.04415078,0.03845923,0.00135975,-0.0700397,-0.0084262,0.00758952,0.02387809,0.01984308,0.07646852,0.00662364,-0.01929202,0.08263251,-0.01890868,-0.03365371,0.08281005,-0.00896667,0.02276494,-0.04526883,-0.00714595,0.06004636,0.01314462,-0.04067992,-0.03129728,-0.17573501,-0.00402497,-0.02699207,-0.01710188,0.08100858,-0.01508883,0.01667387,0.0179037,0.01802503,-0.01929401,0.02943681,0.04288467,-0.02591029,-0.02313568,-0.01477654,-0.03454077,-0.02135617,-0.00752435,-0.02223194,-0.05077638,-0.00153672,0.02625596,-0.08725146,-0.04205961,0.02550137,0.00126819,0.08251181,0.03150482,0.02284001,0.02236298,-0.03669803,0.07448548,0.01973346,-0.19856851,0.08494631,0.02567759,0.04597815,-0.04601627,0.04491514,0.0495886,0.07999877,0.01000799,0.05718394,-0.02042309,0.0895252,0.00185191,0.02409032,-0.00527518,0.00769258,-0.00047449,-0.01867887,-0.0217759,0.03731222,-0.05193963,0.02712593,-0.01588049,-0.01689443,0.03928912,-0.03492698,0.06515905,0.00094245,-0.00973333,-0.08522786,0.04049373,-0.00791978,-0.04716035,0.00392995,0.01817192,0.00315704,-0.05142997,0.12988414,-0.10094478,0.03946899,0.04480154,0.00749203,0.05982697,-0.01101364,-0.0022321,-0.04218416,0.02407951,0.0203771,-0.00309762,0.02951626,0.0059672,-0.07818726,0.07054657,-0.02868355,-0.01582255,0.01674558,0.01829882,0.02149206,0.02503208,-0.01472916,0.0423499,-0.05482613,0.00053211,-0.00056339,0.02932963,0.00543806,0.00690967,0.01882724,0.02165032,0.01440497,-0.09577723,0.02445454,-0.00715179,-0.02216856,0.06357083,-0.0374184,-0.03512597,-0.05075895,-0.04494392,0.02780914,-0.00328267,-0.06027491,-0.05978859,0.10844313,-0.02043171,0.05265728,-0.0564739,-0.073307,-0.00978957,0.0121671,-0.02358263,0.00959417,-0.00042728,0.03406006,0.05753087,0.06472481,-0.03213234,-0.00024991,0.02000325,-0.02332393,-0.09435079,0.09791914,0.04148911,-0.12348624,-0.04613404,-0.05951186,0.01856421,-0.02639015,-0.04552386,0.00278219,0.02953415,0.0077041,0.05266793,0.01918591,0.00702858,-0.0087851,0.02520502,0.0835565,0.06983989,-0.04264015,0.03956126,0.06175337,-0.03135555,-0.08267131,-0.04752964,-0.01989299,-0.01434808,-0.00642748,-0.02906221,-0.01213933,0.01382305,-0.03184763,0.03923196,0.01554966,0.02604284,-0.00437181,0.00243227,-0.03117712,0.06313573,0.06323604,0.00098525,0.0184914,-0.07277138,-0.03112846,0.01461556,-0.06882877,-0.01603571,0.03095855,-0.03341645,0.02706268,0.05021229,0.00877297,-0.08625348,-0.01190238,-0.04753376,0.07834367,0.01270514,0.03553769,-0.04261617,0.04562111,-0.08254715,-0.20698363,-0.00958689,0.05680417,-0.04434387,0.02773367,-0.01595889,0.0710477,0.00468735,0.00809434,0.04980058,0.14960347,-0.03851592,-0.06169061,0.00325103,0.00029078,0.06036094,-0.04959683,-0.00677795,0.01035294,0.01854727,-0.04560084,-0.01823621,-0.04085748,-0.03645113,0.04196269,0.00326869,0.1726146,0.05571968,0.02178138,-0.00101654,0.05458231,-0.00570703,0.04420275,-0.1847723,0.02568497,0.02075107,-0.02421228,0.0096491,0.0474913,0.01049456,0.01086917,-0.02830251,0.01346834,-0.09007002,0.00815164,-0.01851327,-0.00373357,-0.06230345,0.00234188,-0.04057357,0.03597409,-0.02131074,-0.01258148,0.02884022,0.02108266,-0.04814701,-0.02970323,0.00640233,0.0234075,-0.04058597,0.01014998,0.00388076,0.00706565,-0.01688666,0.05904534,-0.04245794,-0.03096524,-0.0079171,-0.01546449,-0.05925103,0.01857075,0.09390116,0.03087913,0.00430661,0.0072235,0.0300992,-0.06209129,0.02972117,-0.04585955,-0.00286459,-0.0088748,-0.01931955,0.05356062,-0.01523988,-0.01737028,0.06995245,0.0033078,-0.06971683,0.01723957,-0.01871496,-0.03724634,0.02605297,-0.04900554,0.02987335,0.07214484,-0.03012336,-0.23363952,0.0975926,0.07758979,-0.05228705,-0.01958501,0.00769343,0.09012891,-0.06711306,-0.07280727,0.01498163,0.02604464,0.05772228,0.02257583,0.00365769,-0.00874194,0.05579281,0.0439644,-0.03380927,0.05866512,-0.06164489,0.03230296,0.02183229,0.22738443,-0.01246856,0.01669904,0.00981871,-0.04972577,0.03362089,0.0904742,0.00874082,-0.00635052,0.013595,0.08738498,-0.03577966,0.0235724,0.01236927,0.07687793,-0.03344421,-0.03486,-0.04044368,-0.05354935,-0.00219539,-0.01188948,-0.03135392,0.02008709,-0.14131533,-0.04443068,-0.05422617,-0.02604662,-0.05294536,-0.02248145,-0.05441664,-0.01164019,0.02547048,0.10207891,0.0450354,0.04480436,0.01850181,-0.089386,0.01083463,0.03084739,-0.03649157,0.04139293,0.01777477,0.05014249]}},"text":null,"length":0,"last_read":{"hash":"6954d68a7882540c8500ba0f13b5eaf17c72b080bd533776bbffd68cdf6eb49a","at":1736406354785},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Git#{1}","lines":[35,36],"size":208,"outlinks":[{"title":"https://git-scm.com/book/en/v2/Git-Basics-Tagging","target":"https://git-scm.com/book/en/v2/Git-Basics-Tagging","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Git#{2}": {"path":null,"last_embed":{"hash":"f6c541e666265802a3f353dd0f70e913829ae902139d2a4ddb0fcf5f8e69e4d1","tokens":92},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03920526,-0.04203667,0.00117756,0.00536454,0.06950862,-0.03849399,0.005332,0.03546055,0.04225673,-0.02359624,0.03405908,-0.01055131,-0.01474139,0.0148941,0.0152417,0.00618449,-0.05356969,0.07197268,-0.01613014,-0.05194016,0.0924774,-0.00300093,-0.02014885,-0.01015658,0.04091064,0.06635901,0.01715534,-0.05207199,-0.03905728,-0.19106951,-0.02686692,-0.00579203,-0.01533899,0.04878704,-0.00412298,0.02983507,-0.04772239,0.02883859,-0.02004365,0.04332869,0.0644563,-0.01285802,-0.02357091,-0.01511385,-0.04886715,-0.03624948,-0.00975124,-0.05502572,-0.05019085,0.02183221,0.03774448,-0.04293605,-0.01569536,0.05215018,-0.00470035,0.08541904,0.01873235,0.00826644,0.02534617,-0.03054673,0.07032287,0.01400936,-0.16657844,0.04432059,0.04977484,-0.00300061,-0.02152831,0.01576565,0.02032677,0.07065944,0.04181195,0.01797444,-0.04984957,0.08909135,0.01119748,0.01742799,0.00103766,0.03655477,0.02657669,-0.00071098,-0.05441157,0.03947657,-0.06422221,-0.04050129,-0.02983337,-0.05037902,0.03091177,0.01326663,0.06156001,0.03111129,-0.00417594,-0.08264238,0.0506139,0.00499357,-0.05706742,0.00980152,0.04626901,-0.00977407,-0.04634491,0.13658129,-0.12013852,0.04260691,0.03860484,0.02169259,0.0689274,0.01884858,-0.01410695,-0.04242675,0.02359539,0.00357259,-0.02035604,0.04122638,0.02011406,-0.10406402,0.04122337,-0.01573574,0.01114099,0.03864812,0.030959,0.01397917,0.02246893,-0.01149385,0.04685802,-0.0215116,0.00903826,0.05438906,0.03484123,0.00306182,0.01195767,0.0117922,0.04951347,0.00181182,-0.13613926,0.04945259,-0.01933053,-0.01534726,0.05787139,-0.05663731,-0.00565387,-0.09140287,-0.00760462,0.01677333,-0.0082381,-0.09750163,-0.08419194,0.13841763,-0.0599379,0.06374543,-0.0735075,-0.03638117,-0.05212438,0.00871107,0.00855587,-0.00056909,0.00059774,0.06778438,0.08020127,0.00412875,-0.01469696,0.00049175,0.04275685,-0.05514138,-0.041098,0.08175507,0.02208121,-0.06448483,-0.04641625,-0.02524469,0.0382737,-0.06244837,0.03354428,0.01268132,0.02910189,-0.02047453,0.02703627,-0.03789162,0.00627649,-0.00809404,0.02048809,0.07756069,0.04157835,-0.0038195,-0.03923602,0.04282241,-0.00553011,-0.03337154,-0.02229392,-0.01699477,0.01253549,0.03663659,-0.06004813,0.02392983,0.10077357,-0.00922391,0.00292417,-0.01194978,0.01864225,0.00323829,-0.02413671,-0.04401198,0.05937519,0.05274105,0.00077748,0.04077157,-0.01112466,-0.03530828,0.0013276,-0.06161542,-0.0325182,0.01115714,-0.03472558,0.03032515,0.02097555,0.02152207,-0.082886,-0.03708922,-0.07154959,0.04576082,0.01645341,0.00502168,-0.04728639,0.02270672,-0.02866051,-0.19448286,-0.01456585,0.05462589,-0.0342549,0.0384137,-0.08591446,0.04842423,-0.00687682,0.02163582,0.03836253,0.04656469,-0.0328945,-0.07641891,-0.03403342,0.01120892,0.06953163,-0.04300285,0.01657065,0.04724915,0.01028365,-0.01826444,-0.04012658,-0.0773679,-0.04189009,0.00682668,-0.00269303,0.17295662,0.07000603,-0.03966006,-0.01443466,0.04896194,-0.00245414,0.01904508,-0.15610071,0.04660497,0.01086201,-0.09954932,-0.03057919,0.0254614,0.01420163,0.02871107,-0.02476783,0.00512873,-0.09391239,0.00363177,0.00681454,-0.01674716,-0.07795856,-0.00043279,-0.02031836,0.02432463,-0.00100076,-0.04280264,0.00686655,0.02996334,-0.02693618,0.00689069,-0.00558605,0.02813752,-0.02937857,-0.00612544,-0.0225238,-0.00217863,0.00139227,0.05038044,-0.0034079,-0.01968441,-0.02450155,-0.05130363,-0.08499695,0.07020767,0.10878856,0.03788128,-0.07443677,0.03759345,0.02800832,-0.02680589,0.00307482,-0.07224661,-0.02614947,0.01767431,-0.04292121,0.04493291,-0.02071638,-0.02823247,0.09009536,0.01374384,-0.0729963,0.00354159,-0.00419385,-0.00363324,-0.00457489,-0.00530497,-0.02339012,0.06354076,-0.02984038,-0.22012314,0.07795378,0.08724203,-0.06790213,-0.00112693,0.04458427,0.10970693,0.00526152,-0.08481842,0.03934672,0.01569107,0.06639321,0.00842834,-0.026336,-0.04234903,0.06303661,0.06224236,-0.04235096,0.03954242,-0.01682135,0.02085876,0.03888421,0.21450703,-0.04494523,0.04182418,-0.00057742,-0.05801209,0.04097807,0.08006088,0.00002605,0.00646479,0.01802636,0.09840199,-0.02435194,0.01518487,0.09456995,0.04919911,0.00008446,-0.02587107,-0.01657206,-0.00226278,-0.02797636,-0.03431471,-0.03871545,0.0582034,-0.08571282,-0.05292101,-0.0480625,-0.03189423,-0.05208625,-0.05642167,-0.02373391,-0.03651928,0.00561666,0.13405895,0.02848407,0.0369936,0.01615127,-0.06537252,0.02530489,0.0091827,0.00356688,0.04025704,0.04044706,0.01118699]}},"text":null,"length":0,"last_read":{"hash":"f6c541e666265802a3f353dd0f70e913829ae902139d2a4ddb0fcf5f8e69e4d1","at":1736406354792},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Git#{2}","lines":[37,39],"size":330,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Git#{3}": {"path":null,"last_embed":{"hash":"770c014bc09d3dc8bc290ebc9a178447021bf0b226136417f0c39c0a63239889","tokens":82},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.00865338,-0.0512381,0.00171645,0.03034798,0.06722273,-0.04171809,-0.00326163,0.01766839,0.0491501,-0.03127412,0.01436163,-0.04748966,0.01692836,-0.00829228,-0.02790239,-0.00312432,-0.05208287,0.03118431,-0.03310389,-0.06316865,0.11172513,-0.02588776,0.02410666,-0.00490332,0.04495864,0.08677363,-0.00306517,-0.06294963,-0.04423714,-0.16923127,-0.03277284,0.0283482,-0.01399973,0.02863299,-0.02749143,0.03464767,-0.05594967,0.04573243,-0.01009426,0.0585441,0.02543286,0.01198965,-0.01208009,-0.00346196,-0.00590934,-0.02068547,-0.06171029,-0.03930134,-0.08532262,0.01696457,0.03423086,-0.01802824,-0.01843676,0.06318159,-0.03067054,0.09268501,0.01350271,0.03186993,0.03032809,-0.00856376,0.09707518,0.05223589,-0.17670725,0.04555812,0.02903036,0.01034644,-0.03645571,0.00654827,0.02403814,0.07382437,0.00509411,0.03231093,-0.05457645,0.03865474,-0.00358332,0.03724461,0.030468,0.0491157,0.00609916,0.00152306,-0.00108541,0.03775778,-0.05784816,0.00811327,-0.07559515,-0.06021034,0.02522019,-0.02260612,0.04075662,0.02103519,-0.01022404,-0.075578,0.04225428,0.00498724,-0.05414301,0.00172058,0.0491946,0.02375881,-0.04703361,0.151254,-0.10029284,0.00603658,0.05530909,0.0596901,0.07596256,0.00664467,-0.02207272,-0.06939562,0.02868882,-0.01065891,-0.01523465,0.02328593,0.04269284,-0.08553602,0.03496764,0.01689555,-0.00173678,0.01360975,0.03365302,0.01388015,0.03740513,0.03215994,0.04617032,-0.03386737,-0.0010629,0.03353878,-0.00141892,-0.01054745,0.01360464,0.01476393,0.00588355,0.02105522,-0.10891964,0.0382091,-0.03354123,-0.03248258,0.05300513,-0.02137403,-0.02930326,-0.07680459,-0.01127085,0.04143794,-0.02051946,-0.05735963,-0.05461211,0.13028015,-0.04352824,0.06692406,-0.05396,-0.02643432,-0.02833756,0.02408157,-0.0223214,-0.01353606,-0.00438298,0.05250042,0.05706021,0.00758446,-0.05026245,0.03443015,0.03678245,-0.06407344,-0.05351845,0.08449246,0.05209544,-0.0735046,-0.03761081,-0.03346894,0.03510939,-0.03485933,0.00573245,0.03690139,0.02183649,-0.02697869,0.03807904,-0.03871891,0.03190042,0.00813981,0.01493466,0.08609948,0.04431716,-0.01105112,-0.01882169,0.00438725,-0.02336008,-0.03022708,-0.03532154,0.02336971,0.01669199,0.01997971,-0.07968556,0.03005664,0.04842003,0.00364017,0.014074,-0.00332183,0.00732881,-0.00168234,0.00020503,-0.04406254,0.05265214,0.06190766,0.02071004,0.02209558,-0.07356644,-0.02084771,-0.00066799,-0.05751872,-0.05759235,0.04853756,-0.01770866,0.02891155,0.01968662,0.02535609,-0.03246894,0.00931577,-0.06452933,0.02047292,-0.00265991,0.03012484,-0.05314268,0.03788564,-0.02611071,-0.19035517,-0.02503353,0.05889465,-0.03829319,0.03891897,-0.05725773,0.05513058,-0.00269229,-0.0025627,0.02662363,0.07208154,-0.00612853,-0.0648585,-0.00918498,0.01365102,0.08590346,-0.05610432,0.02171415,0.00843267,0.01725586,-0.02877172,-0.02917734,-0.07932789,-0.04271478,0.03944927,-0.02453612,0.17525981,0.10746317,-0.055943,-0.01622913,0.03368762,0.01716837,0.03419623,-0.18878661,0.06559258,0.04559162,-0.08629131,-0.02163375,0.01830821,-0.04047783,0.01123939,-0.04687757,-0.0230909,-0.04932462,0.0352214,-0.03876712,-0.01187279,-0.08828907,0.02532576,-0.00466163,0.0569521,0.00653263,-0.04018145,0.03220423,0.06590983,0.01364673,-0.05635032,0.03808977,-0.00192085,-0.01822485,0.01503297,-0.01825148,-0.01388282,0.01286739,0.04514196,-0.01677545,-0.03626276,-0.03146317,-0.01401495,-0.03347494,0.09221815,0.10144443,0.01499768,-0.08674306,0.03323838,0.0175092,-0.0520799,-0.02389776,-0.05129607,-0.04259035,-0.00086442,-0.07746468,0.01966797,-0.04682928,-0.00854043,0.08516983,0.00842206,-0.03246482,0.04611836,-0.02440704,-0.0066287,0.02314921,-0.02816218,-0.0442096,0.10053384,-0.0552561,-0.23420753,0.07201023,0.0272467,-0.05308602,0.03176466,0.05282611,0.07995135,-0.01587334,-0.09889521,0.03348827,0.06874795,0.02648899,-0.00336756,-0.01361672,-0.02889928,0.02957496,0.03760272,-0.06986197,0.01271306,0.00347121,0.04014759,0.0497637,0.20594171,-0.02283177,0.00845937,0.01099154,-0.04096833,0.0511361,0.08742688,0.00875995,-0.04114304,0.01380966,0.10955371,-0.01438215,0.05263533,0.06625465,0.0148546,-0.01196908,0.01305416,0.02354386,-0.03961996,-0.0459583,-0.01181998,-0.00596356,0.04795806,-0.09443089,-0.02658766,-0.0357525,-0.04579042,-0.07201339,-0.01188906,-0.00500757,-0.06181555,-0.01808236,0.08302633,0.04793942,0.03101826,0.01105339,-0.11259315,0.01922134,-0.01723108,0.00806097,0.0303676,0.03949192,-0.03686284]}},"text":null,"length":0,"last_read":{"hash":"770c014bc09d3dc8bc290ebc9a178447021bf0b226136417f0c39c0a63239889","at":1736406354798},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Git#{3}","lines":[40,47],"size":233,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#NPM": {"path":null,"last_embed":{"hash":"40e91db364db262c02795897e9b8708dcf7750c52ebc5881cbb7855273c40c61","tokens":71},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04757982,-0.03066358,0.02365151,-0.01245265,-0.00040489,-0.03159375,-0.09851243,0.01722532,-0.00348154,-0.01077708,-0.0148185,-0.02126566,0.0122002,0.0048067,0.03003929,0.00050118,-0.04015964,0.05562319,-0.01337094,-0.00224751,0.06098795,-0.00883414,0.06746478,-0.02576931,0.01861733,0.04890974,-0.01594653,-0.00412476,-0.0459823,-0.20006552,-0.05830545,-0.00867885,-0.05508032,0.05834579,-0.01067949,0.02639204,-0.04394287,-0.00029714,-0.06960227,0.07297343,0.07336316,0.06532272,-0.03547367,-0.00833274,-0.01540919,-0.04001042,-0.04499494,-0.03087164,-0.02624377,-0.02006184,-0.00602525,-0.05756189,-0.02322868,0.05805258,-0.01284966,0.04265706,0.0378698,0.07194379,0.03076332,0.01037186,0.07377927,-0.00708005,-0.19620065,0.06584704,0.02878416,0.04532001,0.00271047,-0.03273269,0.04813696,0.06850666,0.00209196,0.03903012,-0.0543737,0.07878391,-0.00034595,0.01270525,0.02113868,0.01927429,0.0301746,-0.01011132,-0.02861996,-0.00231414,0.00486805,0.02259864,0.00558156,-0.0041719,0.01299588,0.00112775,0.07324339,0.01080776,-0.02399478,-0.08516208,0.05357239,0.02514351,-0.05584226,0.05501878,0.06262045,-0.02953815,-0.06769004,0.12828463,-0.01551783,0.00801786,0.04424871,0.03830414,0.01845752,-0.01268264,-0.03443426,-0.08767415,0.04823554,-0.02305239,0.00064321,0.02278736,0.04984377,-0.05655808,0.03549866,0.00702309,-0.02677673,-0.00843915,-0.04678918,0.02375205,0.02410248,0.00860984,0.08769114,0.00712445,0.02029078,-0.0037183,0.01625549,-0.04675762,0.00205909,0.01192825,-0.00071439,0.0570924,-0.07205648,-0.03439946,0.00522133,-0.02240137,0.00213249,-0.06607293,-0.01070214,-0.02198592,-0.05069311,0.03831781,0.01845622,-0.06019658,-0.03068782,0.10589238,-0.00206633,0.07492946,-0.06889332,-0.06728718,-0.03247061,0.03756813,-0.01044887,-0.03340463,0.04649196,0.04763393,0.03018762,0.06052211,-0.08389209,0.01596935,0.04721982,-0.04219082,-0.02602361,0.09117484,-0.02473987,-0.0837637,-0.04627865,-0.04124482,0.04430337,-0.00806011,-0.05299161,0.00549649,0.03341645,-0.0222149,0.05863847,-0.02384314,0.03844674,0.00201887,0.02855531,0.06990085,0.04490108,-0.02128412,-0.01390402,0.04099813,-0.01042482,-0.07179169,0.0084959,-0.00906008,0.01037568,0.01408294,-0.08195139,0.05345181,0.06166007,-0.02282657,0.03889114,-0.02979054,0.01234066,-0.00484955,0.01084499,-0.02840743,0.09057435,0.11141179,-0.00073307,0.0138134,-0.11279298,-0.01449222,-0.01798173,-0.0533679,0.02353313,0.03679293,-0.05993376,0.01688229,0.05186607,0.03316847,-0.05673204,0.00236905,-0.01024726,0.05776214,0.009963,0.06151548,-0.01191192,0.01293184,-0.04638035,-0.21606559,-0.02096525,0.0251685,-0.11212337,0.06024049,-0.02993182,0.04258286,-0.02447425,-0.03093659,0.05868628,0.1027773,0.00286394,-0.05442111,0.03768911,-0.02117478,0.01428874,-0.05732,-0.00079989,-0.00101645,0.01020687,-0.0468449,-0.04569903,-0.03876635,-0.03776124,0.02183815,-0.04834154,0.14619295,0.05457932,0.01744913,-0.01363064,0.01974143,0.03469578,0.01030175,-0.21935669,-0.00578042,0.07817163,-0.03994563,0.01739862,0.03084594,-0.0102356,0.00726502,0.03796363,-0.01903586,-0.09427831,-0.03203893,-0.05225549,-0.04432504,-0.02771945,-0.01839181,0.01165267,-0.00606363,0.05929635,0.01176857,0.04296555,-0.00244827,-0.02140604,-0.04109777,0.04526124,0.0507333,-0.01859521,0.06168246,-0.00093896,-0.0024439,-0.01262776,0.08002038,-0.03647939,-0.03027897,-0.01171645,0.03930328,-0.03175341,0.05017998,0.08128477,-0.00148728,-0.01935693,0.00287597,0.03851794,-0.02722968,-0.03744831,-0.02884755,0.01705266,0.00167047,-0.04018984,0.02434798,-0.01896145,-0.04981759,0.06357501,-0.00192334,-0.03156384,0.03353585,0.02677963,-0.02722127,0.00872494,-0.01745589,0.00068362,0.072432,0.00144204,-0.22246781,0.01693091,0.04029766,-0.08305729,-0.0065928,0.04647645,0.04828379,-0.01287107,-0.09013119,0.02481536,0.05367593,-0.002529,-0.0340492,-0.00836622,0.0328952,0.00843405,0.0727511,-0.0321629,0.0421349,-0.08766108,0.04380368,0.05451823,0.21355009,-0.00929947,-0.01868629,0.04292961,-0.04456475,0.06800439,0.04340458,-0.0055637,-0.02178462,0.00656031,0.07389617,0.0267616,-0.00770163,0.10536697,0.00070199,-0.02409217,0.03775786,0.00763593,-0.04220606,0.00968777,-0.02188184,0.01825798,0.0502806,-0.10674763,-0.03059693,-0.02640225,-0.00287235,-0.07440171,-0.01583836,-0.0364444,-0.01080137,-0.00317016,0.11243988,0.06232576,0.04850695,-0.02148167,-0.14103027,0.030142,-0.02662702,-0.03177706,0.05824297,-0.00401019,-0.00389578]}},"text":null,"length":0,"last_read":{"hash":"40e91db364db262c02795897e9b8708dcf7750c52ebc5881cbb7855273c40c61","at":1736406354805},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#NPM","lines":[48,55],"size":170,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#NPM#{1}": {"path":null,"last_embed":{"hash":"b9c4499c427ecf0edf980d38a4ebca110f16f5360928f6e4ea2b8a0f08cdc105","tokens":49},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02875477,-0.02773369,0.02387146,-0.03675335,0.01682895,-0.01551902,-0.12158692,0.03681958,-0.01945241,-0.01181486,-0.02394456,-0.00414138,0.02210893,0.01331524,0.02846479,-0.00271974,-0.02176777,0.03528688,0.00876134,0.01789885,0.0522087,0.01496928,0.0497143,-0.03228309,0.00650105,0.07909255,0.03810411,0.01259174,-0.02112686,-0.19594422,-0.0628092,0.02481235,-0.04049785,0.05374278,0.00248738,0.02409071,-0.02503535,0.01276111,-0.05330234,0.05087426,0.05071562,0.05713405,-0.02997641,-0.01442948,-0.01815208,-0.0143319,-0.0465739,-0.03435485,-0.04740702,-0.03833072,-0.01307243,-0.06955346,-0.01043394,0.04596078,0.01109175,0.05024833,0.01837013,0.06015418,0.03153741,0.01913265,0.0752775,-0.0330054,-0.2135621,0.05065851,0.03170872,0.03327306,-0.02344351,-0.05608307,0.03144379,0.07006552,-0.0037825,0.06479883,-0.03892731,0.06541345,0.02454832,0.0548651,0.0133573,-0.02970136,0.04602689,0.00066824,0.00254718,-0.03548797,-0.00888977,0.00465089,0.0253382,0.00423734,0.01251375,0.00717979,0.08019441,0.00586853,-0.06295165,-0.07719136,0.04425251,0.04535189,-0.04246554,0.02738105,0.06079125,-0.02594865,-0.04751771,0.12732579,-0.01978657,0.00875406,0.02443179,-0.00094339,-0.00580154,-0.01605649,-0.04060226,-0.08326499,0.05877366,-0.00398698,0.00897809,0.00964317,0.05657062,-0.05450837,0.0628394,-0.02137961,-0.05591256,-0.00031624,-0.07313821,0.01170679,0.02913288,-0.01638233,0.07925717,-0.00385543,0.01390459,-0.00032587,0.02897861,-0.0240302,-0.01609326,0.00707876,-0.01937372,0.03677693,-0.07793567,-0.05789467,0.0436787,-0.01486987,0.00235966,-0.0643351,0.0060479,0.03488942,-0.05964063,0.04057036,-0.00243956,-0.06313168,-0.04282251,0.12758011,-0.00188789,0.05141609,-0.05083035,-0.05085985,-0.03032575,0.04652116,-0.02625551,-0.02072849,0.04585524,0.02130494,0.04095171,0.0412619,-0.09124991,-0.00660426,0.05948879,-0.02014805,-0.05636665,0.086613,-0.04355826,-0.0853212,-0.04866887,-0.0240424,0.00485403,0.01000499,-0.04647497,-0.03665344,0.01905552,-0.01203514,0.05381136,0.00621755,0.00625554,-0.01720523,0.01721545,0.05136944,0.0264433,-0.04675425,-0.01383728,0.0159828,0.01274154,-0.05521428,0.02226902,0.02114192,0.02253135,-0.00043288,-0.07445481,0.03469393,0.06908347,-0.01337728,0.02993171,-0.01348265,0.02524732,0.00332518,0.02883265,-0.02431237,0.07789093,0.12188908,0.00834079,0.00709691,-0.09910933,0.01858429,-0.03083855,-0.01514188,0.03720347,0.05287879,-0.08802853,0.00006192,0.06688128,0.04950776,-0.04043855,0.02109324,0.00052117,0.0491389,0.00238807,0.05843059,0.0076607,-0.00946818,-0.06972041,-0.2081787,-0.02313331,0.008002,-0.12382165,0.00435901,-0.00359887,0.04175981,-0.01789183,-0.02800822,0.06271506,0.08624698,-0.00912437,-0.01685762,0.03680318,0.00014744,-0.01226308,-0.02958622,-0.0306096,-0.00892286,0.01606203,-0.01746265,-0.02054496,-0.00386339,-0.0704378,0.00802421,-0.03513334,0.16594356,0.02101087,0.03529388,-0.00332107,0.04532525,0.02740984,-0.01070857,-0.20165671,-0.00219938,0.0822519,-0.03053536,0.02999075,-0.00578791,0.00972437,0.03099263,0.03767903,-0.00300769,-0.11324658,-0.03237928,-0.04216163,-0.03028741,-0.0142135,-0.04759134,0.04008169,-0.01563574,0.06294291,-0.03086399,0.03236876,-0.00408601,-0.02541285,-0.07469442,0.03289547,0.03606934,-0.01486523,0.08557609,-0.03386005,0.00011128,-0.0409138,0.07907065,-0.03771119,-0.00895764,-0.00391903,0.02820503,-0.02842838,0.03490642,0.07903021,-0.00088291,0.02419457,0.01155851,0.02770509,-0.01853759,-0.0144293,-0.00875001,0.00721508,-0.0127351,-0.02713839,-0.00610899,-0.00266593,-0.02889764,0.0641715,-0.01286719,0.00542262,0.03053224,0.01356941,-0.0636294,0.00076418,-0.01764026,-0.0028395,0.06120409,0.02851695,-0.23024671,-0.00273032,0.04797694,-0.03881387,-0.00032957,0.0461705,0.04808627,-0.0100804,-0.08881343,-0.00683634,0.00780931,0.00323898,-0.03184308,0.00026234,0.01045876,0.01272479,0.06814206,-0.02766327,0.04035307,-0.1047248,0.08135692,0.06298875,0.2300476,-0.00212504,-0.03944538,0.0364451,-0.04350389,0.05710852,0.05462347,-0.00883028,0.00213353,0.03707177,0.09934703,0.01750043,-0.01066522,0.08663199,0.01675646,-0.01967839,0.03691626,-0.00977869,-0.05228716,-0.01594874,-0.03964113,0.01414799,0.03962237,-0.1066277,-0.04188693,-0.02525377,-0.00002855,-0.07833467,-0.01193958,-0.04267904,0.01096729,0.00418345,0.11851348,0.06232021,0.05412406,-0.01942685,-0.13856544,-0.00332281,-0.01559353,-0.00791022,0.04718513,0.02870146,-0.01013737]}},"text":null,"length":0,"last_read":{"hash":"b9c4499c427ecf0edf980d38a4ebca110f16f5360928f6e4ea2b8a0f08cdc105","at":1736406354811},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#NPM#{1}","lines":[50,51],"size":91,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#NPM#{2}": {"path":null,"last_embed":{"hash":"6fb5a53983b6dff55ce542b6a04795bd5c410e3c6417b04e6491facd7b66355d","tokens":32},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02557197,-0.04426494,0.00926005,-0.05165051,-0.00281864,-0.06652512,-0.08507147,0.04806378,0.00152177,0.00295046,0.00433526,-0.03899042,0.02497337,0.01476345,0.04662911,0.00123734,-0.04652747,0.06718618,0.00148335,0.01525444,0.07465756,0.00137233,0.03293404,-0.03387706,-0.00355801,0.08251178,0.02108552,0.01337523,-0.04667021,-0.18751626,-0.02602243,0.02537458,-0.02840526,0.02205673,0.01893546,0.03305484,-0.0382471,0.00552862,-0.06198918,0.04619486,0.02970644,0.03927366,-0.05438013,-0.0051072,-0.0490135,-0.05723754,-0.03424485,-0.0098518,-0.05356906,-0.03827012,0.00357443,-0.08913259,-0.03245714,0.03765864,0.00735661,0.06227864,0.02462355,0.04319774,0.02417752,0.00902047,0.07292943,-0.0025959,-0.22107261,0.03513823,0.05695827,0.03675412,0.00242949,-0.06976923,0.00492833,0.0549628,-0.0290618,0.07945473,-0.02102916,0.07907002,0.01545264,0.03426974,0.00451938,-0.02178089,0.03531785,-0.01174457,0.00462735,-0.0209839,-0.02546817,0.02233498,0.01743134,-0.0076036,-0.01278521,0.02798627,0.06223079,0.01413238,-0.07564738,-0.07084874,0.05544556,0.01712134,-0.03827522,0.07169077,0.03867278,-0.01078098,-0.06822353,0.13036895,-0.01031442,-0.00223498,0.04196352,-0.03068438,0.00223238,-0.00499587,-0.05231526,-0.0863563,0.08480286,-0.01117452,-0.01294752,0.0002934,0.06663734,-0.04365554,0.02263376,-0.02826738,-0.02635895,-0.00649732,-0.02551259,-0.00021042,0.04662086,-0.00049857,0.05898123,-0.01282622,-0.00412383,0.00671757,0.02099653,0.00299951,0.00151641,0.01859823,-0.02073942,-0.00071312,-0.05493261,-0.05272994,0.02488692,-0.04948168,-0.01679982,-0.05005158,-0.0200693,-0.030551,-0.04205257,0.01494656,0.0008234,-0.04185791,-0.01337046,0.11711144,-0.01461681,0.05566923,-0.02082717,-0.02709788,-0.04220961,0.0414926,-0.01369472,-0.03496059,0.03806328,0.04376194,0.0463466,-0.01121647,-0.09622701,0.04263442,0.07499811,-0.03685442,-0.01461758,0.13368858,0.00310976,-0.09408956,-0.0456024,-0.03093147,0.01230186,-0.0088604,-0.05660036,-0.02821741,0.00687139,-0.04580331,0.0781974,0.00899808,0.02749455,0.01612885,0.01591973,0.04136857,0.08029756,-0.03897352,-0.01982476,0.02577061,-0.01061703,-0.0621618,0.0366421,-0.01199417,-0.0166103,-0.01578581,-0.07038493,0.06546844,0.05367578,-0.04586786,0.02657922,-0.04809117,0.0382137,0.03181817,-0.00131713,-0.02251254,0.09314696,0.13370967,0.01902111,0.0187078,-0.09282143,0.02319867,-0.03517833,0.00182699,0.01457845,0.03462374,-0.06240642,0.01375967,0.07781589,0.0499622,-0.02991163,0.03777457,-0.00263988,0.03240963,0.01535634,0.05873284,-0.0100615,0.02127103,-0.0701299,-0.22189175,-0.01119231,0.02175966,-0.15572813,0.03049229,0.01826281,0.03248086,-0.04583251,-0.01483221,0.03458372,0.07771631,-0.01586761,-0.0160638,0.04863391,-0.02928235,0.00580549,-0.00489499,-0.01848631,-0.005573,0.04339502,-0.03811841,-0.01797049,0.02391555,-0.03915851,0.0030759,-0.03618744,0.16441356,0.05268512,0.01809204,-0.04180003,0.02379865,0.02338413,0.00805378,-0.20504674,-0.00554294,0.05756852,0.00679799,-0.01255086,0.01804916,-0.01939595,0.02466472,0.02541495,-0.0098448,-0.08359193,-0.0204646,-0.07744919,-0.01540354,-0.04151423,-0.01046034,0.05199198,-0.00987161,0.04092638,-0.01719534,0.02709426,0.01992883,-0.01558087,-0.04687352,0.04756966,0.02752224,-0.03225819,0.07296905,-0.01032566,-0.00634561,-0.03786032,0.07023788,-0.0452173,-0.00238661,-0.0251934,0.05147053,-0.04782714,0.01592989,0.04953428,0.02468005,-0.01007509,0.01820067,0.02504124,-0.02665114,-0.02082701,-0.02344237,-0.00112768,0.05704921,-0.03304113,0.01475662,-0.01690944,-0.02079363,0.03208257,-0.02578167,-0.00887168,0.03474795,-0.01800319,-0.03161236,-0.02415553,-0.03478821,0.04466553,0.10494602,-0.01148875,-0.23054461,0.01154426,0.0354904,-0.04680833,-0.02388532,0.05118917,0.02980944,-0.02046964,-0.09008504,0.00147062,0.00201213,0.01220359,-0.00909301,0.01152267,0.01719446,0.02116934,0.0642269,-0.00857153,0.06497224,-0.15067314,0.02972674,0.07408208,0.22653532,-0.01942644,-0.03282265,0.04724973,-0.01110416,0.04463011,0.03972713,0.02435057,0.01551592,0.00570678,0.09321771,0.01156301,0.00095707,0.06399664,0.02795605,-0.05023865,0.05292579,-0.02329821,-0.04007085,-0.01159554,-0.00080373,0.0148092,0.04325854,-0.07485971,-0.00147362,-0.01120918,-0.00899433,-0.0430715,0.00302214,-0.05135136,0.02105397,0.00954737,0.12082549,0.02852023,0.01644311,-0.01481822,-0.09597113,0.0004452,-0.01650386,0.00073604,0.07926615,0.00774364,0.00601573]}},"text":null,"length":0,"last_read":{"hash":"6fb5a53983b6dff55ce542b6a04795bd5c410e3c6417b04e6491facd7b66355d","at":1736406354817},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#NPM#{2}","lines":[52,53],"size":29,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#NPM#{3}": {"path":null,"last_embed":{"hash":"26ff4a198ea9878abfb9fe45b06e996ec652d6822e35d00023689fd8c34e05ce","tokens":33},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03388571,-0.01273115,0.02411108,0.00047531,0.00010811,-0.02523284,-0.05635049,0.02837184,0.02212809,-0.04510581,-0.00533536,-0.04595688,0.01968691,0.01929622,0.01993083,-0.02083923,-0.04622678,0.0764328,-0.02260935,-0.01948246,0.07658606,-0.02942314,0.02039484,-0.01844112,0.02974379,0.04919328,-0.02664798,-0.02972541,-0.04407697,-0.1808186,-0.05980882,0.00917666,-0.04515408,0.03915378,0.01014558,0.02957061,-0.05274705,0.00447576,-0.08722937,0.07202842,0.08160157,0.0427959,-0.02282613,0.00799517,-0.02285626,-0.07923976,-0.01991753,-0.00783067,-0.02433363,0.01037472,0.00877428,-0.05809245,-0.01822164,0.06138903,-0.01676193,0.05780246,0.05002584,0.06643335,0.05872726,0.00092934,0.07027218,-0.00811823,-0.18124215,0.05183915,0.03374239,0.04783298,-0.00020551,-0.03537581,0.06656623,0.02627745,-0.00237928,0.0181477,-0.0431057,0.07941391,0.00789902,0.00919467,0.00070661,0.03421389,0.02070805,-0.00564768,-0.04811905,0.01097839,-0.02112682,0.04849575,-0.00369561,-0.01831363,-0.00838277,0.00503771,0.07736319,0.00299938,-0.02450219,-0.11607092,0.05844852,0.01650116,-0.06697254,0.09108637,0.05874361,-0.04834767,-0.05563292,0.13668467,-0.03093789,0.01609974,0.05311486,0.04168402,0.03541354,0.00391557,-0.03885267,-0.08158959,0.04307394,-0.02167556,0.01739402,0.02375599,0.04335423,-0.07678902,0.0302623,0.02485844,-0.02077263,0.00996347,-0.04397796,0.03246897,0.01948019,-0.00340671,0.08045803,-0.0022121,-0.00946147,-0.0088249,0.01293478,-0.02643575,-0.00168421,-0.00122865,0.01684247,0.05769942,-0.03947955,-0.01713982,-0.00414774,-0.01887894,0.00803401,-0.06026842,-0.00733995,-0.04674436,-0.03648817,0.0486595,0.01364935,-0.05466183,-0.03825866,0.08350714,-0.01405713,0.08360416,-0.05389705,-0.0618468,-0.00961806,0.01654798,-0.0114647,-0.03336656,0.03492424,0.05019569,0.02515979,0.07041227,-0.06288767,0.04351477,0.03039597,-0.05668256,-0.006698,0.09660712,-0.00568775,-0.08066601,-0.04216297,-0.054186,0.03088282,0.01069372,-0.04279779,0.01557038,0.05007975,-0.0365813,0.07389233,-0.03483352,0.03793213,-0.00199555,0.03893255,0.06189348,0.06550539,-0.02995272,-0.02900502,0.03583403,0.00545001,-0.0519853,0.00506238,-0.02042367,0.00824308,0.00248511,-0.07395251,0.05861324,0.03680012,-0.02846297,0.06273019,-0.01023421,0.02735157,-0.01745198,-0.0220924,-0.01593927,0.09023288,0.10720897,-0.0194255,0.01222502,-0.10552678,-0.02673889,-0.01398633,-0.04561824,0.00967508,0.04452136,-0.02500804,-0.01004129,0.05875418,0.0602182,-0.0739456,0.01383497,-0.02396643,0.05539811,0.01077274,0.04656646,-0.01153283,0.0304931,-0.0088648,-0.21225628,-0.00671118,0.01370892,-0.10613061,0.04063144,-0.04647848,0.03768838,-0.04648187,-0.00322261,0.03340079,0.10781632,0.02222491,-0.04777261,0.0254214,-0.01907127,0.02437221,-0.06814617,0.01369212,0.00741571,0.01806582,-0.03707944,-0.0854827,-0.04059429,-0.04302804,-0.00109052,-0.06954616,0.16542432,0.06392954,0.02741295,-0.0173684,0.01735436,0.02688349,0.01570283,-0.24119417,-0.00572612,0.05978125,-0.04822418,0.01129023,0.04948867,-0.00415812,0.01545132,0.01136971,-0.05340721,-0.08473162,-0.00513524,-0.04872606,-0.06279185,-0.0168354,-0.00027829,-0.00107009,-0.00422643,0.05212167,0.0296119,0.0438495,0.0178478,-0.0391799,-0.01800081,0.05228503,0.04224939,-0.02305768,0.03785319,-0.00541244,-0.0029583,0.01016815,0.07004809,-0.02858775,-0.02053116,-0.01895255,0.03220686,-0.04834328,0.07178374,0.05880593,-0.0078546,-0.04907455,0.00204006,0.04243042,-0.03057677,-0.03084056,-0.04792871,-0.005084,0.01999498,-0.05251094,0.01166701,-0.03722765,-0.05182078,0.07076498,0.03591789,-0.05937149,0.01380186,0.00471542,-0.01005702,-0.00189036,-0.02150631,0.00644196,0.06226376,-0.00225239,-0.22074462,0.02329545,0.02362546,-0.11061427,-0.01046559,0.05839949,0.08024399,-0.0213668,-0.08727732,0.03324258,0.03987561,-0.00734676,-0.00705027,-0.00449753,0.0452646,-0.00300961,0.07355461,-0.02749413,0.04516616,-0.07598573,0.03560429,0.05832225,0.19734751,-0.0231611,-0.00714806,0.02602862,-0.05403404,0.04043175,0.09010683,-0.02364257,-0.02631631,0.00184055,0.06780409,0.02431284,-0.01907958,0.10174964,-0.00138523,-0.00464179,0.02784405,0.01633809,-0.03609414,0.01627855,0.00045106,0.00272979,0.02085035,-0.09920681,-0.03411399,-0.02475355,0.00385207,-0.08109716,-0.00639929,-0.0415247,-0.02360181,-0.0087256,0.09622341,0.06462806,0.03771126,0.00746225,-0.09542095,0.01501884,0.00179726,-0.01519917,0.05701002,-0.05303208,-0.01171941]}},"text":null,"length":0,"last_read":{"hash":"26ff4a198ea9878abfb9fe45b06e996ec652d6822e35d00023689fd8c34e05ce","at":1736406354822},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#NPM#{3}","lines":[54,55],"size":40,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Gradle": {"path":null,"last_embed":{"hash":"4fc25c700cd6073137458fc308a14241b421f56a32be757a3d6a598f79425b4b","tokens":427},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04900736,-0.04147598,0.02378495,-0.03719365,-0.02339543,-0.02595943,-0.09503143,0.01847923,0.02134222,-0.06268538,0.00899657,-0.03438553,0.03652873,0.04274142,0.05329318,-0.02939633,-0.02231145,0.06766813,-0.04585187,-0.01422202,0.10368837,-0.0388215,0.02130073,-0.05000731,-0.00357159,0.04432798,0.04224302,-0.01967799,-0.01419063,-0.23777246,-0.0369556,0.03312722,-0.08187933,0.03001529,-0.02030715,0.03104135,-0.03936353,0.0230172,-0.09899348,0.05126966,0.06486852,0.03553345,-0.02105696,0.0041474,-0.05009909,-0.04285411,-0.04151246,-0.01229257,-0.06171304,-0.00941874,-0.01214496,-0.04815239,-0.02216323,0.04278523,-0.04816118,0.03318963,0.02581964,0.0522181,0.0256972,0.00493186,0.09861615,0.00696433,-0.21557251,0.09432749,0.03219606,0.06362224,-0.03239336,-0.04061793,0.05139595,0.02053333,0.01007885,0.02365668,-0.00420842,0.04415904,-0.00706869,0.03279358,0.00201582,0.02582402,0.03788103,-0.01629306,0.00133609,0.02140843,-0.00716592,0.02023054,0.01940286,-0.02220526,0.00469047,-0.00984358,0.0801125,-0.01938295,-0.04799334,-0.07964917,0.06686179,-0.0030986,-0.03309809,0.06252527,0.01949277,-0.01948105,-0.06421989,0.10930672,-0.00611195,0.00012227,0.03617258,0.03443699,0.04320729,-0.02691816,-0.04743601,-0.05110069,0.05790868,0.01952705,0.01650501,-0.0404253,0.04032441,-0.01586517,0.03872156,0.01516673,-0.01485779,-0.00015394,0.00296963,0.0187081,0.05271893,-0.03672804,0.1076453,-0.0226141,-0.01487842,0.00541298,-0.01279988,-0.02002932,-0.02403995,0.01790102,0.02846971,0.03392319,-0.02911839,-0.0396222,0.0093249,-0.03863138,0.01789415,-0.04661719,-0.04144543,-0.03457497,-0.05447251,0.01231973,0.01203584,-0.01918324,-0.04696062,0.0791386,-0.02927891,0.04131926,-0.02558529,-0.02226393,-0.0199825,0.05653757,-0.0322584,-0.01195597,0.05672115,0.05649164,0.02069176,0.06399063,-0.06707123,0.02207157,0.04121671,-0.0359086,-0.05503093,0.11397871,-0.02477988,-0.07139367,-0.08615904,-0.03437952,-0.00198386,0.0206356,0.01584267,0.00201782,0.02376468,-0.02241654,0.04698863,0.01659437,0.02683267,-0.0138367,0.03818754,0.08429258,0.05848864,-0.0265274,-0.02172901,0.04138692,0.01828941,-0.04109011,0.02894691,0.00565627,-0.00722367,-0.01506678,-0.08904181,0.05051828,0.0385528,-0.00446161,0.04079923,-0.01229967,0.02443286,0.00961998,-0.02440324,-0.02412542,0.08509915,0.13767932,0.00784937,0.0364586,-0.07932755,0.02197481,-0.03347965,-0.03764133,0.04357973,-0.01232968,-0.03130529,-0.00870538,0.06180715,0.05368838,-0.07788683,0.02160476,-0.00136727,0.04701173,-0.00655048,0.04963462,0.03806442,-0.02816201,-0.02118707,-0.20352548,-0.00739649,0.02727943,-0.12622723,0.02087136,-0.02906973,0.03878885,-0.02792156,0.01836074,0.0306417,0.11881942,-0.00180141,-0.05490969,0.02463009,-0.04384018,0.02471953,-0.06266357,-0.01503349,-0.0296359,0.04439177,-0.01881002,-0.05723399,-0.01648049,-0.04739646,0.02958515,-0.00809767,0.1562019,0.01884806,0.05877096,-0.00910998,-0.01290073,0.01945719,0.04844687,-0.16009551,0.0274592,0.07604299,-0.00824085,0.03417995,0.0291804,-0.00320057,0.03703511,0.05637364,-0.04215061,-0.10070942,-0.02138142,-0.04369311,-0.04243196,0.00514803,-0.02177288,0.01951372,0.01345233,0.01321734,-0.00176431,0.02086471,0.00786202,-0.06063611,-0.04736073,0.0284368,0.02825486,-0.00621592,0.08914685,0.00660822,-0.0244182,-0.01593989,0.06609731,-0.01702968,-0.01953416,-0.05120912,0.034681,-0.06643081,0.02758775,0.07981533,0.00763836,0.00013376,-0.00896911,0.0080451,-0.00059969,-0.03704044,-0.02629227,-0.03262753,-0.04151257,-0.00509642,-0.01277138,-0.0285275,-0.05404052,0.05411454,0.0263808,-0.02318714,0.04221779,-0.01442051,-0.03720695,-0.01551813,0.00905231,-0.03392748,0.07141086,-0.0045062,-0.23327859,0.03291776,0.06224689,-0.06739599,-0.02344408,0.04818911,0.06320676,-0.01927676,-0.09077083,0.01492288,0.03964596,0.00193569,0.00374047,-0.00687518,0.04935696,0.00793804,0.05825322,-0.06860097,0.06566185,-0.0998445,0.06362008,0.06687827,0.22367777,-0.04664035,-0.02571127,0.01298411,-0.03653508,0.01781152,0.00950562,0.01619115,0.01101534,0.01318041,0.07215916,0.01406364,-0.01844518,0.10685468,-0.00878868,-0.01560063,0.04340393,-0.00054629,-0.03587529,0.00728702,0.00994579,0.00890391,0.07157128,-0.0779927,-0.07025743,-0.00960795,-0.00254355,-0.07266235,0.01602799,-0.06884207,0.00148462,-0.02020587,0.08864024,0.04197056,0.04529836,0.00492032,-0.10139462,0.00824414,0.0060375,-0.01398439,0.05844738,-0.01054713,0.00916271]}},"text":null,"length":0,"last_read":{"hash":"4fc25c700cd6073137458fc308a14241b421f56a32be757a3d6a598f79425b4b","at":1736406354827},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Gradle","lines":[56,66],"size":1126,"outlinks":[{"title":"https://axion-release-plugin.readthedocs.io/en/latest/configuration/ci_servers/#gitlab-ci","target":"https://axion-release-plugin.readthedocs.io/en/latest/configuration/ci_servers/#gitlab-ci","line":4},{"title":"https://github.com/jozala/blog-post-axion-release/blob/master/build.gradle","target":"https://github.com/jozala/blog-post-axion-release/blob/master/build.gradle","line":6},{"title":"https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Gradle.gitlab-ci.yml","target":"https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Gradle.gitlab-ci.yml","line":8},{"title":"https://gitlab.com/gitlab-org/gitlab/-/tree/master/lib/gitlab/ci/templates?_gl=1*10mhcse*_ga*NzQzOTM0NTIzLjE2ODIxMDkxOTE.*_ga_ENFH3X7M5Y*MTY4Mjk5MDEzMi4xNi4xLjE2ODI5OTE5NjQuMC4wLjA","target":"https://gitlab.com/gitlab-org/gitlab/-/tree/master/lib/gitlab/ci/templates?_gl=1%2a10mhcse%2a_ga%2aNzQzOTM0NTIzLjE2ODIxMDkxOTE.%2a_ga_ENFH3X7M5Y%2aMTY4Mjk5MDEzMi4xNi4xLjE2ODI5OTE5NjQuMC4wLjA","line":10}],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Gradle#{1}": {"path":null,"last_embed":{"hash":"4097d0c154e1bf41bd9d71768f6027ec32f0b905beb184255be6c4a635bdcfca","tokens":182},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04061732,-0.02449163,0.03393826,-0.04092099,-0.00326186,-0.03419513,-0.09400295,0.02571703,0.01516777,-0.0647577,0.02086055,-0.03537372,0.02764185,0.03411582,0.05250549,-0.02787495,-0.00927891,0.06073859,-0.05826503,-0.01456854,0.13051935,-0.05323224,0.02377719,-0.04236582,0.00401564,0.03257001,0.03008625,-0.00512077,-0.01096303,-0.21815106,-0.05263234,0.03503533,-0.08840597,0.02568607,-0.00543084,0.01773362,-0.04562461,0.01503731,-0.10599438,0.04080216,0.0663969,0.034379,-0.02483588,0.00678704,-0.03776281,-0.03995543,-0.03654316,-0.03441326,-0.04896785,-0.02539913,0.00149984,-0.02105094,-0.01147297,0.04577826,-0.05317955,0.03781429,0.0232159,0.05553737,0.02169323,0.00186247,0.08828143,0.01955695,-0.19478619,0.11532091,0.03149074,0.0486468,-0.03296598,-0.03755153,0.0698345,0.00556471,0.02193368,0.01837551,-0.00184645,0.04652965,-0.00472445,0.03842429,0.00939512,0.03708851,0.0472425,-0.02405507,0.01408858,0.01361074,-0.00388668,0.01725024,0.01314178,-0.03735455,0.00263029,-0.00250906,0.08463076,-0.00957627,-0.04149027,-0.09919926,0.07561206,0.00418199,-0.04663201,0.06038798,-0.0062799,-0.03641968,-0.06668508,0.11626167,-0.0130069,-0.00251832,0.03343231,0.01841605,0.03982418,-0.02642717,-0.04246981,-0.02727348,0.04690258,0.01707267,0.03643684,-0.03388484,0.0415634,-0.01586768,0.0410335,0.01532188,-0.01615534,0.00216579,0.00243633,0.02783957,0.06265741,-0.03427694,0.09163734,-0.02061576,-0.01239726,0.00618618,-0.01124229,-0.03277303,-0.02924583,0.01389036,0.03628574,0.02889622,-0.02511392,-0.02954888,0.01338439,-0.03349038,0.02162837,-0.03962334,-0.03622325,-0.02336739,-0.06112574,0.00292051,0.02105947,-0.01915021,-0.05084395,0.05945323,-0.03135298,0.04793229,-0.02487234,-0.00653615,-0.01069159,0.05248431,-0.02712978,-0.00925238,0.06702351,0.06685162,0.03196999,0.07477198,-0.06457808,0.01277243,0.03872083,-0.04399149,-0.053077,0.13432632,-0.03677513,-0.0601367,-0.08386856,-0.03516174,-0.00597727,0.02912259,0.01204839,0.02751767,0.02330182,-0.02446519,0.04959273,0.01070951,0.02008781,-0.02529608,0.03093114,0.09087199,0.06439905,-0.01678065,-0.02537238,0.03570021,0.01870001,-0.05753011,0.01640306,0.00251784,-0.00677687,-0.00444381,-0.09320014,0.05337328,0.0143744,-0.01093524,0.04509624,0.00807149,0.03655015,-0.02043586,-0.03921716,-0.01756042,0.07508702,0.13321416,0.00209745,0.04652452,-0.07985855,0.01567466,-0.04078066,-0.04076747,0.05816798,-0.01930878,-0.02078523,-0.01009314,0.0521948,0.0482806,-0.06909195,0.02333975,-0.00173346,0.03637531,0.00679211,0.04540142,0.04166306,-0.00726083,-0.02781515,-0.21041401,-0.00382987,0.02869061,-0.14233077,0.02199062,-0.01406598,0.04191292,-0.01709936,0.02140528,0.02664606,0.1158523,-0.0098619,-0.05778053,0.02250151,-0.04621929,0.01905524,-0.06837662,-0.01781592,-0.03867748,0.04806131,-0.01246694,-0.07245662,-0.00919056,-0.03714893,0.02670433,0.00364324,0.13279957,0.03476069,0.07627693,-0.02038289,-0.01916491,0.01905345,0.04297159,-0.17392193,0.00954808,0.06934021,-0.01249177,0.03450628,0.02181341,0.01201964,0.04963782,0.05340773,-0.055493,-0.10635215,-0.00298249,-0.03458926,-0.03091043,-0.00657446,-0.02530489,0.02325121,0.02253949,0.00617091,0.01214821,0.01555305,0.00120434,-0.05813005,-0.0284718,0.03264469,0.03394592,-0.00210712,0.07975735,0.00923917,-0.02574692,-0.0120254,0.05222698,-0.017786,-0.02059545,-0.06561308,0.03437848,-0.07724262,0.03429358,0.07565317,-0.005354,-0.00065737,-0.0123526,0.01122597,-0.00399332,-0.03110976,-0.03366859,-0.02422914,-0.04338397,-0.00603597,0.00814081,-0.02133859,-0.05796802,0.05179079,0.03205145,-0.04644143,0.04943178,-0.02888764,-0.03929373,-0.00925639,-0.00722907,-0.04606709,0.07091218,-0.0035017,-0.22260161,0.05964212,0.07758222,-0.06452923,-0.03468335,0.03696296,0.0797066,-0.03176596,-0.08003417,0.03129298,0.04226779,-0.01473374,-0.00558473,-0.01058437,0.06250506,0.01001079,0.07032155,-0.06118599,0.05296706,-0.09690668,0.05104237,0.0363001,0.21395127,-0.0457032,-0.02979081,0.01366933,-0.03223461,0.01612707,0.00784339,0.01780784,0.02098351,0.03094143,0.05765858,0.00301829,-0.01950774,0.09908411,-0.00416734,0.00194689,0.03791052,-0.00530508,-0.03378336,0.00518451,-0.00017582,-0.00615274,0.06142214,-0.08717093,-0.08462212,-0.01599415,-0.01126917,-0.06018303,0.02527637,-0.0862724,-0.00373476,-0.03740948,0.06864056,0.03817495,0.03791869,0.00085627,-0.09340938,0.00501458,0.00356042,-0.02011529,0.06683469,-0.01471945,0.038096]}},"text":null,"length":0,"last_read":{"hash":"4097d0c154e1bf41bd9d71768f6027ec32f0b905beb184255be6c4a635bdcfca","at":1736406354848},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Gradle#{1}","lines":[58,61],"size":386,"outlinks":[{"title":"https://axion-release-plugin.readthedocs.io/en/latest/configuration/ci_servers/#gitlab-ci","target":"https://axion-release-plugin.readthedocs.io/en/latest/configuration/ci_servers/#gitlab-ci","line":2},{"title":"https://github.com/jozala/blog-post-axion-release/blob/master/build.gradle","target":"https://github.com/jozala/blog-post-axion-release/blob/master/build.gradle","line":4}],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Gradle#{2}": {"path":null,"last_embed":{"hash":"1494c53f9068a294187a484e98e9fe6e06ff1075b379e350b8731aec32b92355","tokens":58},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03370527,-0.05832945,0.0253433,-0.08427229,-0.04966521,-0.02813083,-0.08180337,0.02548623,0.0295487,-0.01264738,0.02433805,-0.06981011,0.04438778,0.0533957,0.05744107,-0.01808227,-0.0319553,0.0802275,0.01598972,-0.04409748,0.05484854,-0.00846702,0.04097959,-0.0785502,0.02112083,0.09321817,0.03892108,-0.01309713,-0.02772313,-0.23661526,0.02455174,0.04852504,-0.04424074,-0.01179218,0.00753832,-0.0068045,-0.02502448,0.0245344,-0.07113152,0.03444845,0.01195589,-0.0011631,-0.01591886,-0.0311335,-0.04250171,-0.05428408,-0.03686615,-0.00204825,-0.05752043,-0.00512902,0.00035734,-0.06430519,-0.00650989,0.03766413,-0.02635591,0.04642631,0.0294446,0.05550423,0.06899723,0.01977739,0.10744648,-0.01662493,-0.20591724,0.06287519,0.05335478,0.07039011,-0.02050271,-0.08750403,-0.00554698,0.04918398,0.00638239,-0.0045322,-0.00298466,0.0510813,0.00721028,0.0301165,0.00095902,-0.01502265,0.01364642,0.01097711,-0.00860471,0.03789927,-0.01792696,-0.00152303,0.02093804,-0.02843166,0.02541214,-0.00075407,0.05049916,-0.03643739,-0.07087378,-0.07682918,0.03717428,0.01278385,-0.05216146,0.03993487,0.0330229,-0.0143115,-0.04412624,0.14269613,-0.02185583,0.01356405,0.02073849,0.00992799,0.04877354,-0.01053266,-0.05780376,-0.06483337,0.03945459,0.01045547,0.02713304,-0.05713122,0.07540418,-0.01477913,0.03258295,-0.02378971,-0.06231105,-0.00094824,-0.00290297,0.00204942,0.02274795,-0.03366211,0.11820351,-0.03294519,-0.01861297,0.01122112,-0.00631532,0.03070394,0.00592495,0.02864395,-0.01718719,0.01289751,-0.03660078,-0.05011154,0.04300353,-0.01496393,-0.03118663,-0.05444866,-0.01351899,-0.02240765,-0.05119977,0.01419187,0.02335726,-0.01179302,-0.02390575,0.11323969,-0.02356489,0.00933015,-0.00911019,-0.04562319,-0.0760242,0.03748102,-0.05706011,-0.01609855,0.01831408,0.05003064,0.01327064,-0.00254572,-0.07072998,0.05817746,0.03160312,-0.03634096,-0.00356402,0.10223961,-0.01178935,-0.10494024,-0.02973272,-0.02915563,-0.01618144,-0.01631709,0.03729367,-0.02258246,-0.00903793,-0.01128166,0.04320924,0.01270083,0.00061938,0.01649941,0.0040365,0.01727957,0.0657864,-0.03629671,0.00690777,0.03162269,-0.01977178,-0.02507851,0.03861095,-0.00675039,-0.00139079,0.01268312,-0.08176862,0.03728898,0.01457095,-0.01870517,0.04635424,-0.04557905,-0.00882985,0.06468783,0.00114266,-0.02132083,0.07391309,0.11225589,0.00636895,0.0303134,-0.05388601,0.05379805,-0.03807512,0.00683357,0.02608484,-0.02642135,-0.04961315,-0.00199052,0.11524484,0.03789795,-0.04618465,0.04178739,-0.00241975,0.04632176,-0.05437936,0.04253461,0.01327262,-0.06378172,-0.03466597,-0.22239015,-0.00590343,0.02676818,-0.08470732,0.01159268,-0.03676141,0.04688438,-0.03037793,0.01465448,0.0757302,0.11445311,-0.00488025,-0.01598884,0.02571785,-0.0566722,0.06472045,-0.02302527,-0.03351618,-0.04364702,0.02351881,-0.00979077,-0.03672899,-0.00278067,-0.06445069,0.018321,0.02002407,0.15093988,0.04223664,0.04714164,-0.0382623,0.00182657,0.04431587,0.03241662,-0.16949864,0.02352433,0.04993787,0.04392287,-0.01405349,0.02061388,-0.02064353,-0.00009967,0.03920125,-0.01622779,-0.10670049,-0.00816066,-0.02256878,-0.01762,-0.01917187,0.01127756,0.0392357,-0.02418488,-0.00248728,-0.00176079,0.04029578,0.01952776,-0.04986722,-0.04762112,0.02437335,-0.00619596,0.00620255,0.08915381,0.02378917,-0.0297227,-0.03302415,0.09626374,-0.03854616,-0.03624502,-0.03894782,0.05092539,-0.0424794,0.00579924,0.05328023,0.01489435,0.04072211,-0.020584,0.02640523,-0.00123612,-0.0445232,-0.03158294,-0.03617732,0.01477207,0.0089669,-0.0424772,0.00344635,-0.01767899,0.05770826,0.01319805,-0.01374093,0.07076851,-0.00150317,-0.00775787,-0.03177726,0.00111396,-0.02225788,0.09047708,-0.01186403,-0.22238018,0.01233447,0.01646178,-0.02488931,-0.04466251,0.04424402,0.05711731,0.00356494,-0.07332601,0.01537181,-0.02335097,0.01652464,0.02645354,0.02527491,0.02322719,0.01554727,0.06452485,-0.04022582,0.06723859,-0.11449667,0.04223862,0.09216988,0.22809631,-0.02827959,0.00425122,0.01280608,-0.04852846,0.00851145,0.0269657,0.01032236,-0.00820399,0.00768814,0.05982573,-0.01728636,0.02834766,0.13171367,0.00038716,-0.01010881,0.0519677,0.00178136,-0.02891936,0.01194613,0.03310701,0.04099236,0.05922086,-0.06636129,-0.05783776,-0.00024494,-0.01594701,-0.06865543,-0.02472017,-0.06387573,0.0089413,-0.01557141,0.05978482,0.05625396,0.04311616,0.01395048,-0.11412697,-0.01501,-0.01046042,0.00983403,0.06846584,0.01184104,-0.04705591]}},"text":null,"length":0,"last_read":{"hash":"1494c53f9068a294187a484e98e9fe6e06ff1075b379e350b8731aec32b92355","at":1736406354857},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Gradle#{2}","lines":[62,62],"size":90,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Gradle#{3}": {"path":null,"last_embed":{"hash":"5ecb58783865e2eb2709ce506798b8858f69fa4a03d5a95d3b6ada275aa101d1","tokens":140},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0196958,-0.01221663,0.00066133,-0.03575217,-0.00449734,-0.0270438,-0.09826112,0.02784771,0.02181595,-0.06813698,-0.0185101,-0.04005728,0.05262661,0.02733664,0.03633901,-0.03233895,-0.0077021,0.09730764,-0.04224295,-0.01780623,0.13521183,-0.01849443,0.01165355,-0.05442351,0.02177629,0.06557344,0.00737633,-0.01366061,-0.00239813,-0.20339134,-0.02713345,0.04291882,-0.04262876,0.03596504,-0.00192906,0.02128563,-0.04810147,0.064859,-0.08600532,0.03706525,0.0562843,0.0221501,-0.00826846,0.02556692,-0.04755933,-0.04418875,-0.06136341,0.02275423,-0.05651334,-0.02239445,-0.00184339,-0.05967302,-0.03954645,0.04258539,-0.03893055,0.03346795,0.02525427,0.04202417,0.01153295,0.00387135,0.06839961,0.02053474,-0.22421715,0.09195271,0.00792679,0.07750869,-0.03022998,-0.0437903,0.0377164,0.03069999,0.00543945,0.01533853,0.0021161,0.06536778,0.03143788,0.01886026,-0.01298312,-0.0125517,0.01741003,-0.00923509,-0.01302313,0.01620889,-0.00901745,0.03690506,0.00165226,-0.02772243,-0.01771312,-0.00692448,0.08517479,-0.00131636,-0.03571833,-0.0955644,0.07225148,0.0018783,-0.06987288,0.05685223,0.0316695,-0.04572969,-0.03439147,0.12539139,-0.03860026,0.03081726,0.07941262,0.05392934,0.03038572,-0.00365474,-0.03355774,-0.0733899,0.07350668,0.00830788,0.01957887,-0.04571569,0.04706699,-0.04401958,0.02173892,0.00654356,-0.0241752,-0.00245943,-0.02929528,0.01955188,0.012812,-0.03269163,0.09198084,-0.02670092,-0.03547349,-0.02413568,-0.02425671,-0.02066042,-0.01838637,0.02104975,0.01058596,0.04817829,-0.03429057,-0.05483776,-0.0020399,-0.0247591,0.02476343,-0.04950859,-0.02211137,-0.04550113,-0.04095708,0.03443753,-0.02584283,-0.0160656,-0.04337708,0.09927893,-0.03959155,0.06195531,-0.02259488,-0.04078714,-0.02434118,0.04749961,-0.02998862,0.00060278,0.02065961,0.04895392,0.0287689,0.07240775,-0.06883795,0.02832349,0.01828359,-0.03000432,-0.06133815,0.10973243,-0.00278913,-0.05131439,-0.0897164,-0.03040776,0.00348176,0.03408901,0.02422347,0.0025433,0.04451716,-0.00975216,0.04443265,0.03445777,0.03665071,0.03725077,0.05093327,0.06003969,0.08568285,-0.03545896,-0.01274791,0.02543988,-0.00675588,-0.04096449,0.03330709,0.01073097,-0.0139172,0.01604971,-0.10352455,0.071897,0.04597755,-0.04106748,0.03410707,-0.01981156,0.0191395,-0.00909461,0.01617732,-0.02966247,0.08639777,0.13285027,0.00203825,0.02195561,-0.08389887,-0.00154208,0.00746593,-0.04680174,0.03572878,0.0206586,-0.03189502,-0.02011355,0.07165421,0.06093837,-0.04899474,0.00925364,-0.01443652,0.03234406,-0.00794587,0.02667088,0.03246919,-0.02326567,-0.00947868,-0.19214447,0.00424353,-0.00435469,-0.11662116,0.00969899,-0.03728816,0.03227523,-0.04608162,-0.0200405,0.04438476,0.12584424,-0.02098349,-0.04895566,-0.01445738,-0.0399618,0.02040868,-0.03730796,-0.0374149,0.01352712,0.04027091,-0.02682801,-0.05148315,-0.01066814,-0.03850638,0.02977608,-0.03824805,0.16889407,0.04172394,0.05205166,-0.00536209,0.00648135,0.00719744,0.02766944,-0.1651721,0.04772252,0.06813654,-0.03594825,-0.01049625,0.04575495,-0.02804569,0.03204184,0.0501723,-0.03537636,-0.08380631,-0.01925551,-0.03116902,-0.03513191,0.00610673,-0.00977151,0.04075998,-0.0104143,0.02656588,0.00586546,0.00909696,0.02739536,-0.07408334,-0.06184111,0.03322757,0.03765031,-0.00806039,0.06514867,-0.01829,-0.03347921,-0.01228075,0.05263674,-0.05178251,-0.02523722,-0.04996229,0.03755396,-0.04363084,0.06744982,0.06212457,0.02898751,-0.02299682,0.01328565,0.03411753,0.01858438,-0.02496175,-0.0185102,-0.05824045,-0.02713737,0.00413078,0.0055121,-0.04671552,-0.0337678,0.03467342,-0.01883063,-0.04871412,0.02004258,-0.02248426,-0.04813237,-0.00073887,-0.01365945,0.01022092,0.05710306,-0.03034294,-0.22784561,0.01860674,0.04221312,-0.06617482,-0.01683161,0.06478411,0.06386197,-0.01352413,-0.09769741,-0.001632,0.03530863,-0.0107027,0.02739262,-0.01258221,0.04793527,-0.01361007,0.08445651,-0.05458878,0.07139625,-0.07439791,0.06740623,0.06768502,0.20325236,-0.02929809,-0.00344185,-0.01673236,-0.02975297,0.01330735,0.0438376,0.00483385,0.00969438,0.01215439,0.10758214,0.02079449,-0.01530544,0.12320715,-0.01044414,-0.02114929,0.02483001,0.00849955,-0.02266252,0.011084,-0.02653412,-0.00544764,0.05559796,-0.08430043,-0.06263147,-0.00792156,-0.01871763,-0.08642498,0.02780138,-0.02036533,-0.03373617,0.00400516,0.07064072,0.03963856,0.04750201,0.00750119,-0.08520651,-0.02363312,-0.02264431,0.01613619,0.04939653,-0.00701384,0.01561738]}},"text":null,"length":0,"last_read":{"hash":"5ecb58783865e2eb2709ce506798b8858f69fa4a03d5a95d3b6ada275aa101d1","at":1736406354863},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Gradle#{3}","lines":[63,63],"size":220,"outlinks":[{"title":"https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Gradle.gitlab-ci.yml","target":"https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Gradle.gitlab-ci.yml","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Gradle#{4}": {"path":null,"last_embed":{"hash":"4c37bcaa3139dff94a5401b6fd1245e8a21418dcfa92d937810f6739cbb7d0a5","tokens":268},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01399223,-0.00409635,0.00619779,-0.03323162,-0.01738301,-0.0143365,-0.08653535,0.03990439,0.02069069,-0.04858548,-0.02865432,-0.04191443,0.058598,0.0388737,0.05099832,-0.02613587,-0.01134862,0.09217976,-0.05626228,-0.00955811,0.13850157,-0.03690189,-0.00083971,-0.05809469,0.01672797,0.05960762,0.01520722,-0.01363802,0.00047126,-0.21263258,-0.03308987,0.05153724,-0.03316981,0.03689324,-0.01135789,0.02030267,-0.05848959,0.06432816,-0.08525217,0.03974795,0.0587323,0.01396079,-0.00934922,0.02255864,-0.02002795,-0.03744272,-0.06703763,0.01380922,-0.06824587,-0.00770791,-0.00834598,-0.0433761,-0.01443509,0.01584538,-0.052843,0.01189029,0.01996328,0.03554479,0.01282614,0.01502104,0.08975238,0.03044298,-0.23974994,0.0816756,0.014402,0.08536356,-0.04583018,-0.04806289,0.03271008,0.01857319,-0.00350172,0.01302699,0.00568164,0.06384331,0.01026771,0.02909333,-0.01565799,-0.01584143,0.02483963,-0.01046736,-0.02110947,0.01068995,-0.0368651,0.03468648,0.00915893,-0.01908035,-0.00575908,-0.00887397,0.09484891,-0.00113284,-0.02252501,-0.08396944,0.07132958,-0.00544353,-0.04860248,0.07073368,0.03829382,-0.0441309,-0.0500633,0.12359205,-0.03141505,0.01642455,0.07829294,0.03742014,0.03003991,-0.00290084,-0.03843284,-0.07120046,0.05836188,-0.00943137,0.01065593,-0.04656585,0.03783353,-0.03375795,0.02258067,0.0116661,-0.01172497,-0.00904835,-0.00436561,0.02731088,0.02809753,-0.03717284,0.06581528,-0.02389201,-0.01631316,-0.00743347,-0.02344997,-0.00893715,-0.0017308,0.03990638,0.01789647,0.05853493,-0.01817351,-0.05116814,0.00403719,-0.03773921,0.0202632,-0.04750844,-0.03837002,-0.04312472,-0.03140581,0.02582154,-0.03705499,-0.01129995,-0.03401592,0.08437501,-0.05227735,0.05509505,-0.02387546,-0.02646575,-0.02051862,0.05126996,-0.02025079,0.0048251,0.01584272,0.06941167,0.02154442,0.05676413,-0.07735825,0.03287369,0.02935162,-0.01889621,-0.05729382,0.11943214,0.00933325,-0.07089847,-0.08082701,-0.02137867,0.01656973,0.03458825,0.03165141,0.01408957,0.02560288,-0.01477976,0.04983858,0.00483031,0.01363584,0.04159344,0.04266923,0.08469214,0.08801676,-0.03130591,-0.00505461,0.04098175,-0.00547744,-0.04983768,0.01551704,0.01859013,-0.00159392,0.00353654,-0.08983112,0.0708136,0.05582874,-0.03089539,0.02254508,-0.03351802,0.02251474,-0.00807009,0.01837716,-0.03714745,0.09972431,0.13645452,0.01842061,0.02208958,-0.06618033,0.00114515,-0.01392064,-0.03833294,0.0343115,0.02292774,-0.04077904,-0.02315035,0.07653882,0.05017025,-0.04293276,0.00005134,-0.02657306,0.02627924,-0.00439058,0.03622366,0.01382202,-0.02631992,-0.02339228,-0.2039585,0.00549805,0.00404518,-0.11244157,-0.00937725,-0.0360413,0.02416733,-0.0491512,0.00082268,0.02565321,0.11197881,-0.00137884,-0.0418798,0.01906958,-0.05584708,0.02808681,-0.05129435,-0.02617607,-0.00060269,0.04103833,-0.02576524,-0.05271218,-0.00448032,-0.04954528,0.01548502,-0.01791335,0.18181185,0.04172712,0.03448406,-0.00320268,0.00719165,0.00866708,0.02826462,-0.15759067,0.0353651,0.06993514,-0.03281399,-0.0025166,0.03779168,-0.02784916,0.04186147,0.02982562,-0.04035897,-0.09543262,-0.03917742,-0.0404199,-0.03846595,-0.00571266,-0.01632563,0.04461609,-0.01808686,0.02010906,0.00499778,0.00660525,0.02898734,-0.06008487,-0.08924218,0.04066785,0.05102718,-0.01120782,0.07087407,-0.02324392,-0.03473423,-0.02489878,0.04490716,-0.04291091,-0.04549538,-0.04057934,0.02485534,-0.03042806,0.06048046,0.07700291,0.02928452,-0.02632627,0.03480045,0.02085605,0.01072239,-0.00937052,-0.01317837,-0.03936515,-0.03897687,-0.00063581,0.01729549,-0.0465764,-0.02199642,0.04670398,-0.02760634,-0.03117544,0.0188305,-0.03627937,-0.04065073,0.00672474,-0.01067053,0.00689879,0.0728716,-0.02514801,-0.23730005,0.02646725,0.02879209,-0.05730822,-0.00959352,0.05547479,0.0539334,-0.00296802,-0.10699446,-0.00227924,0.01124794,-0.00422126,0.03400526,-0.01771464,0.04789394,0.00055101,0.07914308,-0.06273726,0.0718646,-0.07203963,0.06900287,0.06701142,0.21954051,-0.03298621,-0.00605415,0.01861734,-0.04460072,0.00738334,0.05194255,0.0216983,-0.0105985,0.01684033,0.09228028,0.02051682,0.00007073,0.09094714,-0.01708876,-0.00700988,0.00389638,-0.00969517,-0.03378302,0.00201106,-0.0115889,0.00037389,0.04935944,-0.07145892,-0.05607159,-0.00578837,-0.01547765,-0.06737696,0.00473417,-0.01735194,-0.0282675,-0.01216805,0.0687419,0.0499497,0.03785532,0.01194656,-0.08760601,-0.04481001,-0.02672092,0.01536799,0.05348972,0.01213092,0.00885603]}},"text":null,"length":0,"last_read":{"hash":"4c37bcaa3139dff94a5401b6fd1245e8a21418dcfa92d937810f6739cbb7d0a5","at":1736406354871},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Gradle#{4}","lines":[64,66],"size":416,"outlinks":[{"title":"https://gitlab.com/gitlab-org/gitlab/-/tree/master/lib/gitlab/ci/templates?_gl=1*10mhcse*_ga*NzQzOTM0NTIzLjE2ODIxMDkxOTE.*_ga_ENFH3X7M5Y*MTY4Mjk5MDEzMi4xNi4xLjE2ODI5OTE5NjQuMC4wLjA","target":"https://gitlab.com/gitlab-org/gitlab/-/tree/master/lib/gitlab/ci/templates?_gl=1%2a10mhcse%2a_ga%2aNzQzOTM0NTIzLjE2ODIxMDkxOTE.%2a_ga_ENFH3X7M5Y%2aMTY4Mjk5MDEzMi4xNi4xLjE2ODI5OTE5NjQuMC4wLjA","line":2}],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Gitlab other": {"path":null,"last_embed":{"hash":"fe224d6dc30ae82067e167e226cdd5a37dc0b481e0b7046d37d737a26a5f6318","tokens":456},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05884372,-0.03924599,0.02143044,0.01867031,-0.0430172,-0.02860793,-0.08813453,0.03798001,-0.01727146,-0.06418058,-0.00091361,-0.03598971,0.02740018,0.03608976,0.05053742,-0.02187878,-0.04567326,0.04937156,-0.0304949,-0.03411958,0.08933854,-0.03746852,0.02170953,-0.04868929,0.02717545,0.0684706,0.01877953,-0.04259797,-0.02548719,-0.2248645,-0.03790706,0.02092578,-0.05658132,0.04241751,-0.00776822,0.02791866,-0.0502883,0.06834915,-0.10463127,0.07433897,0.07823173,0.05034963,-0.00629577,-0.00784571,-0.0199947,-0.06645741,-0.04221588,-0.00458032,-0.0474446,0.0091428,-0.00884707,-0.03934202,-0.03691735,0.02657641,-0.03864853,0.01227436,0.04234591,0.05117747,0.02501997,-0.01100464,0.1090136,0.0321197,-0.18130898,0.08263592,0.0193612,0.03422053,-0.02869561,-0.06256422,0.05310557,0.04485052,0.00128035,0.04326975,-0.00885713,0.05597858,0.02007402,0.02415641,0.00110868,0.0197264,0.00443348,-0.00576149,-0.04988028,0.00727166,-0.04192482,0.04918534,-0.01036494,-0.01683901,0.01423424,-0.02301649,0.07293351,-0.01455742,-0.03074348,-0.09183343,0.07572155,-0.00792203,-0.03548059,0.06833608,0.03165061,-0.00254655,-0.03416602,0.11249462,-0.03149453,0.0103164,0.03662618,0.04486157,0.0428651,0.01157697,-0.03096709,-0.08911527,0.04628899,-0.01782406,0.03265493,-0.03240949,0.06035826,-0.05122004,0.03873964,0.03483252,-0.02511133,-0.00164333,0.00452663,0.03321405,0.07137729,-0.02071589,0.07926155,0.01302131,0.01641286,-0.01669859,0.01068704,-0.01411153,0.00823578,0.01654322,0.03718315,0.01388551,-0.03182117,-0.03264884,0.00455512,-0.02237196,0.00710679,-0.05499761,-0.01552755,-0.03127504,-0.03511031,0.02248164,0.00455989,-0.06113245,-0.05451553,0.11723483,-0.04905736,0.05572826,-0.03916335,-0.04445545,0.00209497,0.00182528,-0.01677857,-0.02779993,0.02228247,0.0466215,-0.00100699,0.07285278,-0.0802644,0.01005881,0.01341281,-0.02054621,-0.05098778,0.12680502,0.00641603,-0.04902921,-0.08540697,-0.05087345,0.03821237,0.00463252,0.02682174,0.01374164,0.00567549,-0.00115287,0.06267861,0.0038982,0.01025991,-0.0207744,0.03768111,0.10381489,0.09215126,-0.02203599,-0.01264334,0.03042969,0.02258707,-0.07359901,0.01399193,0.03294054,0.00393775,0.00891945,-0.07967974,0.04891867,0.05453543,0.00589018,0.00577422,-0.04092409,0.0126555,-0.01689015,-0.04067305,-0.01792778,0.07946232,0.11966851,0.02029037,0.01474816,-0.06301573,-0.0142463,-0.02380846,-0.08590498,0.03503451,0.01921441,-0.04568213,-0.05101129,0.01113981,0.05901023,-0.0731404,0.00294804,-0.02864524,0.03602108,-0.00715958,0.04159021,0.00916509,0.01897727,0.00415449,-0.19841553,-0.0334602,0.01236479,-0.08410827,0.05502439,-0.04676238,0.0406857,-0.04751797,0.00058122,0.03357577,0.06802396,-0.02501876,-0.04044821,0.02600703,-0.02056806,0.01337249,-0.08418576,0.01608645,0.00019576,0.04310321,-0.02963322,-0.02650801,-0.01993047,-0.07019606,0.04289766,-0.03304828,0.19600274,0.02940391,0.03218153,0.00223262,-0.00171659,0.0214503,0.03677043,-0.16098365,-0.00437729,0.06966209,-0.02644558,0.03171882,0.05293097,-0.02206862,-0.01223137,0.03333383,-0.03038554,-0.11878663,-0.03143773,-0.07695715,-0.03310017,-0.04756891,0.00495764,0.02079679,0.01999698,0.03250854,-0.04052776,0.00874193,0.00645331,-0.04494377,-0.0153591,0.05256566,0.04053043,-0.00868529,0.04270709,-0.01827325,-0.01799681,-0.00073582,0.05693969,-0.0162615,-0.00616887,-0.01722662,0.00317064,-0.05308601,0.05158924,0.13822816,0.00871386,-0.03353061,0.00008786,0.04043341,-0.00496576,0.00893457,-0.05158133,-0.04730555,-0.00951891,-0.05992017,0.02697914,-0.03129118,-0.03104586,0.07634413,0.02522867,-0.0558968,0.00914543,-0.01538519,-0.05915553,0.0046226,-0.02481944,-0.00182244,0.08184236,0.01593838,-0.23479013,0.00997333,0.05405028,-0.09460162,0.00528102,0.05133329,0.05894754,-0.02549797,-0.09381567,0.00730124,0.01962203,0.02144396,0.01131252,-0.01576771,0.04686901,-0.00542176,0.04866502,-0.05062956,0.07746585,-0.08435134,0.00025316,0.06388988,0.22060695,-0.00305833,0.00477677,0.00823891,-0.03420887,0.01447633,0.06673463,-0.00183571,-0.00798908,0.02602157,0.06715176,0.00856996,-0.02315589,0.07796928,-0.00968248,-0.01199403,0.00799549,0.02748764,-0.03308742,-0.00556583,0.00302163,0.01001894,0.0634744,-0.02825827,-0.04235178,-0.03937571,-0.01590282,-0.04576265,0.02627838,-0.03237076,-0.02465425,-0.02005765,0.09852694,0.06676634,0.03574821,0.0294951,-0.0626051,0.0070651,0.02050389,-0.01565778,0.02599228,0.02016681,0.00303851]}},"text":null,"length":0,"last_read":{"hash":"fe224d6dc30ae82067e167e226cdd5a37dc0b481e0b7046d37d737a26a5f6318","at":1736406354882},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Gitlab other","lines":[67,155],"size":3434,"outlinks":[{"title":"https://about.gitlab.com/blog/2017/11/02/automating-boring-git-operations-gitlab-ci/","target":"https://about.gitlab.com/blog/2017/11/02/automating-boring-git-operations-gitlab-ci/","line":3},{"title":"https://marcosschroh.github.io/posts/autobumping-with-gitlab/","target":"https://marcosschroh.github.io/posts/autobumping-with-gitlab/","line":4},{"title":"https://forum.gitlab.com/t/is-it-possible-to-commit-artifacts-into-the-git/22218/7","target":"https://forum.gitlab.com/t/is-it-possible-to-commit-artifacts-into-the-git/22218/7","line":5},{"title":"https://threedots.tech/post/automatic-semantic-versioning-in-gitlab-ci/","target":"https://threedots.tech/post/automatic-semantic-versioning-in-gitlab-ci/","line":6},{"title":"https://quarkus.io/blog/*********************quarkus-with-gitlab/","target":"https://quarkus.io/blog/*********************quarkus-with-gitlab/","line":7},{"title":"https://blog.jdriven.com/2021/11/reuse-gradle-build-cache-on-gitlab/","target":"https://blog.jdriven.com/2021/11/reuse-gradle-build-cache-on-gitlab/","line":8},{"title":"https://neron-joseph.medium.com/tag-your-git-repo-using-gitlabs-ci-cd-*********************","target":"https://neron-joseph.medium.com/tag-your-git-repo-using-gitlabs-ci-cd-*********************","line":12},{"title":"https://dev.to/vumdao/config-gitlab-runner-to-push-a-tag-3l5g","target":"https://dev.to/vumdao/config-gitlab-runner-to-push-a-tag-3l5g","line":13}],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Gitlab other#{1}": {"path":null,"last_embed":{"hash":"ef1187c39379e4364ab03585f1469835487333de7fec38a249a7a253ca62960a","tokens":103},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04181936,-0.04135256,0.05124396,0.02211573,-0.02624357,-0.02824399,-0.04263306,0.03822921,0.00407018,-0.05909098,-0.00871757,-0.01811918,0.02787566,0.02320268,0.06577735,-0.02170344,-0.03682821,0.04815914,-0.04645156,-0.06771496,0.12692825,-0.02893059,0.0220315,-0.0560342,0.03543828,0.06585735,-0.00023725,-0.02742635,-0.03160172,-0.20990878,-0.06262986,0.01800129,-0.02515215,0.03263755,0.01378603,0.00562927,-0.04672994,0.07736103,-0.11261511,0.05738568,0.07400911,0.03876897,-0.02472603,-0.00655839,-0.02438859,-0.06111801,-0.05335147,0.00087774,-0.0675219,-0.00118439,-0.00238105,-0.05559904,-0.03846711,0.0182449,-0.03932881,-0.00232259,0.0422611,0.04687329,0.03501217,0.00368739,0.11508738,0.01638508,-0.18680555,0.06760164,0.00797662,0.03676658,-0.02478664,-0.07671499,0.06857228,0.04308699,0.0288765,0.01923399,-0.0115311,0.06795704,0.01606149,0.02699778,-0.01844331,0.02182667,0.00604282,0.00974197,-0.01309937,-0.01546569,-0.03281666,0.02638694,0.01559103,-0.04750022,-0.01359428,0.01360827,0.10228305,0.00462945,-0.01884086,-0.08513578,0.1018405,-0.01438599,-0.02466797,0.05769769,0.02206054,-0.01637182,-0.03530942,0.13355088,-0.03375224,0.01996405,0.05905465,0.03845598,0.02685278,0.02543141,-0.03365899,-0.04863075,0.03941697,-0.01066815,0.02019276,-0.02093867,0.06299265,-0.06908941,0.03239252,0.03802738,-0.00574544,0.03185615,0.0036011,0.03810371,0.05302164,-0.04728321,0.0754004,0.01375786,0.01236038,-0.00498053,0.02317412,0.00565068,-0.01027984,0.00570077,0.0367718,0.0201998,-0.03255944,-0.01980048,0.00468827,-0.02451978,0.01040663,-0.04440695,-0.03738925,-0.03013189,-0.02881652,0.02550831,0.00145779,-0.06247764,-0.03760905,0.10281545,-0.04373077,0.0469585,-0.02458281,-0.04947831,-0.00446024,-0.00079561,-0.00842133,-0.01882342,0.00730521,0.05353403,0.01515679,0.05801267,-0.07584879,0.00647125,0.00395304,0.00654913,-0.0495795,0.12473167,0.00458703,-0.06205081,-0.06370437,-0.05861276,0.03166334,0.00759544,0.01729541,0.00629819,0.01227958,-0.01242891,0.07149047,0.0111513,0.02055348,0.00754245,0.02354694,0.1060569,0.0975076,-0.0275487,0.00784032,0.03786165,0.01214071,-0.07514841,0.01158271,0.02387005,-0.00416399,0.0040864,-0.09510656,0.0120666,0.03504123,-0.00739231,0.01317114,-0.03593604,0.01929967,-0.01851983,-0.0321962,-0.00602912,0.07535943,0.10875083,0.04327726,0.01921299,-0.09464427,-0.00442117,-0.00450866,-0.07903723,0.03787579,0.0128291,-0.06995497,-0.0452478,0.03343368,0.04895151,-0.06350712,-0.00426618,-0.04117686,0.03323621,-0.02388957,0.03158388,-0.00541223,0.04876168,-0.01270787,-0.21659446,-0.03076527,0.00509086,-0.10846095,0.05013719,-0.06244015,0.03150917,-0.03658953,-0.01178869,0.06477588,0.08901455,-0.02131106,-0.05858096,0.02910375,-0.00750319,0.03650536,-0.07011537,0.01016945,-0.0067237,0.05376519,-0.00089416,-0.02119065,0.03044613,-0.07006788,0.00822629,-0.01338468,0.18025343,0.03913522,0.08568642,0.009165,0.00491133,0.00926088,0.01852833,-0.13840729,-0.01668108,0.03648008,-0.01542008,0.00533347,0.04673008,-0.02128133,-0.02399157,0.02777397,-0.03422512,-0.12028467,-0.00322234,-0.06965691,-0.02802862,-0.03658205,-0.0140374,0.01856575,-0.00173945,0.03666356,-0.02797906,0.01355376,0.02408749,-0.05273974,-0.03436092,0.02834743,0.02921247,-0.01923683,0.03915948,-0.02592353,-0.03278043,-0.02400672,0.04625106,-0.01580192,-0.01360226,-0.04553583,0.00455059,-0.03261342,0.03051055,0.12264826,0.00224252,-0.05613316,-0.03300834,0.0446615,-0.00024335,0.02186283,-0.04691354,-0.03719506,-0.02292388,-0.04495993,0.03570726,-0.01913719,-0.01780465,0.04390197,0.00855096,-0.0446768,0.03020059,-0.03907016,-0.06375368,0.01527268,-0.02774884,-0.00197119,0.0877846,0.0166182,-0.24171256,0.02306128,0.05736288,-0.08561454,-0.00380052,0.04919604,0.05248824,-0.02887283,-0.0843408,-0.00504027,0.0486252,0.0221732,0.01437726,0.00014659,0.04362161,-0.00392444,0.05156804,-0.04898805,0.07770287,-0.0525959,0.01393286,0.0499069,0.23209427,-0.00342606,-0.01013063,0.01857769,-0.01826773,-0.00321676,0.04646248,0.00400812,0.00558438,0.03212227,0.07291517,-0.01731797,-0.01998527,0.05839287,0.02135034,0.00068045,0.01366912,0.01812385,-0.04862456,-0.00418711,-0.00130592,-0.0032484,0.06382107,-0.01290087,-0.05796711,-0.04826014,-0.02762485,-0.06201167,0.03130458,-0.05764471,-0.03275285,-0.00480747,0.09497623,0.07627098,0.03009353,0.02725252,-0.06740385,0.00374056,0.01814175,-0.0342833,0.0266295,0.00718775,0.00755085]}},"text":null,"length":0,"last_read":{"hash":"ef1187c39379e4364ab03585f1469835487333de7fec38a249a7a253ca62960a","at":1736406354898},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Gitlab other#{1}","lines":[69,69],"size":174,"outlinks":[{"title":"https://about.gitlab.com/blog/2017/11/02/automating-boring-git-operations-gitlab-ci/","target":"https://about.gitlab.com/blog/2017/11/02/automating-boring-git-operations-gitlab-ci/","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Gitlab other#{2}": {"path":null,"last_embed":{"hash":"d774e3f457a34674d27585f14047e1cd289a975646413690b46c779804ec6620","tokens":85},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04275371,-0.02918738,0.00934088,-0.00559747,-0.02137204,-0.03714146,-0.07238663,0.02285043,-0.01528737,-0.05859037,-0.00479157,-0.04750085,0.04217082,0.03188172,0.05733404,0.01026568,-0.02313954,0.07171606,-0.02537004,-0.03406096,0.09734342,-0.03402156,0.02446685,-0.04345513,0.04419823,0.0682641,-0.00912701,-0.029559,-0.01317275,-0.193812,-0.04437229,0.01450396,-0.03509104,0.01478354,0.00894199,-0.00405607,-0.05446869,0.03244621,-0.08891246,0.05321847,0.08247694,0.04675187,-0.03741162,0.0082776,-0.01248267,-0.03230727,-0.01942937,0.01361503,-0.0555745,-0.00636044,0.03326883,-0.03237561,-0.00894217,0.0293623,-0.04554781,0.02016529,0.02918964,0.06755035,0.03189382,0.01408071,0.10990053,0.03319464,-0.20123336,0.07635894,0.03357386,0.06409416,-0.03517295,-0.04069987,0.04906039,0.03909324,0.00824126,0.01439069,0.00460459,0.0910328,0.01777411,0.00675287,-0.0000543,0.02571696,0.01338322,-0.01372741,-0.0388804,-0.01171144,-0.02079999,0.03804775,0.00076124,-0.02922997,0.00436322,-0.01621791,0.09582578,-0.00574051,-0.02734795,-0.10589896,0.10470748,0.00183396,-0.03849868,0.09539454,0.03171878,-0.04190627,-0.0171092,0.13079081,-0.04092491,0.01983353,0.06668,0.03199671,0.03482881,-0.004848,-0.03677898,-0.0580305,0.03915969,-0.01848068,0.0244859,-0.03090423,0.04282966,-0.05685943,0.04810205,-0.00230761,-0.031065,-0.01079947,-0.01585227,0.04184372,0.05523057,-0.06495268,0.07966237,-0.02678904,0.00572918,0.0038502,0.01485916,-0.0100348,0.00893388,0.02576088,0.02734471,0.02440195,-0.03906428,-0.03943276,-0.00135088,-0.02810635,0.02882303,-0.05945007,-0.00256221,-0.01888044,-0.04567249,0.02267293,0.00400007,-0.03311751,-0.0420787,0.09089845,-0.06526802,0.04749813,-0.04084542,-0.05323252,-0.01178321,0.01959903,-0.04906806,0.00639429,0.05132272,0.07921013,0.01374543,0.07926144,-0.0568459,0.02034396,0.0186849,-0.01955304,-0.04804052,0.09496751,-0.00770267,-0.06100826,-0.08049867,-0.03254817,0.00903685,0.03952709,-0.00548449,0.01954712,-0.00125369,-0.03424167,0.05101495,0.00100931,0.02675202,0.02238016,0.02838039,0.09706897,0.05471325,-0.01367378,-0.00732905,0.02278893,0.00302846,-0.05531069,0.00492101,0.01509929,-0.01292028,0.0131405,-0.07774436,0.03906703,0.04242856,-0.04205487,0.04124802,-0.01788848,0.02639412,-0.01920848,-0.01243198,-0.0119967,0.09389829,0.15491109,0.01718871,0.01596307,-0.05666139,-0.00374696,-0.01396395,-0.06036906,0.03321069,0.00849545,-0.03981568,-0.03579945,0.04723581,0.04962128,-0.07862737,-0.01994401,-0.04970934,0.04033124,0.00925322,0.05475612,-0.00910504,0.01420593,-0.03756532,-0.20828669,-0.03342908,0.01141419,-0.09924842,0.0284195,-0.05202067,0.02574711,-0.0290253,-0.00332669,0.03509004,0.12060191,-0.02065242,-0.02819715,-0.00626928,-0.03130169,0.01943295,-0.07154801,-0.01615616,-0.0014328,0.04268972,-0.00925455,-0.04382535,0.00222985,-0.0471028,0.01210841,-0.04048454,0.17751151,0.05242702,0.04165881,-0.00460901,0.00656948,0.00007915,0.01948072,-0.1742146,-0.00734731,0.05284319,-0.03707106,0.00435265,0.03217569,-0.00818187,0.00593237,0.05416218,-0.0365482,-0.11855764,-0.04016753,-0.08982799,-0.01570574,-0.03387896,-0.0026295,0.02180261,-0.02358247,0.03420163,-0.03637405,0.00610007,0.02862049,-0.05847919,-0.03582809,0.04418986,0.05034452,-0.00069847,0.07502053,-0.02514058,-0.03563535,-0.0276732,0.0672792,-0.05503597,-0.05262141,-0.03256382,0.01328791,-0.02415481,0.04665517,0.08957113,-0.00303218,0.00665785,-0.01513527,0.04655411,0.0022453,-0.01353882,-0.06139007,-0.02986338,-0.02938904,-0.03776253,0.04409552,-0.01690161,-0.0113707,0.07962325,-0.00401042,-0.03978683,0.02537391,-0.0386207,-0.05578794,-0.00833934,-0.03491591,0.01816283,0.04585434,-0.00001799,-0.21408106,0.03263741,0.04396304,-0.09950002,-0.00874292,0.05737532,0.07560276,-0.04218231,-0.11697532,0.00781746,0.01442306,0.02023193,0.01459747,-0.00601296,0.05882632,0.03046,0.04978891,-0.04089791,0.0713233,-0.07793014,0.0308614,0.03178327,0.21814181,-0.00222648,0.00915823,0.03057052,-0.02667255,0.00211866,0.05904996,0.00173534,-0.00155754,0.02404722,0.09771626,-0.00017433,-0.01478265,0.09589303,-0.00838884,0.00420239,0.02496,-0.00052532,-0.04468486,-0.01694947,-0.0085432,-0.00903804,0.05875624,-0.05592273,-0.05426865,-0.03626294,-0.0208182,-0.04009598,0.01303862,-0.05117131,-0.03449738,-0.02518099,0.09048084,0.05619123,0.05371832,0.0327008,-0.08694772,-0.01343399,0.02303394,-0.00298773,0.02664388,-0.00529007,0.02658307]}},"text":null,"length":0,"last_read":{"hash":"d774e3f457a34674d27585f14047e1cd289a975646413690b46c779804ec6620","at":1736406354906},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Gitlab other#{2}","lines":[70,70],"size":128,"outlinks":[{"title":"https://marcosschroh.github.io/posts/autobumping-with-gitlab/","target":"https://marcosschroh.github.io/posts/autobumping-with-gitlab/","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Gitlab other#{3}": {"path":null,"last_embed":{"hash":"e8e20ff632a8bf02fdbc368858bee4ec3257e356875ffb07c10421809aa0b678","tokens":103},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04028173,-0.04055116,0.01344212,-0.00945271,-0.01912088,-0.02310132,-0.07453122,0.0420393,0.00759302,-0.04270488,0.00153744,-0.04548834,0.01778347,0.02272447,0.02652317,-0.03023089,-0.05190563,0.08027194,-0.03917568,-0.00481645,0.08869778,-0.00985017,0.03171542,-0.03362646,0.03931422,0.06957693,0.00002076,-0.06593423,-0.0354814,-0.17321701,-0.05236317,0.02320324,-0.07812135,0.02374996,0.01025142,0.01723535,-0.03480215,0.02884802,-0.08669402,0.07047243,0.07616401,0.01595453,-0.0359962,-0.0104433,-0.02731522,-0.07093438,-0.00698871,0.00210242,-0.00999546,0.01284314,0.02403297,-0.02182718,-0.02380203,0.06028371,-0.04281861,0.06180896,0.02649811,0.05672072,0.03811823,-0.04076143,0.08533432,0.01011375,-0.17622182,0.09122209,-0.00296324,0.05354591,0.00960014,-0.02571214,0.05303789,0.01574896,0.00653457,0.02448949,-0.06007957,0.0449698,0.02694069,-0.00139541,0.03171248,0.03099167,0.01249314,-0.01318098,-0.05822128,0.00426531,-0.01468901,0.05588579,-0.04447037,-0.04580503,0.00427866,0.00549053,0.05167551,0.00033469,-0.03579492,-0.09460311,0.10334285,-0.01840902,-0.04823519,0.06212039,0.0519227,-0.02203236,-0.01779797,0.1483327,-0.07121983,0.01948281,0.0569318,0.051064,0.05346366,0.01566604,-0.02926784,-0.08247983,0.0353484,-0.02526317,0.01205897,0.01394682,0.04028402,-0.05030981,0.06462708,0.03329314,-0.04314761,0.00588905,-0.03762032,0.02490517,0.03086947,0.00697754,0.07570142,0.00239272,-0.00383311,-0.0276895,0.01012684,-0.04201695,0.02139075,0.03760369,0.00739183,0.00570782,-0.05408217,-0.03215208,-0.01195694,-0.03085664,0.02888769,-0.05712489,0.00183891,-0.04014982,-0.04699876,0.02790457,0.02643181,-0.03698831,-0.04618107,0.09456957,-0.03196886,0.04950557,-0.04319113,-0.05239705,-0.03889781,0.00325048,-0.02873633,-0.0080919,0.03933192,0.01125847,0.02502891,0.03714912,-0.06826967,0.0209236,0.04321627,-0.04300618,-0.02112283,0.13324468,0.02665026,-0.05140472,-0.07324243,-0.06843733,0.04381225,-0.0266517,-0.02421905,0.02045684,0.02725726,0.03062619,0.08068644,-0.02285675,0.03965816,-0.02140559,0.01693389,0.08606225,0.08938792,-0.01313957,-0.01477076,0.03859116,-0.00011147,-0.07257559,0.00664578,-0.00061153,0.03102153,0.01440905,-0.05825642,0.08757863,0.03086238,-0.00044743,0.05796348,-0.03414671,0.0265604,-0.00431038,-0.04686247,-0.024562,0.07458523,0.09808931,-0.02962069,0.02049707,-0.0571542,-0.00697042,-0.00751782,-0.08320135,-0.00306299,0.01764677,-0.05433057,-0.03828928,0.04842254,0.05960294,-0.06466688,0.03254309,-0.02452852,0.04582929,0.01811741,0.01613371,-0.02542382,0.01395187,0.03661365,-0.19939917,-0.02712989,0.02162107,-0.05335267,0.0480257,-0.06727121,0.03785364,-0.03483966,-0.00949141,0.03088653,0.09036174,0.01154241,-0.04229746,0.03226803,-0.01309005,0.01954863,-0.06816263,0.00022464,0.02317563,0.01655871,-0.04980901,-0.0369732,-0.06726009,-0.03556826,0.03286341,-0.0384117,0.20215762,0.05382841,0.01021166,-0.0428221,0.00465218,0.02235043,0.03171537,-0.20001584,0.00129864,0.03634942,-0.04598168,-0.00064509,0.07931288,-0.03656998,0.00462197,0.02536342,-0.05749936,-0.10601158,-0.01429469,-0.03538128,-0.04910569,-0.0383118,0.04125529,0.01473059,0.02020289,0.0293996,-0.0058555,0.04726362,0.02517242,-0.03725619,-0.01050321,0.07297584,0.04792698,0.00227808,0.02065138,0.01271548,0.00307375,0.01393178,0.06422354,-0.00678575,-0.01283103,-0.01204034,0.01078527,-0.06693253,0.06386959,0.10967921,0.00004768,-0.01310395,0.00529509,0.07955238,-0.05320695,-0.01628091,-0.04018695,-0.02886657,-0.01378094,-0.09481262,0.02219063,-0.03062635,-0.03575152,0.10112697,-0.02091084,-0.0731921,-0.01042379,-0.01045921,-0.00637145,-0.03243262,-0.01782131,0.00725552,0.03725619,-0.00482654,-0.21272309,0.02944854,0.05795257,-0.09206331,-0.00159814,0.05934176,0.0813416,-0.02003236,-0.1016252,0.03009351,0.04829038,0.02202519,0.01781714,-0.01518335,0.06859434,-0.01034381,0.06623588,-0.05481968,0.04881663,-0.09716548,0.02106945,0.05079726,0.1924476,-0.02028313,-0.01427473,0.00680168,-0.03732977,0.04218617,0.03869373,-0.01983585,-0.00569145,0.00118971,0.08283858,0.00057619,0.00214695,0.07412001,-0.00382464,-0.01312634,0.03011669,-0.00031072,-0.05645211,-0.0215207,-0.03915265,0.02970982,0.05596452,-0.05650045,-0.02687425,-0.02452027,-0.02641055,-0.06471951,0.02072678,-0.01299299,-0.03475833,-0.03707509,0.05826476,0.05258792,0.04463436,0.01541178,-0.1022224,0.01456848,0.0111413,0.02390163,0.03688805,0.0011057,-0.01615663]}},"text":null,"length":0,"last_read":{"hash":"e8e20ff632a8bf02fdbc368858bee4ec3257e356875ffb07c10421809aa0b678","at":1736406354913},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Gitlab other#{3}","lines":[71,71],"size":170,"outlinks":[{"title":"https://forum.gitlab.com/t/is-it-possible-to-commit-artifacts-into-the-git/22218/7","target":"https://forum.gitlab.com/t/is-it-possible-to-commit-artifacts-into-the-git/22218/7","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Gitlab other#{4}": {"path":null,"last_embed":{"hash":"abc798a442b2b5f793987dc4a7bc9f41695b55565ef549af9c7217751e416a5f","tokens":83},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02740604,-0.04238417,0.00986926,0.01042664,-0.0270429,-0.02206016,-0.06105488,0.01991478,-0.01004985,-0.03483191,0.01900918,-0.07881368,0.0297539,0.02286316,0.06095295,0.01230682,-0.01693736,0.05578113,-0.00582588,-0.0130029,0.11620663,-0.03068403,0.02509157,-0.02299199,0.04246242,0.06236296,-0.00086187,-0.03608763,-0.01576779,-0.20408595,-0.06501678,0.00399106,-0.07927518,0.0548077,-0.02635128,0.01432559,-0.04914807,0.02335722,-0.04436929,0.05303765,0.07014866,0.04459615,-0.01762178,-0.00511591,-0.05069466,-0.05268071,-0.03543976,0.0214566,-0.05708665,-0.01198295,-0.00770206,-0.03713327,0.00997284,0.05334091,-0.02629296,0.03235615,0.04754112,0.0858218,0.045205,0.00453842,0.10771948,0.02453324,-0.19199122,0.0925774,0.02687897,0.06302981,-0.03626152,-0.04462732,0.02532958,0.03169535,0.0092416,0.03097807,-0.02401285,0.06893673,0.00931772,0.04231891,0.00667884,0.00209381,-0.0130929,0.00123035,0.00727115,0.02378401,-0.0158158,0.03166947,-0.01108298,-0.0434632,-0.01357935,0.00348025,0.05923326,-0.03900446,-0.07640038,-0.09678272,0.08946735,0.01241118,-0.0429777,0.04411948,0.04299089,-0.02955316,-0.01006359,0.12688881,-0.02992801,0.00274687,0.05470709,0.02987142,0.04138989,0.00337676,-0.01346875,-0.0725895,0.04195025,-0.01730439,-0.00502915,-0.01495455,0.06214254,-0.04576084,0.02276483,0.02023066,-0.00483104,-0.02707911,0.02343528,0.01349996,0.04281355,-0.01779852,0.07195509,-0.02930011,-0.00292377,0.00203202,0.0383042,-0.00495453,-0.00070258,0.06655066,0.04110261,0.0381087,-0.0529047,-0.01676688,0.01467814,-0.00875937,-0.00700304,-0.07701107,-0.01992624,-0.00355293,-0.04248141,-0.00618074,0.01117218,-0.02590422,-0.02752124,0.14747983,-0.04229707,0.03884563,-0.05697738,-0.0421607,0.00478361,0.04034656,-0.02861639,0.02235397,0.00936113,0.06240713,-0.0004423,0.04860942,-0.06382994,0.02942473,0.02267755,-0.0269343,-0.07176273,0.1215312,0.00846245,-0.05397732,-0.05494509,-0.02879532,0.01101254,0.04739358,0.00094271,0.02116751,-0.001453,-0.0013727,0.0397901,0.00305332,-0.00186772,0.00994014,0.02383899,0.08164614,0.05250993,-0.04997211,-0.01930781,0.03180218,0.00625206,-0.0463529,0.01670803,-0.00468393,0.02693091,0.03929309,-0.08116575,0.05651689,0.04658514,-0.03991622,0.05162957,0.00978538,0.00988733,-0.00018693,-0.00381756,-0.04088984,0.07676799,0.13556242,0.01714341,0.01788579,-0.10221949,-0.01262524,-0.0190629,-0.05686132,0.02544695,0.00493347,-0.04827347,-0.03930676,0.05748478,0.05189934,-0.04774049,-0.00137265,-0.0260684,0.02351198,-0.01135445,0.07238325,-0.00333462,0.02153675,-0.03346099,-0.18867558,-0.02857969,0.02933081,-0.0935483,0.02743996,-0.04467108,0.00526825,-0.02236338,0.01263993,0.07518031,0.1063308,-0.03475205,-0.0390642,-0.00836398,-0.03027583,0.02096087,-0.03848541,0.00495825,0.02368143,0.0448215,0.00402242,-0.01982775,-0.02938622,-0.07197236,0.04336894,-0.02931574,0.16211517,0.02405809,0.04751392,-0.00018536,0.01098185,-0.00556275,-0.00598911,-0.18116243,0.01750042,0.05745374,-0.0480348,0.01938297,0.01126521,-0.02013672,0.01964539,0.03365887,-0.02541669,-0.12614478,-0.02716516,-0.0707519,-0.011907,-0.03587805,0.01424397,0.0153879,-0.02816809,0.04111149,-0.06000929,0.03130117,-0.00172481,-0.04858902,-0.06425265,0.05885807,0.03338927,-0.02362903,0.0602535,-0.04132603,-0.04263812,-0.01019649,0.07572597,-0.03429149,-0.04654984,-0.00859136,-0.00306462,-0.03563947,0.0304563,0.12190288,-0.00448858,-0.02926419,-0.04148402,0.05473313,-0.02410624,0.0020786,-0.03227217,-0.01611557,-0.02281967,-0.05006777,0.02350549,-0.0233366,-0.03594075,0.0574695,-0.01076317,-0.02357003,0.02934454,-0.02121435,-0.02524048,0.02074758,-0.04445319,-0.02238517,0.0987,-0.00036145,-0.21507019,0.02397734,0.07382575,-0.07451652,-0.00596187,0.07559142,0.05779235,-0.0367648,-0.09743831,-0.00338985,0.00451159,0.00881813,0.0257359,0.01959365,0.01796559,0.00688601,0.07280272,-0.05857962,0.04299532,-0.0856379,0.00387573,0.05421255,0.22053695,-0.00042124,-0.00575711,0.00479642,-0.01039136,0.01165867,0.04642115,0.00952465,-0.02958691,0.00360796,0.09353184,-0.01927345,-0.01672838,0.08157839,-0.02504306,-0.007337,0.01532823,0.05009111,-0.04112469,-0.00873276,-0.03769291,0.00077199,0.07156982,0.00029429,-0.07408864,-0.03015101,-0.02901504,-0.09177049,-0.00424324,-0.05763176,-0.0631483,-0.04514883,0.10421599,0.0520312,0.03156329,-0.004746,-0.10573869,0.02030385,-0.00560787,-0.0090783,0.03742218,0.01650389,0.01109609]}},"text":null,"length":0,"last_read":{"hash":"abc798a442b2b5f793987dc4a7bc9f41695b55565ef549af9c7217751e416a5f","at":1736406354921},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Gitlab other#{4}","lines":[72,72],"size":148,"outlinks":[{"title":"https://threedots.tech/post/automatic-semantic-versioning-in-gitlab-ci/","target":"https://threedots.tech/post/automatic-semantic-versioning-in-gitlab-ci/","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Gitlab other#{5}": {"path":null,"last_embed":{"hash":"3ba34083c857e5f7884301142b2575641253baa91164fb8349df06e657bcd844","tokens":95},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06291887,-0.03873088,-0.00513776,0.01360484,-0.03713978,-0.0299948,-0.06993986,0.05161104,-0.01120439,-0.07033648,-0.00449289,-0.07048752,0.01849222,0.01705901,0.07012364,-0.00994671,-0.02856601,0.05777004,-0.0388009,-0.03298963,0.08027173,-0.03977784,-0.02499912,-0.05670647,0.02162375,0.06576185,0.0088272,-0.04802781,0.01120959,-0.20956756,-0.02462523,0.02485268,-0.06486258,0.01126206,-0.0208932,0.0089391,-0.05805634,0.02369355,-0.04919913,0.07122682,0.09803436,0.04345078,-0.01575315,-0.01606133,0.00340274,-0.03668243,-0.02445918,-0.00050041,-0.07877354,0.00341633,0.00700307,-0.00836801,-0.0236343,0.03603479,-0.04493085,0.01167466,0.03842119,0.0618754,0.025636,0.00657413,0.0814795,0.04308141,-0.17760161,0.08372907,0.01478161,0.03519949,-0.03110826,-0.05950571,0.04775818,0.05037453,0.03183558,0.03368217,0.01468437,0.06687348,0.01645292,-0.00832924,-0.01456671,0.00430347,0.01939631,0.00913295,-0.03503576,0.019529,-0.05120908,0.0436784,-0.03301813,0.01421146,0.01961964,-0.01447292,0.05199092,-0.00523026,-0.01542985,-0.10547195,0.07500349,0.01541564,-0.03041776,0.06581406,0.01484118,-0.0522312,-0.01468799,0.1240406,-0.03192662,0.00282377,0.02890487,0.02464988,0.05078581,-0.00090604,-0.03404012,-0.11001687,0.04983887,-0.01098775,0.03564063,-0.05240332,0.0533989,-0.03911903,0.02853003,-0.00710764,-0.02014706,0.00218257,0.01512511,0.03138678,0.04526476,-0.03491171,0.07197545,-0.0168038,-0.0044547,-0.01370277,0.02641628,-0.00153162,-0.03089483,0.00862995,0.05647778,-0.02060094,-0.00666428,-0.04224739,-0.00623305,-0.0196397,0.02446161,-0.0334747,-0.01087626,0.00304122,-0.02543367,0.00335607,-0.00082504,-0.0731946,-0.03729495,0.10007999,-0.03835095,0.01461918,-0.02450671,-0.021229,-0.01513281,0.01229999,-0.00799352,-0.00362541,0.03637641,0.06344254,0.02216106,0.08312408,-0.07258044,0.01072528,0.01217081,-0.0178246,-0.03116206,0.14956652,-0.03444434,-0.07095541,-0.07354027,-0.02128859,0.03041882,0.00595006,-0.01718281,0.03850331,-0.00673231,-0.02336092,0.0436659,-0.00217069,-0.03051371,-0.00511265,0.01577635,0.10636906,0.07361443,-0.02399737,-0.01137647,0.01405803,-0.01416095,-0.0711647,0.02997088,0.03045793,-0.01689895,-0.00778849,-0.05218536,0.04488562,0.03315911,-0.02845403,-0.0096148,-0.03178518,0.01448231,-0.03513595,-0.03513112,-0.02268702,0.07176413,0.12187369,0.02856594,0.04756004,-0.09164696,-0.01072636,-0.03079731,-0.07099707,0.02951199,0.01786916,-0.02492125,-0.03002029,0.02085925,0.05595531,-0.07015108,0.02182968,-0.05491142,0.03532314,-0.01286022,0.02288722,0.02400237,0.01663252,-0.04135005,-0.22861339,-0.05681123,-0.00383015,-0.09543669,0.03990287,-0.0287382,0.03361029,-0.02370858,0.00465926,0.07747489,0.09084781,-0.01189834,-0.05304942,-0.02533919,-0.02408496,0.0664885,-0.05734886,-0.01090853,-0.00368725,0.05513297,-0.07872479,-0.02812093,-0.02054252,-0.0846654,0.0577619,-0.02160689,0.19920674,0.04656812,0.06972951,0.01486532,0.01640935,0.01360209,0.02713752,-0.13849425,0.02072764,0.08146942,-0.03322116,0.05359532,0.06307844,-0.02410841,0.00794484,0.03541222,-0.03853694,-0.1178216,-0.00189731,-0.07178194,-0.01298789,-0.02815351,0.01211427,0.02337182,-0.01766656,0.02402124,-0.05005234,0.02728482,-0.01020687,-0.04552331,-0.01453965,0.05145701,0.03645843,0.02489224,0.04079547,-0.01626835,-0.03790821,0.01009632,0.07368585,-0.02466174,-0.02202881,-0.01898894,0.0342901,-0.03912853,0.0433903,0.12291456,-0.01144786,-0.01553049,-0.00546294,0.0256105,0.0096567,-0.00542701,-0.01524817,-0.01813711,0.00651292,-0.04116515,0.03464758,0.02135525,-0.021541,0.07355306,0.04884662,-0.04736864,0.01413191,-0.01948734,-0.04803541,-0.00353158,-0.05545986,0.00868684,0.04992691,0.02993759,-0.23882306,0.02274522,0.05081787,-0.07096867,-0.01433124,0.04742317,0.08361622,-0.03397831,-0.10770789,-0.00460799,-0.00881963,0.02889751,0.0087473,0.01254952,0.02316337,0.00943522,0.09030316,-0.0515916,0.05412119,-0.03378583,0.02705946,0.08020478,0.21709406,0.01851154,0.02496865,0.01995739,-0.03405755,0.00510944,0.06254156,0.02746979,-0.01788702,0.01986257,0.05888049,-0.02043971,-0.02212309,0.09443625,-0.01772169,0.00561676,0.02374558,0.01998934,-0.02829753,0.00391737,-0.02781824,-0.01374525,0.04599881,-0.01589933,-0.08270016,-0.03209252,-0.03349464,-0.03335983,0.0130485,-0.04872747,-0.03992944,-0.02337027,0.08080417,0.03308986,0.05074628,0.02184925,-0.05353905,-0.03805562,0.02533729,-0.04421257,0.02909734,-0.00783112,0.04659352]}},"text":null,"length":0,"last_read":{"hash":"3ba34083c857e5f7884301142b2575641253baa91164fb8349df06e657bcd844","at":1736406354928},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Gitlab other#{5}","lines":[73,73],"size":156,"outlinks":[{"title":"https://quarkus.io/blog/*********************quarkus-with-gitlab/","target":"https://quarkus.io/blog/*********************quarkus-with-gitlab/","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Gitlab other#{6}": {"path":null,"last_embed":{"hash":"44107f006a13cd0c3b2f682946f4f5366a02eef78d6e776e849e0d07cd5f3fcc","tokens":103},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03132811,-0.01062058,0.0150795,-0.00479322,-0.01503097,-0.02859483,-0.0830669,0.02194023,-0.00983262,-0.07062945,0.0051548,-0.06154329,0.03747912,0.04489495,0.04153621,-0.00501976,0.00113885,0.05824807,-0.04088555,-0.05930298,0.10504752,-0.02834002,0.04290134,-0.06419829,0.04266731,0.04284287,-0.00144084,-0.03674304,-0.03665225,-0.18452066,-0.07153162,-0.01227539,-0.06174213,0.00771211,-0.00386274,0.00651115,-0.03401341,0.08294678,-0.09269615,0.07944539,0.03425182,0.04489215,-0.03437189,0.01219566,-0.02640071,-0.03530766,-0.07441579,-0.00421098,-0.01101479,-0.01506971,0.029643,-0.03354181,-0.03538755,0.02373993,-0.03701249,0.04821967,0.03648978,0.0646107,0.0117797,-0.01073264,0.08593503,0.02100863,-0.19268921,0.09324206,0.01770876,0.0658237,-0.00639448,-0.06878827,0.05245493,0.05686787,-0.02684036,0.01739376,0.0034054,0.06436515,0.03633575,0.05836556,-0.00361821,-0.00137988,0.00856474,-0.01713783,-0.05031884,0.0114436,-0.01002614,0.0209855,-0.0162109,-0.04756039,-0.0050264,-0.00617802,0.09536807,-0.0129011,-0.02129808,-0.07203071,0.09723275,0.00648976,-0.11310586,0.04011255,0.03646044,0.04121623,-0.01562562,0.13621275,-0.05879283,0.03596048,0.07752929,0.04824639,0.04856811,0.00455282,-0.01123,-0.06289092,0.05683782,-0.02227045,0.03306907,-0.03361329,0.02627353,-0.01632155,0.04254623,0.0283981,-0.00961167,0.00338241,0.00198217,0.03483895,0.0281257,-0.05799853,0.07134585,-0.02671905,-0.02437334,0.00891737,-0.02118269,-0.02997686,-0.03678076,0.02611225,-0.01555056,0.01513869,-0.01851854,-0.02017996,0.00713728,-0.02627402,0.01869079,-0.06375838,0.01479599,-0.04100585,-0.0580529,0.01078568,0.01864848,-0.02484267,-0.05066789,0.08217231,-0.01177356,0.0694935,-0.0545865,-0.04046851,-0.01122896,0.0311196,-0.00807975,-0.00098264,0.049577,0.0552755,-0.01368719,0.07110719,-0.04469714,0.01228114,0.02678446,-0.01438162,-0.03900887,0.0900363,-0.00222133,-0.05397646,-0.10679694,-0.00154634,0.01334079,0.01537442,0.03806847,0.02307373,0.01030707,-0.03561532,0.07366855,0.00878634,-0.00077182,0.01801649,0.02569725,0.07315823,0.08796823,-0.01431349,-0.05287267,0.0218349,-0.01331119,-0.08514619,0.01430061,0.00936737,0.01194155,0.03624207,-0.1097219,0.06331001,0.02371027,-0.01885381,0.05349649,0.00875941,0.01185675,-0.02076678,0.00719312,-0.04085325,0.1152931,0.12309622,-0.01553425,0.02281115,-0.07761043,-0.02667806,-0.01531726,-0.0429537,0.06230669,0.00190412,-0.02120878,-0.01179371,0.00015813,0.04971695,-0.05405772,0.00225487,-0.02207308,0.02041738,0.00101573,0.03347801,0.05457753,-0.01486939,0.0040926,-0.18972738,0.02530274,0.0110914,-0.09036732,0.0342503,-0.0642305,0.04076108,-0.03406987,-0.0256986,0.03696893,0.07199627,-0.04608417,-0.05252629,0.02348174,-0.02360974,-0.00425618,-0.05766961,-0.03419491,-0.04572733,0.00898933,-0.00108071,-0.05887932,0.00646762,-0.03345285,0.08009613,-0.00683843,0.15160149,0.05696655,0.05031942,-0.04704002,0.02191197,0.01294953,0.01434213,-0.1596262,0.01816286,0.06611702,-0.00741738,-0.00982767,0.02523402,-0.01399399,0.02364213,0.04586055,-0.04100527,-0.14098918,-0.01401311,-0.03632393,-0.05964895,-0.05037629,0.01616388,0.01645107,-0.01783375,0.02885194,-0.02448205,-0.0007499,0.01876703,-0.0534128,0.00525803,0.04698005,0.03023041,0.01908067,0.07916583,0.01580391,-0.03526437,-0.02673531,0.03050391,-0.01867758,-0.06587765,-0.04212905,-0.00232768,-0.04146129,0.04850192,0.07524388,0.00227814,-0.0257351,-0.02117017,0.05221347,-0.02351534,-0.00005317,-0.01939887,-0.03642935,-0.00342852,-0.03685058,0.03565611,-0.01658754,-0.0066542,0.07621264,-0.01003966,-0.03291854,0.0470693,-0.03763238,-0.05052281,-0.01448232,-0.03220137,-0.03104971,0.06515626,-0.01120855,-0.21635897,0.06100627,0.0419588,-0.08805474,-0.00041328,0.05812037,0.04682167,0.02226392,-0.06507386,0.00913622,0.06184142,0.00830511,0.01761959,0.01648243,0.06656341,0.00358116,0.09203022,-0.04586626,0.06563827,-0.09996075,0.02471919,0.026646,0.20992498,0.01585782,0.0069423,0.00456808,-0.06769353,0.01536946,0.04004839,-0.00970433,0.00134466,0.01285699,0.08068456,-0.00340551,-0.02206581,0.12117967,-0.01689959,-0.0145348,-0.00260739,0.00061472,0.00570941,0.00983694,-0.02686021,0.00773262,0.10010844,-0.06952644,-0.05975606,-0.0622698,-0.02758277,-0.05933154,0.07689175,-0.05275609,-0.02768325,-0.01595799,0.02731302,0.02089608,0.02766237,0.01085873,-0.10673609,-0.02886748,0.00548045,0.07009529,0.02926198,-0.03346251,-0.00070046]}},"text":null,"length":0,"last_read":{"hash":"44107f006a13cd0c3b2f682946f4f5366a02eef78d6e776e849e0d07cd5f3fcc","at":1736406354936},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Gitlab other#{6}","lines":[74,75],"size":164,"outlinks":[{"title":"https://blog.jdriven.com/2021/11/reuse-gradle-build-cache-on-gitlab/","target":"https://blog.jdriven.com/2021/11/reuse-gradle-build-cache-on-gitlab/","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Gitlab other#Gitlab tagging": {"path":null,"last_embed":{"hash":"cb7da74b816867cde5e8f42028cb4205d92d97675694090df40a27421a12fc64","tokens":506},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04610536,-0.03079928,0.03718303,0.00376264,-0.02001414,-0.03082177,-0.05831949,0.03086455,0.02767646,-0.05839523,0.00044466,-0.04202924,0.03638696,-0.0029822,0.07289989,-0.01831918,-0.01999835,0.08940215,-0.03422081,-0.02757228,0.09809067,-0.04060864,0.03159391,-0.06985399,-0.00227249,0.02473337,0.0217459,-0.00308063,-0.0121274,-0.21746051,-0.01596605,-0.01668947,-0.0594312,0.07381074,-0.02973996,0.01734416,-0.03446672,0.00927623,-0.05134158,0.03365534,0.06785106,0.02163757,-0.03053664,-0.00916785,-0.02435261,-0.03582738,-0.04623788,-0.00307251,-0.04324935,0.03245223,0.00864178,-0.06739832,-0.03372522,0.05101086,-0.04642899,0.0579104,0.03070611,0.05921416,0.04401485,-0.00259044,0.09137997,0.03569965,-0.21537563,0.097425,0.01396335,0.03325049,-0.04737493,0.01296285,0.03698515,0.06079378,-0.01011787,0.03125915,-0.00366525,0.06724983,0.02509944,0.02573957,-0.04351731,0.0091865,0.00192344,0.00604271,-0.03777434,0.04338041,-0.05836267,0.0250492,0.01290144,-0.03846946,0.04060591,0.00793688,0.06036962,0.02773652,-0.00568746,-0.09051547,0.07147644,-0.02173908,-0.06271992,0.0468021,0.02630494,-0.00851154,-0.08132555,0.11054768,-0.04351123,0.03107926,0.05157589,-0.00808018,0.05796159,0.01493264,-0.04391738,-0.0476116,0.04425222,0.00802538,-0.00668872,-0.00596782,0.01477588,-0.0838126,0.02481236,0.02751563,-0.02145665,-0.00797558,-0.00973106,0.00022752,0.03877616,-0.03130796,0.05874688,-0.03386246,0.00198841,0.0359341,0.04316515,-0.00942541,-0.00911477,0.05464886,0.01030758,0.00736811,-0.05964568,-0.01250076,-0.03900475,-0.0237734,0.01395089,-0.04166913,-0.0649514,-0.03121628,-0.03130831,0.034364,-0.00622596,-0.03997863,-0.04131082,0.08655523,-0.00376957,0.08021569,-0.02760488,-0.07391209,0.01684564,0.03076431,-0.04457524,-0.0024649,-0.00128408,0.02204837,0.02944653,0.08906687,-0.04570063,0.02200896,0.02557095,-0.06217945,-0.05997628,0.09548662,0.02049784,-0.09743925,-0.07187886,-0.02703818,0.04443194,-0.00064045,-0.03157494,0.00346331,0.03019651,0.00201868,0.05638231,0.00686095,0.01571722,0.02528147,0.02959354,0.11176391,0.05817053,-0.04546707,0.01080395,0.03108077,-0.01417975,-0.07845445,-0.00859494,0.013173,-0.0102939,0.00771227,-0.04546172,0.04801861,0.0038831,-0.00620613,0.06067352,-0.01213532,0.00766589,-0.02134355,0.01054234,-0.04458809,0.10398951,0.09936015,0.01959609,0.01968011,-0.09184912,-0.02734229,0.00017492,-0.07964294,-0.01307617,0.01644129,-0.01340256,0.02376234,0.03507268,0.03537376,-0.11205211,0.00672541,-0.06479082,0.07309945,0.04819675,0.03316152,-0.02499398,0.03663323,-0.03096323,-0.2183761,-0.00179088,0.03402133,-0.04193161,-0.00763205,-0.05242317,0.05728297,-0.0250054,-0.00145629,0.03318074,0.13922316,-0.0196252,-0.06675207,0.03925196,-0.01640359,0.03691116,-0.03470299,0.00188435,0.02584102,0.02757232,-0.05340618,-0.02938865,0.006717,-0.0396774,0.04428669,-0.00660622,0.16708203,0.05184934,0.02335855,-0.01144645,0.0365098,-0.01488515,0.03129206,-0.20124225,0.02432448,0.03738567,0.00511908,0.01897665,0.06090915,0.02003221,0.02344043,0.01082029,-0.0013332,-0.0994578,0.02180065,-0.03555881,-0.01885765,-0.01708782,-0.00970553,-0.02738554,0.02754634,0.01160475,0.02205305,0.02579689,0.02546865,-0.06587514,-0.01083149,0.02473375,0.02760166,-0.01579545,0.03583075,-0.01879161,-0.0141079,0.01939416,0.05094079,-0.0519828,-0.02032114,-0.00626287,0.04578405,-0.08168678,0.00102654,0.09399581,0.00072818,0.04138607,0.00895827,0.02113555,-0.03892325,-0.018182,-0.03609011,0.00439517,-0.0252866,-0.00299494,0.0337384,-0.03354879,-0.03570621,0.05435268,0.0099365,-0.03712225,0.03877658,-0.00285025,-0.04145072,0.00270825,-0.04419549,-0.0037311,0.06707992,-0.01761896,-0.24621828,0.05400333,0.04851983,-0.0766869,-0.00901144,0.0463681,0.08252882,-0.03702703,-0.08505045,0.00994529,0.00604424,0.04334624,0.02386842,-0.00562599,0.03028304,0.00928972,0.04075066,-0.04891217,0.0688286,-0.05588464,0.03182772,0.03963168,0.20443289,-0.02954584,0.00563275,-0.00499412,-0.04516131,0.02265878,0.06482152,-0.00924897,-0.03179874,0.01666886,0.09821875,-0.03438492,0.0198566,0.06077473,0.03430427,-0.04923577,-0.00762318,-0.01917037,-0.04403858,0.02099374,-0.00157096,-0.02308241,0.02959546,-0.09860528,-0.05044655,-0.04497462,-0.01013609,-0.05825022,-0.00710916,-0.05808048,-0.02994795,0.00171531,0.09867579,0.06018539,0.03873419,0.01527345,-0.07345529,-0.00581539,-0.01115959,-0.01720893,0.03166401,0.00518421,0.0187402]}},"text":null,"length":0,"last_read":{"hash":"cb7da74b816867cde5e8f42028cb4205d92d97675694090df40a27421a12fc64","at":1736406354943},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Gitlab other#Gitlab tagging","lines":[76,155],"size":2471,"outlinks":[{"title":"https://neron-joseph.medium.com/tag-your-git-repo-using-gitlabs-ci-cd-*********************","target":"https://neron-joseph.medium.com/tag-your-git-repo-using-gitlabs-ci-cd-*********************","line":3},{"title":"https://dev.to/vumdao/config-gitlab-runner-to-push-a-tag-3l5g","target":"https://dev.to/vumdao/config-gitlab-runner-to-push-a-tag-3l5g","line":4}],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Gitlab other#Gitlab tagging#{1}": {"path":null,"last_embed":{"hash":"ed9d450d11526ed6a735cdf28ae843533e67253bd0f8c1dfa37010b1dabbe503","tokens":129},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05988895,-0.02933055,0.04753372,0.00325908,-0.01814894,-0.05577358,-0.03539659,0.04041325,0.02028681,-0.0458163,0.00225922,-0.04828712,0.03102458,0.0047234,0.06738017,0.00627222,0.01591509,0.09071577,-0.0122556,-0.01160455,0.0997095,-0.05606291,0.00053408,-0.0770514,0.00226774,0.02173422,0.01485823,-0.02607926,-0.01813011,-0.22295585,-0.02716628,-0.01034761,-0.05356713,0.06482098,-0.00932335,0.00257495,-0.04463215,0.01832606,-0.03182856,0.03578933,0.05043336,0.04884962,-0.02560784,0.00321529,-0.02749701,-0.03441562,-0.03897788,0.00129771,-0.03184889,0.03661418,-0.00782975,-0.0365553,-0.0324477,0.04517261,-0.05134832,0.04829719,0.02953037,0.04352513,0.06823709,0.01827986,0.09706508,0.03805355,-0.22745165,0.1070592,0.01679538,0.01841621,-0.03112609,-0.00023947,0.03682901,0.0927461,-0.01295647,0.00219487,-0.01239833,0.05759204,-0.00300959,0.01485348,-0.05022607,0.02086416,-0.00370415,0.01107159,-0.03562585,0.01752481,-0.05424212,0.00244139,0.01646945,-0.06409578,0.01866294,0.03767937,0.07611632,0.02054582,-0.00139308,-0.08458235,0.08761045,-0.03212424,-0.08314284,0.01802706,0.03464817,-0.00930981,-0.0247766,0.13838525,-0.05504947,0.00921748,0.08058424,0.02563647,0.0537806,0.02384841,-0.04488701,-0.02533862,0.03469081,-0.02160514,0.00140697,-0.01706473,0.03236425,-0.09005424,0.07240011,0.02349164,-0.00844981,-0.00523345,-0.00303212,0.02534196,0.00709907,-0.04749197,0.05766822,-0.02436629,0.01525693,0.03069198,0.05803951,-0.0154568,-0.01697741,0.03833038,-0.01060302,-0.00267098,-0.05831383,0.01122173,-0.04629795,0.02144544,0.032183,-0.06821952,-0.05118433,-0.05372469,-0.03164784,0.00731637,-0.00557494,-0.05095543,-0.04385947,0.06756054,-0.01144195,0.0702832,-0.03505615,-0.07206623,0.01208781,0.03097032,-0.03379308,0.00363336,0.03359658,0.04168886,0.03381416,0.06405085,-0.03001387,0.04157935,0.01203458,-0.06274199,-0.04245348,0.13182946,0.02058661,-0.06581893,-0.05432019,0.00232269,0.03924452,-0.00805325,-0.03013599,-0.00119299,0.04110724,0.01201397,0.06085613,-0.00840535,-0.00698343,0.0004333,0.02613448,0.10540698,0.08696826,-0.0273697,-0.00732327,0.02009143,0.0091792,-0.06432082,-0.00061854,0.00041274,0.01266424,0.02538753,-0.05810991,0.01532498,0.01110341,0.00953679,0.04554619,-0.0012762,0.00652835,-0.02827488,-0.01991009,-0.03652028,0.10534124,0.0864772,-0.00311999,0.02475174,-0.06863347,-0.02040495,0.02874671,-0.10747148,-0.01598043,0.01466284,-0.05030297,0.02935233,0.04038581,0.00772868,-0.07990631,-0.00075316,-0.06869344,0.05701845,0.00032332,0.04382031,-0.02459549,0.00757791,-0.05266634,-0.24787323,0.00827848,0.05214236,-0.04570691,0.01596522,-0.05260554,0.04437498,-0.02489389,-0.00141454,0.07010742,0.07340393,-0.00560885,-0.05917792,0.00449981,-0.03095329,0.06037149,-0.03261482,-0.01896475,-0.00481555,0.0138302,-0.04758901,-0.02527698,-0.00567892,-0.04397222,0.07502048,0.01606959,0.17339817,0.04770334,0.03513943,0.00618484,0.04779359,-0.00543902,0.02479728,-0.1578147,0.03153278,0.02618814,-0.00763677,0.00595239,0.05335899,-0.00651854,0.00923779,0.03531688,-0.01359593,-0.10804331,0.03045979,-0.03318924,-0.01146076,-0.01327443,-0.02770272,-0.03740486,0.00033094,0.00303871,0.00830542,0.05426998,0.03036857,-0.04277379,-0.04747485,0.01887289,-0.00204751,-0.04869457,0.06203957,-0.0189662,-0.01892396,-0.00579704,0.00723646,-0.01950009,-0.03318134,-0.01292899,0.01607937,-0.07439361,0.01850534,0.06623539,-0.01300812,0.01928093,-0.00353113,0.0351049,-0.0313182,-0.03117352,-0.03055241,-0.00410977,-0.01456088,-0.00566317,0.06065799,-0.00665067,-0.04984977,0.05988044,-0.00180063,-0.03964572,0.05908366,-0.00699619,-0.0410027,0.01816629,-0.07070672,-0.00803613,0.0918775,-0.00913463,-0.22385812,0.06698927,0.0555188,-0.05527379,-0.01072271,0.0679379,0.08854146,-0.03200749,-0.07379442,0.01140763,0.00474418,0.04643993,0.01545847,0.00359944,0.01658783,0.00774222,0.06832011,-0.0300395,0.04404384,-0.01489353,0.0254434,0.03274278,0.21319556,-0.02152799,-0.00256143,-0.0205427,-0.0510758,0.00161621,0.04938275,-0.0258866,-0.04296152,0.02940751,0.08422919,-0.02331332,-0.00847179,0.102459,0.02041843,-0.03742061,-0.01432801,-0.00798385,-0.0606074,0.00703687,-0.02523164,-0.03733097,0.04279693,-0.05371954,-0.07660232,-0.03235026,-0.01401805,-0.05637555,-0.00716586,-0.04022825,-0.01849226,-0.02624315,0.1032097,0.08343597,0.01017614,0.00353757,-0.09032215,0.00465018,0.00674708,-0.01575332,0.03307211,0.02677561,0.00818586]}},"text":null,"length":0,"last_read":{"hash":"ed9d450d11526ed6a735cdf28ae843533e67253bd0f8c1dfa37010b1dabbe503","at":1736406354968},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Gitlab other#Gitlab tagging#{1}","lines":[78,78],"size":188,"outlinks":[{"title":"https://neron-joseph.medium.com/tag-your-git-repo-using-gitlabs-ci-cd-*********************","target":"https://neron-joseph.medium.com/tag-your-git-repo-using-gitlabs-ci-cd-*********************","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Gitlab other#Gitlab tagging#{2}": {"path":null,"last_embed":{"hash":"1effc7791662deb41010e8d3f0251e9c739e61406c40e5cafaaf9bd993ea2855","tokens":103},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03948637,-0.04002145,-0.02708584,-0.01177857,0.01670524,-0.01847693,-0.0761328,0.01259182,0.01002347,-0.04558284,0.01511302,-0.05306219,0.04121441,-0.00039356,0.06904547,0.03359633,-0.0332916,0.11272175,-0.01253715,-0.02470642,0.09662575,-0.01182011,0.02280418,-0.05855369,-0.00564209,0.05436889,0.02435279,-0.03686173,-0.00954669,-0.18667886,-0.01494364,-0.0131707,-0.04389642,0.03869876,-0.03147067,0.01521656,-0.01993433,0.00862761,-0.04374568,0.04834197,0.08186369,0.01106673,-0.05270946,0.01104624,-0.03599563,-0.03358581,-0.04405841,0.00233298,-0.04118287,0.00112705,0.00956122,-0.05545673,-0.03502059,0.02495873,-0.01621263,0.03896644,0.03883805,0.02684558,0.03400125,0.00237166,0.08588776,0.03160134,-0.19095311,0.0870112,-0.00134001,0.01696256,-0.02954291,0.01811418,0.00663569,0.09699708,-0.00593553,0.04220634,0.02717416,0.08237106,0.0268778,0.06028423,-0.02594233,-0.01842834,-0.00327551,0.01844606,-0.02815809,0.00990166,-0.08322287,0.02010241,0.00791009,-0.05719639,0.0243995,-0.01044684,0.0594583,0.00715424,-0.01963861,-0.09333899,0.07135903,-0.00350623,-0.0656999,0.04869829,-0.00112149,-0.02517077,-0.09316736,0.11241657,-0.08799296,0.0359127,0.05780662,0.00186529,0.07410917,0.01358239,0.0044739,-0.05695689,0.0303612,0.00080839,0.00389412,0.01632989,0.03935807,-0.09731348,0.04692126,0.02633458,-0.04593229,-0.00876051,-0.00972484,0.01148587,0.01877166,-0.0676709,0.04773782,-0.07598029,0.0094635,0.01932962,0.04300033,0.01189539,0.02774007,0.02092808,0.02046002,-0.00408502,-0.0911789,0.00044418,-0.03032128,-0.04924955,0.03728864,-0.04102969,-0.04035651,-0.03282409,-0.06652095,0.02810523,0.03124461,-0.04341023,-0.01372794,0.09712191,-0.03415034,0.05958499,-0.04664735,-0.07230192,0.01054099,0.04962466,-0.05456374,0.00155167,-0.02293891,-0.02023348,0.03054154,0.09467657,-0.04075004,0.02473933,0.02095338,-0.04196179,-0.0609008,0.0987091,-0.00088179,-0.11677044,-0.05676185,-0.02405062,0.0367831,-0.01246609,-0.04188611,0.02050826,-0.01300723,0.01161964,0.0657422,0.01581557,0.00497905,0.04361228,0.00677341,0.07406288,0.06077741,-0.01978311,0.01774827,0.0431003,-0.0229027,-0.09376279,-0.01584097,-0.01900881,0.0028193,-0.01615433,-0.05004934,0.03391335,0.02599713,-0.02807222,0.05977963,-0.0068822,0.00939091,-0.02820882,0.00998687,-0.0529772,0.0833739,0.11985019,0.01308548,0.0204921,-0.07925379,-0.03783793,-0.0205732,-0.08601853,-0.02080369,0.04505636,-0.01140612,0.01308452,0.05759114,0.0284751,-0.08216181,-0.00285844,-0.04306671,0.07744266,0.04223813,0.01727992,-0.01213704,0.03018108,-0.06024842,-0.21508804,-0.01382357,0.03630749,-0.06284264,-0.01686684,-0.03147834,0.06117106,-0.0035761,-0.00314742,0.00604608,0.1255357,0.00309621,-0.05130547,0.01693077,0.00741111,0.05178713,-0.00432108,0.01128014,0.01174975,0.05411608,-0.02137409,-0.03064087,-0.05174478,-0.02284388,0.04293811,0.00993884,0.18497407,0.06092089,0.00740468,-0.01069142,0.05327782,-0.01310741,0.0126774,-0.17989501,0.0436608,0.01469165,-0.00075936,0.02550614,0.06343683,0.01876528,0.02149333,0.001829,-0.01752694,-0.11294849,0.00543445,-0.02194621,0.00357049,-0.03829555,0.00263834,-0.03253927,-0.00518101,0.00120757,0.01452259,0.04526034,0.02505637,-0.06294865,-0.0150593,0.00535042,0.01809831,-0.01547978,-0.00401341,0.0148427,0.01363492,0.00284382,0.03313977,-0.02197234,-0.00298147,-0.01042674,0.0225746,-0.0415677,0.02006171,0.070916,-0.00614081,0.00558674,0.02705098,0.02422496,-0.07495464,-0.00568786,-0.04428756,0.03049247,-0.02410669,-0.00485297,6e-8,-0.02168523,0.00377282,0.03182387,-0.00131425,-0.02599677,0.02995288,-0.00894368,-0.02085612,-0.01195225,-0.04573056,-0.00328408,0.08686738,-0.03720896,-0.24790975,0.04237998,0.09057333,-0.05765383,-0.01062204,0.06780558,0.0693637,-0.05574387,-0.09957504,0.00667871,0.01721661,0.06360441,0.01929663,-0.00268744,0.02815212,0.02390041,0.05971767,-0.04537027,0.08340076,-0.06104521,0.00027178,0.02236142,0.21661928,-0.00300203,0.00272901,0.01466409,-0.06643945,0.01638514,0.04446858,-0.00565115,-0.00907862,0.00975176,0.11341254,-0.04064081,0.02137229,0.02948027,0.03091293,0.00175536,0.0065316,0.01951527,0.0078144,0.03635751,-0.00941254,-0.03601525,0.05655161,-0.09214531,-0.06636133,-0.05343387,-0.02429446,-0.04160196,-0.00879549,-0.05826851,-0.03201633,0.00878525,0.08964296,0.05048668,0.04928286,-0.00940255,-0.06816906,-0.00337798,0.02822843,-0.00769194,0.03003035,0.01099838,0.04790785]}},"text":null,"length":0,"last_read":{"hash":"1effc7791662deb41010e8d3f0251e9c739e61406c40e5cafaaf9bd993ea2855","at":1736406354976},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Gitlab other#Gitlab tagging#{2}","lines":[79,80],"size":129,"outlinks":[{"title":"https://dev.to/vumdao/config-gitlab-runner-to-push-a-tag-3l5g","target":"https://dev.to/vumdao/config-gitlab-runner-to-push-a-tag-3l5g","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Gitlab other#Gitlab tagging#{3}": {"path":null,"last_embed":{"hash":"1bc52698e457f087a02e35cea41c886f8038883597b157cb9336aaa51c4260d8","tokens":449},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02647053,-0.04059739,0.03502326,0.00127804,-0.03343661,-0.04634974,-0.0400022,0.04451872,0.02250038,-0.06749668,-0.00826331,-0.03302002,0.03713448,0.00159673,0.06366763,-0.0294737,-0.01823452,0.06147253,-0.05492544,-0.02699931,0.09257981,-0.02768287,0.02599692,-0.06680652,0.0146876,0.03761392,-0.00626751,-0.0050558,-0.0168445,-0.19129294,-0.02207488,-0.03424361,-0.05211135,0.07667182,-0.02642408,-0.00415978,-0.04449598,-0.00379841,-0.03761214,0.02482611,0.07414889,0.03048273,-0.03286028,-0.0120952,-0.02141051,-0.0331311,-0.04940533,-0.02154176,-0.04818965,0.00963975,0.02122624,-0.05710756,-0.01341288,0.06015565,-0.02783727,0.05767998,0.01320856,0.07233657,0.03792707,-0.01094813,0.06159764,0.0483397,-0.19832034,0.11989027,0.00727423,0.04765876,-0.0286638,0.00971818,0.03710087,0.03330819,0.01982991,0.0520275,-0.025937,0.07392288,0.02853274,0.0098535,-0.0231031,0.0051364,0.00182128,0.00288826,-0.0487369,0.04338304,-0.01866999,0.03619681,-0.01877867,-0.04912127,0.04425393,-0.02178388,0.0585696,0.03401227,0.00741605,-0.08603479,0.05106961,-0.01293853,-0.05755303,0.05230951,0.00747391,-0.00946828,-0.10987455,0.11826868,-0.03202882,0.03606935,0.03685462,-0.00311134,0.04720324,0.00116368,-0.05898753,-0.04849898,0.05289906,-0.00142732,0.00581083,0.0051,0.00876989,-0.05822929,0.00145056,0.02734624,-0.00905206,-0.00085541,0.01863014,-0.00794105,0.05057599,-0.01088763,0.05592287,-0.01845211,-0.00565293,0.04367081,0.01179936,0.00590668,-0.02748864,0.05977821,0.03030585,0.00701758,-0.0211283,-0.01172909,-0.03754753,-0.01535352,0.01708178,-0.04114429,-0.03347056,-0.04193699,-0.02766529,0.02985525,-0.00513616,-0.03682227,-0.04902452,0.08972931,-0.00031156,0.08489691,-0.04240654,-0.06387144,-0.00230184,0.02527604,-0.04423947,0.00439724,0.02086888,0.06452941,0.02819822,0.09100748,-0.05569036,0.00961955,0.00446303,-0.03559023,-0.06054348,0.06969419,-0.00637872,-0.09614978,-0.07005976,-0.01754558,0.0045487,-0.00158883,-0.01375145,-0.00002729,0.02250306,-0.01803701,0.04717991,0.00074594,0.02789384,0.03781998,0.03932718,0.09199688,0.04377533,-0.02501504,0.00194935,0.03795984,-0.00445348,-0.06792524,-0.01209577,0.031584,0.01679373,-0.00356945,-0.06920685,0.05938081,-0.0029299,-0.02209146,0.05233325,-0.01316649,-0.00123408,-0.01675768,0.00804352,-0.04544116,0.13667487,0.11402681,0.00146309,0.04342034,-0.09181275,-0.01621622,0.01181499,-0.05255144,0.01540856,0.01136389,0.00552667,0.01764709,0.00432175,0.05760659,-0.10264639,0.00270806,-0.04672899,0.04610894,0.04918613,0.03692849,-0.02157542,0.04252498,-0.01530741,-0.22438738,0.01136332,0.03049501,-0.04370001,-0.02880437,-0.083632,0.0545582,-0.01624627,0.00350103,0.04085036,0.15948048,-0.03445113,-0.08045784,0.03145724,-0.00409752,0.04079422,-0.06989333,-0.00527286,0.01525494,0.036344,-0.05004743,-0.02504505,0.01243533,-0.03429127,0.05866924,-0.01527103,0.14085878,0.06035398,0.007572,-0.03307629,0.01297285,-0.01371845,0.02612608,-0.21279448,0.01852594,0.08782338,-0.0206498,0.02191432,0.0618465,0.01640368,-0.00459963,0.00949764,-0.01250632,-0.092935,0.01138252,-0.03343402,-0.0305058,-0.0453376,-0.00903569,-0.02045883,0.03664874,0.01473281,0.03290308,0.01074591,0.01631496,-0.07512359,0.0004098,0.02527609,0.03958419,0.02402346,0.05760016,-0.03903003,-0.03357035,0.01599368,0.07632741,-0.07916079,-0.03732649,-0.02977021,0.01780265,-0.0828415,0.0043906,0.10110358,0.01227688,0.02708268,-0.00875566,0.04242554,-0.0131899,-0.029553,-0.03040413,-0.00781865,-0.03428598,0.00678961,0.03008087,-0.01567336,-0.03805007,0.07623989,-0.00067935,-0.02664223,0.04325522,-0.0155268,-0.04992674,0.00960174,-0.03450738,-0.01007525,0.05732134,-0.02495857,-0.249865,0.05097291,0.03059296,-0.09989253,-0.00187033,0.03063545,0.08684816,-0.01284444,-0.08843161,0.00960444,0.01340647,0.03598705,0.02031027,-0.02063222,0.04420509,0.00744045,0.04599208,-0.04987474,0.0389764,-0.05885717,0.05688333,0.04363213,0.19891044,-0.03143822,0.01875323,-0.00903635,-0.02562139,0.03643068,0.07312695,-0.00650416,-0.00909669,0.00952569,0.09371985,-0.02665814,0.0232043,0.05841497,0.03377476,-0.07014155,-0.00764945,-0.00385571,-0.03356782,0.03078495,0.00946189,-0.01148752,0.03258418,-0.08423143,-0.05082988,-0.05018146,0.00679658,-0.05730309,0.00949033,-0.06416277,-0.05183614,0.01759598,0.08288667,0.04432214,0.04274564,0.02371455,-0.08004115,-0.00475223,-0.01910269,0.00896086,0.00973438,-0.02252767,0.0116117]}},"text":null,"length":0,"last_read":{"hash":"1bc52698e457f087a02e35cea41c886f8038883597b157cb9336aaa51c4260d8","at":1736406354983},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Gitlab other#Gitlab tagging#{3}","lines":[81,155],"size":2132,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Semantic release": {"path":null,"last_embed":{"hash":"7bea58ec458948222b32d9d3ea7fbc51b33b92b874285bcc6906a020da78ad36","tokens":110},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02935519,-0.01636449,0.01924424,0.01142477,-0.01050743,-0.03392009,-0.08851978,0.04235914,0.03721593,-0.03674865,-0.00668063,-0.04088524,0.01901697,0.03742554,0.04650475,0.02816675,0.01515263,0.02159757,-0.04256535,0.01485686,0.10890904,-0.03670912,0.01073529,-0.00822892,0.04422287,0.05699801,0.00205046,-0.04307913,-0.00696095,-0.15387732,-0.04855809,0.00611321,-0.05347039,0.08261236,0.01896193,0.02563652,-0.02472719,0.03240978,-0.07313568,0.05096244,0.04872375,0.03513978,0.00115633,0.00166111,-0.02049638,-0.04049338,-0.03864359,-0.02234981,-0.04486905,-0.00454847,-0.00181747,-0.03246824,-0.01804202,0.0581371,-0.01064315,0.03651187,0.03849278,0.04179955,0.02076633,0.00233409,0.09388523,0.00030317,-0.19282305,0.07664029,0.0025088,0.0407001,-0.0286758,-0.03492853,0.05521702,0.0241333,-0.00068787,0.04030567,-0.00991032,0.06001474,0.01871586,0.00717338,0.02856058,0.00696701,0.00892567,-0.01469801,-0.03169267,0.02130154,-0.00289454,0.01492444,-0.00677425,-0.01036761,0.02531246,-0.00644532,0.08277806,-0.05253353,-0.08696194,-0.06362576,0.08186781,0.01574106,-0.06887408,0.05290706,0.01509655,-0.04051589,-0.04760537,0.13335454,-0.05564239,0.00823856,0.0281539,-0.00123334,0.02436066,0.02673171,-0.01982386,-0.09130624,0.05420584,-0.03667772,0.00591179,-0.00658569,0.05734805,-0.02911977,0.04848928,0.0071474,-0.02004231,-0.00216149,-0.0234203,0.03421244,0.06600246,0.02197754,0.10150424,-0.02504472,-0.01035421,-0.00579479,0.03492611,-0.03356752,-0.0010172,-0.0074229,0.00548775,0.04297244,-0.057431,-0.02965786,-0.00546623,-0.02996744,0.02866744,-0.06081524,0.00155922,-0.03055326,-0.03068926,0.00733382,0.01811377,-0.03935149,-0.0588514,0.14990292,-0.0115958,0.04085031,-0.04731369,-0.01969672,-0.02679127,0.05534377,-0.02642289,-0.01179158,0.03714296,0.04353731,0.02586369,0.04458877,-0.09325653,0.05105349,0.05826245,-0.03893183,-0.04687523,0.10044665,-0.03667154,-0.06270358,-0.101079,-0.06022591,0.02481561,-0.01140616,0.01555524,0.01726,0.02414576,-0.01290255,0.02270503,-0.02735092,0.01016883,-0.05597785,0.02854287,0.08745431,0.06480782,-0.04568246,-0.03503335,0.04065524,0.01368928,-0.06819273,-0.01540784,0.00255409,0.02165148,0.01912374,-0.07041641,0.00536544,0.06955674,-0.01704004,0.01352335,-0.00194032,-0.00048948,0.02757109,-0.06178249,0.00835865,0.05800267,0.15076987,-0.00190172,0.00766944,-0.05315655,0.01328882,-0.00898415,-0.0457434,-0.01638447,0.01144762,-0.04681569,-0.00347504,0.00273735,0.06809616,-0.08060174,0.02175894,0.01299189,0.02400772,-0.01532616,0.03595047,0.02181442,0.00442512,-0.02060241,-0.19310308,0.00828189,0.02065338,-0.10740755,0.04398496,-0.01692972,0.00991802,-0.02988075,0.04572034,0.04965018,0.11188535,-0.01413617,-0.05278406,0.00702847,-0.01483888,-0.00465977,-0.07809984,-0.00506052,-0.01326476,0.02831567,-0.0251281,-0.03429993,-0.06509154,-0.07880753,0.04701556,-0.04673013,0.1494167,0.07197952,0.0364825,-0.00387208,0.0056437,0.0125273,0.0323886,-0.22436705,0.00741039,0.06132682,-0.05934046,0.01866266,0.05225388,-0.02389343,0.02235149,0.03448468,-0.03791518,-0.09092414,-0.03886196,-0.05045198,-0.03965464,-0.02938533,0.00048192,0.02345883,0.01343486,0.05729777,-0.01368686,0.03453798,0.05042294,-0.05711968,-0.04254411,0.06385163,0.03308957,-0.01799057,0.041219,-0.01270149,0.01422016,-0.00432892,0.06007249,-0.0392845,-0.0214722,-0.02184721,-0.00171567,-0.07322054,0.0505745,0.09766424,-0.02093764,-0.0032417,0.00008986,0.01277194,-0.01454435,-0.04577284,-0.06373578,-0.03098623,-0.00652337,-0.05413158,0.00099391,-0.0363193,-0.05230683,0.08657351,0.0267412,-0.05302757,0.02248785,-0.02367248,-0.03012854,0.02637591,0.01088772,-0.02744522,0.08871899,0.00076437,-0.23224801,0.01419126,0.05620725,-0.06806553,-0.00451455,0.05921968,0.08741753,-0.02486153,-0.1099055,0.00820856,-0.00009197,0.01372222,-0.02805063,-0.00536914,-0.00950067,-0.01427176,0.07541952,-0.0832582,0.0592034,-0.08379257,0.03299281,0.04597434,0.21082164,-0.00908372,-0.0236999,0.02597343,-0.00646115,0.03723933,0.0917505,-0.00924897,0.01852445,0.02095933,0.0681555,-0.00200614,-0.01981936,0.08015168,0.01797963,0.00280169,0.03708102,0.00944691,-0.01766213,-0.01438416,0.00393413,0.01191709,0.07613045,-0.07158955,-0.0363102,-0.04126491,0.00150379,-0.05735448,0.01329613,-0.0468949,-0.02794029,-0.0052451,0.10289197,0.05659917,0.05552008,-0.02722236,-0.09313765,0.02344115,0.01537951,-0.01525227,0.02692804,0.003285,-0.00612746]}},"text":null,"length":0,"last_read":{"hash":"7bea58ec458948222b32d9d3ea7fbc51b33b92b874285bcc6906a020da78ad36","at":1736406355005},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Semantic release","lines":[156,161],"size":259,"outlinks":[{"title":"https://github.com/semantic-release/gitlab","target":"https://github.com/semantic-release/gitlab#readme","line":4},{"title":"https://github.com/semantic-release/semantic-release","target":"https://github.com/semantic-release/semantic-release","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Semantic release#{1}": {"path":null,"last_embed":{"hash":"9e032bc60b67e4caf3cee282fb1b612d1946cb549e7b0ba6807b3234ae441f62","tokens":28},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01557853,-0.00613348,0.00422843,-0.02552221,0.0075624,-0.03546181,-0.07020361,0.03980295,0.0420567,-0.03452181,-0.01147862,-0.04402651,0.02252107,0.04079325,0.01238835,0.00290725,-0.00894195,0.01759645,-0.02035509,0.01648245,0.08066276,-0.01817103,-0.00402826,-0.01184551,0.03714173,0.08277991,-0.00012027,-0.02879018,-0.02308562,-0.14882967,-0.03141756,0.02326788,-0.0104063,0.05826054,0.03189871,0.0252995,-0.02889736,0.04083696,-0.07093098,0.04212977,0.03729202,0.03090023,-0.01740663,-0.00317903,-0.02410931,-0.08121664,-0.02074358,-0.02027651,-0.05189828,0.00071234,0.00487509,-0.05996563,-0.02305616,0.05606944,0.0105187,0.06721599,0.02432263,0.04753118,0.01887632,0.00741994,0.0640413,-0.03062388,-0.17457154,0.04522043,-0.00100305,0.0278319,-0.02832866,-0.05172092,0.04313996,0.02071998,-0.01533615,0.05841744,-0.00411964,0.06364437,0.033354,0.04245872,0.02991862,-0.01623309,0.01392229,-0.00819295,-0.01535651,-0.00508516,-0.03118957,0.0253223,-0.00525059,0.00613175,0.02353881,-0.00694608,0.0649879,-0.04119228,-0.10729203,-0.08234479,0.06649107,0.02932294,-0.07252361,0.04864364,0.01288639,-0.05552815,-0.06216714,0.14331417,-0.05072179,0.00746524,0.03434002,-0.02445668,0.01640724,0.02068783,-0.0197028,-0.10759309,0.05795032,-0.03077096,-0.0051465,0.00810682,0.06204639,-0.04259304,0.04558575,-0.03172671,-0.03899275,0.00750394,-0.02878814,0.0288423,0.0497333,0.02558774,0.07934283,-0.01039134,-0.02958844,0.00995579,0.04115564,-0.01065745,0.0020753,-0.01719121,-0.01309557,0.04072699,-0.06476059,-0.02871253,0.00797224,-0.0371318,0.01457514,-0.05023411,0.01794025,-0.01212396,-0.03497893,0.04432841,0.02179189,-0.05149126,-0.04895165,0.16884206,0.01695413,0.06701267,-0.03329581,-0.02056627,-0.04301179,0.04117482,-0.00095777,-0.02332089,0.03000177,0.0376884,0.0303414,0.01689742,-0.0826667,0.04356696,0.06708573,-0.04264043,-0.03471862,0.12326467,-0.02801747,-0.09303602,-0.06955096,-0.05538563,0.01044751,-0.01388492,-0.00262025,-0.01326834,0.04104468,-0.02121574,0.03099765,-0.02880994,0.02320539,-0.03940368,0.02184673,0.06331636,0.07752188,-0.04483077,-0.03802296,0.0305365,0.01826827,-0.07040614,0.00968528,0.00616812,0.01789062,0.01481104,-0.0746233,0.03014294,0.09294124,-0.02549969,0.02192854,-0.01967602,0.01885788,0.03015415,-0.0648933,0.00229339,0.06371906,0.14068155,0.000867,0.00005141,-0.04316268,0.0151999,-0.03309765,-0.00275465,-0.01868923,0.02249107,-0.05527832,-0.00044272,0.03976762,0.07535619,-0.06430817,0.05143806,0.00704479,0.02287656,-0.00914804,0.02228192,-0.01168381,0.03302451,-0.01092134,-0.20416254,-0.00533499,0.0166215,-0.11014982,0.02860145,-0.0141835,0.00717342,-0.04926957,0.03474059,0.03115224,0.06979185,-0.01762849,-0.0331862,0.01196421,0.01654672,-0.01700092,-0.05008207,-0.0193917,0.0046384,0.03138808,-0.01199678,-0.04467098,-0.03910174,-0.1043723,0.01076895,-0.04815,0.16207972,0.05716394,0.01785469,-0.01395713,0.02245603,0.03018901,0.02809953,-0.23583931,-0.00074002,0.05385116,-0.03946563,0.01062899,0.03439372,-0.02341589,0.03185405,0.01877313,-0.02868819,-0.0981522,-0.00816674,-0.06252685,-0.03710879,-0.03394848,0.00676987,0.04085988,0.00127339,0.05062641,-0.011115,0.04439997,0.04896568,-0.03790599,-0.02919207,0.07937568,0.02095963,-0.02809922,0.03482399,-0.02190888,0.03454882,0.00047083,0.06538112,-0.02509407,-0.00680921,-0.01488787,0.00024389,-0.09089658,0.05766921,0.07627056,-0.03869657,-0.02666576,0.03045126,0.01416062,-0.02912361,-0.03835317,-0.06716846,-0.03381677,0.04270185,-0.06774253,-0.02547978,-0.04039404,-0.02523264,0.08725212,0.04910963,-0.04179491,0.01322227,-0.02993995,-0.0199807,0.0000598,0.00718858,-0.01011608,0.10373025,0.0153948,-0.23553912,-0.00908614,0.03615383,-0.04954573,-0.03406687,0.05988945,0.06890602,-0.01200125,-0.10066298,0.01173141,-0.04088495,0.01577747,-0.02739139,0.00937335,-0.01791123,0.00161021,0.08523467,-0.04744348,0.05898502,-0.11738089,0.02271196,0.05494991,0.19963887,-0.02007231,-0.03941658,0.03997133,0.00431883,0.05340329,0.12309394,0.00036823,0.02521662,0.01359864,0.07390134,0.02225668,-0.01576594,0.06612527,0.02737617,-0.01042098,0.03646797,0.01897394,-0.0188878,-0.02545048,0.0128568,-0.00534735,0.02628628,-0.0582565,-0.02226373,-0.02591385,0.00064721,-0.07074858,0.0125766,-0.04677518,-0.0155518,0.01255249,0.11239723,0.06102257,0.03960141,-0.02124686,-0.09087931,0.02172812,0.02784056,-0.00436823,0.04468955,0.00551992,-0.01245919]}},"text":null,"length":0,"last_read":{"hash":"9e032bc60b67e4caf3cee282fb1b612d1946cb549e7b0ba6807b3234ae441f62","at":1736406355013},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Semantic release#{1}","lines":[158,158],"size":24,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Semantic release#{2}": {"path":null,"last_embed":{"hash":"031b04a0e3570a9a357664f29440d7a5511626301efffb73bcdcec7e07a057f2","tokens":103},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02630976,-0.02635388,0.03491797,0.01537577,-0.02159074,-0.02316445,-0.09665194,0.0349406,0.02920484,-0.03492359,-0.00268543,-0.04951762,0.03244708,0.04678522,0.07048249,0.02856281,0.02587923,0.03081292,-0.05367827,0.01495996,0.12689292,-0.04573847,0.00693193,-0.02535875,0.02469777,0.06755923,0.02934127,-0.03483667,0.01114057,-0.16686365,-0.06424447,0.00487314,-0.06411979,0.06806177,0.01653363,0.01881051,-0.0218744,0.02793462,-0.06423341,0.02752689,0.05080768,0.03728194,-0.00405051,-0.00139454,-0.03366244,-0.00635827,-0.04218096,-0.01600574,-0.05704514,-0.01755792,-0.02216387,-0.02888429,-0.01832738,0.05781196,-0.0261364,0.03039289,0.04066429,0.03736992,0.02563595,-0.00390318,0.09898068,0.01384767,-0.19483475,0.07824061,0.01539394,0.04349095,-0.0408629,-0.05347726,0.04833834,0.01042122,0.00083297,0.04297454,0.0000993,0.05890876,0.02308463,0.00868612,0.0189287,0.00260912,0.0204189,0.00249756,-0.00434198,0.01269899,0.00814725,0.02531922,-0.00303285,-0.01598123,0.01953967,0.00056498,0.08497519,-0.03387868,-0.08114903,-0.0642023,0.0859937,0.01061706,-0.06251849,0.0574123,0.01583515,-0.04573858,-0.02784972,0.12894917,-0.04059528,0.00555615,0.03736898,-0.00512543,0.0197734,0.01633414,-0.01913283,-0.07212929,0.0615851,-0.03589984,0.01118141,-0.01686038,0.05970382,-0.03321598,0.04124962,0.01387953,-0.01446902,-0.00159613,-0.01843586,0.02686628,0.07186782,0.00859346,0.09425455,-0.03446589,-0.01486942,-0.00113995,0.03808117,-0.02552322,-0.02076837,0.00875413,0.01427306,0.03024338,-0.04646878,-0.04549675,0.01122936,-0.0255159,0.02655071,-0.05814587,-0.01450137,-0.023979,-0.03825692,-0.00805832,0.0113602,-0.02270149,-0.05149426,0.13909747,-0.02925421,0.02988376,-0.04735423,-0.02266743,-0.02242308,0.05794735,-0.03564379,-0.00252264,0.04029588,0.04190568,0.02573795,0.04286236,-0.09312622,0.04932193,0.0522885,-0.03671213,-0.06671664,0.10265305,-0.04168067,-0.04974207,-0.10267612,-0.05402743,0.0242049,0.01606998,0.01599293,0.01833936,0.01620081,-0.00250895,0.03452364,-0.0070312,-0.0126177,-0.04572134,0.02413002,0.08804948,0.07388306,-0.05073111,-0.03186666,0.02954187,0.01236993,-0.05713097,-0.01182458,-0.00085065,0.01047665,0.01140764,-0.0708336,0.01199824,0.0558618,-0.02346619,0.02936993,0.00295273,0.00798022,0.0180992,-0.04894816,0.00917514,0.0570657,0.15242304,0.00171215,0.01218987,-0.05771882,0.01236677,-0.0077652,-0.0366353,-0.00014993,0.01983031,-0.0462696,-0.00341122,-0.00407826,0.07377977,-0.07074729,0.00430201,0.00110917,0.01853673,-0.01790315,0.04609828,0.03742122,-0.01213204,-0.03629155,-0.18622649,0.02673089,0.01117988,-0.12396652,0.02133192,-0.01416903,0.01257487,-0.03361529,0.04510048,0.06796674,0.12753014,-0.01074416,-0.05524504,0.0111536,-0.04187639,-0.00295371,-0.0526192,-0.00182008,-0.02039712,0.04064042,-0.02263153,-0.03942132,-0.04906966,-0.06459131,0.04567654,-0.03058567,0.15213987,0.05805727,0.0691666,-0.00939205,0.00224657,0.01036629,0.02051216,-0.21121715,0.01795155,0.06010481,-0.05134894,0.01533382,0.03737677,-0.01614538,0.02400297,0.04544156,-0.03424633,-0.09830942,-0.03019712,-0.03216029,-0.02821366,-0.0080143,-0.00366909,0.03171534,-0.00079574,0.04909725,-0.01951558,0.01826906,0.03990442,-0.0499368,-0.05047461,0.06444418,0.03792152,-0.01212787,0.05718176,-0.01620872,-0.00534991,-0.0132928,0.06066284,-0.05557545,-0.0259593,-0.03332191,-0.00619957,-0.06730234,0.03363213,0.08341284,-0.00700106,0.00810874,-0.01999844,0.01560327,0.00098879,-0.04108683,-0.05319287,-0.03902096,-0.02642233,-0.036437,0.00542007,-0.02925813,-0.05615791,0.07215011,0.00462334,-0.03989736,0.02906664,-0.02074525,-0.03725321,0.02876311,-0.00123685,-0.0238498,0.08201712,-0.00121529,-0.2389804,0.04213275,0.0683983,-0.05959335,0.0063611,0.05724161,0.09172142,-0.03939432,-0.10737489,-0.01559399,-0.01066244,0.00134914,-0.02393368,-0.00162568,-0.00346266,-0.01887819,0.08000457,-0.09414866,0.06014961,-0.07827514,0.04404081,0.04438989,0.22054186,0.00015861,-0.02029069,0.01017605,-0.00414669,0.01241204,0.06523912,0.00182482,0.01871947,0.0355843,0.07949811,-0.01908583,-0.03043146,0.08593649,0.022094,0.00407614,0.03577413,-0.00049279,-0.0166746,-0.02007909,-0.00808764,0.01361418,0.08070791,-0.06591595,-0.04067305,-0.05142723,-0.01960797,-0.05707896,0.01561708,-0.06014784,-0.02900469,-0.0200539,0.10397501,0.03740594,0.06975982,-0.02754313,-0.10011657,0.01430657,0.00713275,-0.01096929,0.0188363,0.00154053,0.0053335]}},"text":null,"length":0,"last_read":{"hash":"031b04a0e3570a9a357664f29440d7a5511626301efffb73bcdcec7e07a057f2","at":1736406355018},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Semantic release#{2}","lines":[159,161],"size":213,"outlinks":[{"title":"https://github.com/semantic-release/gitlab","target":"https://github.com/semantic-release/gitlab#readme","line":1},{"title":"https://github.com/semantic-release/semantic-release","target":"https://github.com/semantic-release/semantic-release","line":2}],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Version tools": {"path":null,"last_embed":{"hash":"864384681f2a7eaa616d7e6b99ef32995cdd54e1e8dcc08181ad6e5eb6b5d2a1","tokens":83},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03939173,-0.07109316,0.01209367,-0.01481169,0.0215048,-0.03580448,-0.10534655,0.04517831,-0.00116106,-0.01957926,-0.0047568,-0.04881394,0.02606784,0.02691424,0.02978818,-0.0334991,-0.03130597,0.06773085,-0.02141115,0.02351552,0.0816507,-0.02040762,0.03483598,-0.00394267,0.02156627,0.05852828,0.01996102,0.00495998,-0.00961497,-0.21275869,-0.02852954,0.03162374,-0.05347324,0.05584943,0.02528357,0.02367981,-0.00344237,-0.02024754,-0.07178632,0.04262988,0.02455751,0.03216196,-0.01710992,0.00196982,-0.05486867,-0.03314364,-0.03441408,0.01141128,-0.03652451,-0.05365108,0.01571614,-0.08466914,-0.03682456,0.04617668,0.02759312,0.04896579,0.00460612,0.04949188,0.0424194,0.00455323,0.07987402,-0.00850938,-0.23613241,0.07512785,0.03867354,0.05615022,-0.01137542,-0.0725983,0.01768515,0.05468765,0.00386181,0.04427359,-0.01406336,0.06686347,-0.01936987,0.04324071,-0.00221878,-0.01144615,0.03313242,0.00116036,-0.04092076,-0.00150519,-0.02195265,0.02309855,0.01059498,-0.01140251,-0.00302168,0.01762872,0.07948335,-0.01218503,-0.07293173,-0.07189031,0.0604479,0.00395744,-0.02338647,0.04208209,0.05725346,-0.0050386,-0.0459561,0.14480931,-0.03434889,0.00658794,0.03875057,0.01218241,0.01426852,0.00095834,-0.04259774,-0.0606073,0.06348672,0.01316081,0.00165541,-0.01468134,0.05069393,-0.05783462,0.03371662,-0.01673981,-0.01276235,0.00139039,-0.00325429,0.02087722,0.04059969,-0.02659366,0.09946413,-0.0454151,-0.00423856,0.00622709,0.02292587,-0.04602012,-0.00843728,0.01112566,-0.00736606,0.01962051,-0.05858603,-0.06062332,0.02627536,-0.0136025,-0.00079828,-0.05261374,-0.02174128,-0.01338509,-0.05178631,-0.0303277,-0.0041095,-0.03681741,-0.03693273,0.10866637,-0.04119682,0.08274597,-0.04492919,-0.04316366,-0.01040614,0.02711508,-0.04291757,-0.01173961,0.01215067,0.04630559,0.03597248,0.01129808,-0.09296202,0.03865242,0.07081874,-0.01557464,-0.04867537,0.11370626,0.00666305,-0.070708,-0.06714903,-0.02055172,0.01764178,-0.00217177,-0.01773838,-0.02855983,0.00935129,-0.03527951,0.09223197,0.00264652,0.03388582,-0.0270071,0.04069054,0.03460263,0.06920602,-0.0138646,-0.0115807,0.01848252,0.02621394,-0.06062485,0.02004315,0.01574867,-0.00227246,0.02200081,-0.05767478,0.01283449,0.07631178,0.00772833,0.00655318,-0.01006241,0.00118806,0.02625598,-0.00205294,-0.0388693,0.08965869,0.13064918,0.02438105,-0.00800873,-0.10255634,0.03224353,-0.03211255,-0.05566477,0.03069079,0.00984997,-0.08460893,0.00623806,0.07556796,0.07726531,-0.04406936,0.01572875,0.02203024,0.02787837,-0.0070073,0.06836126,0.00920882,0.00394409,-0.06977887,-0.18553011,-0.0420079,0.01209218,-0.088476,0.08668122,0.00312085,0.03884347,-0.06572251,-0.01283889,0.02239278,0.07794335,0.00116277,0.00061378,0.01738886,-0.02936758,0.00677641,-0.03571957,-0.01713111,0.02260409,0.02477582,-0.00437317,-0.01834669,0.00181924,-0.04580378,0.00788831,-0.0554093,0.17334536,0.02141548,0.05330176,-0.00371335,-0.02550695,-0.01338349,0.0310061,-0.17048515,0.00884858,0.07149351,-0.03258764,-0.0246803,0.0283058,-0.02558645,0.03597329,0.02405404,-0.02460563,-0.10177962,-0.03467624,-0.0839402,-0.03556067,-0.02835611,-0.01736389,0.05132439,-0.02636926,0.05073239,-0.00807659,0.00923752,-0.00571303,-0.04010009,-0.06318453,0.02679341,0.05304389,-0.03600109,0.07565619,-0.04947763,-0.0311396,-0.0453803,0.09600032,-0.02621416,0.00509897,-0.0368181,0.01803567,-0.04802797,0.02892132,0.05727083,-0.03191677,0.02089995,-0.00519186,0.04814121,-0.00902664,-0.01435476,-0.01701337,0.00412771,0.01587961,-0.02222479,0.01664177,-0.03020057,-0.04587904,0.04087005,0.00482754,-0.01755308,0.05242876,-0.02905391,-0.03086588,-0.00732428,-0.02259719,-0.02328965,0.05222342,0.02543143,-0.20135815,0.00922654,0.04394793,-0.04258722,-0.02034008,0.04771456,0.04998756,-0.01136792,-0.08886893,-0.0488194,0.04178684,0.00375377,0.010842,-0.00457571,0.004402,0.02257988,0.08045239,-0.02490377,0.06188944,-0.11409035,0.07261465,0.06042003,0.23012966,-0.01626368,-0.02022231,0.00333398,-0.00385331,0.03336007,0.06387611,-0.03386454,0.01454012,0.03051447,0.0928183,0.02609502,-0.00771844,0.10497004,-0.00319399,-0.0248079,0.04593471,-0.02380248,-0.03914825,-0.03196367,-0.02470061,0.01549781,0.08817267,-0.08929289,-0.04632324,-0.00361625,-0.00116544,-0.04656135,-0.00951169,-0.05857177,0.00688566,0.00319843,0.11090007,0.04454007,0.03335132,-0.00485119,-0.12026828,-0.00399573,-0.00318641,0.02187048,0.0602342,0.00006513,0.00844537]}},"text":null,"length":0,"last_read":{"hash":"864384681f2a7eaa616d7e6b99ef32995cdd54e1e8dcc08181ad6e5eb6b5d2a1","at":1736406355025},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Version tools","lines":[162,166],"size":142,"outlinks":[{"title":"https://github.com/npm/node-semver","target":"https://github.com/npm/node-semver","line":4}],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Version tools#{1}": {"path":null,"last_embed":{"hash":"25397f62613eaab5d245e571d6bc610a9a5dd7dde7e2977b792008e876cfe409","tokens":41},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04081216,-0.06305096,0.0045417,-0.01607014,0.01705767,-0.01599942,-0.10648592,0.04218753,0.0157708,-0.02726986,0.00115514,-0.05705292,0.02952592,0.03610362,0.01705436,-0.04001823,-0.02695361,0.06062165,-0.03704217,0.00392158,0.08720448,-0.02658881,0.02032754,-0.00707556,0.03187611,0.08373275,0.0250607,-0.00591679,-0.0142614,-0.20479991,-0.03839364,0.03578459,-0.04306842,0.03575691,0.02560439,0.01498968,-0.01328986,0.01666918,-0.0693539,0.03160524,0.03053871,0.03645547,-0.0244785,-0.024599,-0.06297754,-0.04093283,-0.02864851,0.02754916,-0.03869845,-0.02858493,0.01058636,-0.07049194,-0.03391985,0.04225139,0.01632063,0.05447862,0.00911195,0.05620914,0.03450721,-0.00238145,0.06983832,-0.01949625,-0.21958837,0.07319196,0.00410704,0.05195071,0.00252184,-0.09316262,0.01990544,0.04353661,-0.01416534,0.03327196,0.00179641,0.07489043,0.00080616,0.05564742,0.00025556,-0.02892905,0.02658153,0.00683022,-0.0242796,-0.01688227,-0.02397144,0.03362913,0.00451659,-0.011889,0.00585482,0.01286423,0.0863243,-0.03643231,-0.08201934,-0.09053065,0.0945036,-0.00444466,-0.03064868,0.03710493,0.0386027,-0.01543309,-0.03708189,0.14545229,-0.04602825,0.00026511,0.04452126,0.00958201,0.02465032,-0.00512699,-0.03828325,-0.05789708,0.05545481,0.00641633,0.00147649,-0.01460662,0.06725317,-0.04307821,0.03361764,-0.03871574,-0.02425016,-0.00145877,-0.00318089,0.01834395,0.03878859,-0.05260447,0.09248646,-0.03599486,-0.02920735,0.00308976,0.02412278,-0.03571292,-0.01561051,0.00293684,-0.00968601,0.00964505,-0.06239654,-0.06126984,0.01605695,0.00226317,-0.00044563,-0.04503911,-0.01621271,0.00515229,-0.06772426,-0.01097094,0.0061847,-0.03582543,-0.05142601,0.12770204,-0.03431674,0.07763359,-0.02898491,-0.03926695,-0.00906764,0.02070729,-0.02904039,0.00617852,0.02413397,0.0408747,0.03608765,-0.00272172,-0.08294673,0.03362834,0.07080284,-0.02471469,-0.07553256,0.12282776,0.00168019,-0.06643162,-0.06120421,-0.03301711,0.00684383,0.02040699,-0.00126252,-0.03176845,0.02285376,-0.00437286,0.09344947,0.010565,0.02856365,-0.0071699,0.03387486,0.05174917,0.09671853,-0.00011662,-0.02575874,0.00851133,0.02406883,-0.06872066,0.02971351,0.02011454,0.01565189,0.04551954,-0.05037465,0.02300029,0.08190668,-0.0014029,0.02950202,0.00209958,0.00599561,0.01144626,-0.02164744,-0.02818604,0.07267787,0.15505207,0.01464364,-0.00757107,-0.07741406,0.03908712,-0.04347143,-0.0469782,0.03318187,0.02016537,-0.08134436,-0.00391355,0.06958132,0.08674154,-0.04344428,0.03878262,0.01704303,0.00960838,-0.00900156,0.05561489,-0.00542343,0.0152725,-0.03961634,-0.19530854,-0.02529315,-0.00220221,-0.08703275,0.06042602,-0.01913347,0.03367149,-0.0738271,-0.00199857,0.0297093,0.06011963,0.01438724,-0.00879041,0.01708674,-0.00940409,-0.00713442,-0.04379307,-0.02198104,0.011833,0.02534453,0.00659332,-0.01737881,-0.00377231,-0.06510285,0.0069023,-0.0521773,0.17408234,0.02240954,0.06234705,-0.01494182,-0.009891,-0.00239295,0.03223778,-0.17675129,0.01424076,0.06600554,-0.01411458,-0.03836293,0.02677927,-0.01694459,0.05204158,0.02477458,-0.03607139,-0.11162445,-0.01984726,-0.08132083,-0.01467643,-0.02453278,-0.01215529,0.05473942,-0.03241894,0.03408061,-0.01011599,0.01645015,0.00952816,-0.03470908,-0.05515483,0.05600324,0.05587761,-0.04302966,0.07480711,-0.04784173,-0.01939201,-0.02462519,0.08629694,-0.01788569,-0.01177566,-0.04180448,0.00105199,-0.05155957,0.03332216,0.0520952,-0.05627594,0.02017614,-0.02703566,0.0570522,-0.00728754,-0.02166669,-0.0151897,-0.01456151,0.01234503,-0.01204267,0.00011881,-0.04060803,-0.03242928,0.04793486,0.02237296,-0.01348397,0.05225508,-0.0552902,-0.0246521,-0.01748535,-0.02669276,-0.02482648,0.06886416,0.04227081,-0.1853268,0.00818674,0.04077789,-0.03887827,-0.03331925,0.05169232,0.05515603,-0.02832082,-0.07859999,-0.05433516,0.01664003,-0.00559993,0.01558879,0.00130142,0.01902444,0.01358635,0.08387635,-0.02884042,0.06434985,-0.10610833,0.05170857,0.04736449,0.21280412,-0.01406844,-0.02560083,-0.01124666,0.01219826,0.01904002,0.07484183,-0.03056191,0.02658353,0.02622827,0.1088879,0.02728286,-0.02219679,0.1087717,-0.00476022,-0.02765255,0.03241869,-0.00103196,-0.03250606,-0.02753134,-0.03426098,0.00730111,0.06225201,-0.06299327,-0.05780626,-0.00353674,-0.01215852,-0.07306996,0.00481027,-0.08126134,0.00250309,0.00734952,0.10166484,0.04759933,0.03643578,0.0004301,-0.11399525,-0.00628075,0.01945922,0.04083099,0.05272841,-0.0093103,0.00987507]}},"text":null,"length":0,"last_read":{"hash":"25397f62613eaab5d245e571d6bc610a9a5dd7dde7e2977b792008e876cfe409","at":1736406355031},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Version tools#{1}","lines":[164,164],"size":48,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Version tools#{2}": {"path":null,"last_embed":{"hash":"c51bd682257bb9b1bfa715193fedfc198ec10a50a9a271720ed5809363a438ff","tokens":63},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02875987,-0.06032056,0.03133987,-0.0212073,-0.00518035,-0.04601466,-0.09153672,0.04899238,-0.01347753,-0.01518416,0.00623507,-0.03510921,0.02788188,0.01594678,0.05128003,-0.01376049,-0.02802322,0.06508193,-0.00179511,0.02261935,0.08135279,-0.0029977,0.03104337,-0.02770704,0.00864516,0.06599125,0.03522771,0.01372119,-0.00806964,-0.2038421,-0.02825632,0.05103227,-0.04612535,0.06880073,0.01126513,0.02922108,-0.00375087,-0.01535145,-0.07249668,0.05236251,0.03559152,0.0374356,-0.03273856,0.00267288,-0.05973353,-0.03988593,-0.02852006,-0.01329883,-0.03555363,-0.04277063,0.02293236,-0.08175164,-0.03019687,0.05759736,0.00992817,0.04622304,0.01788169,0.03652864,0.04855461,0.00757524,0.08918849,-0.01296838,-0.23435548,0.06891157,0.04258352,0.04796213,-0.02013215,-0.06986189,0.03289226,0.0524484,-0.00191968,0.05188682,-0.01542971,0.06714145,-0.00180024,0.04763678,0.0059737,-0.00956612,0.04908483,-0.00220358,-0.03179178,-0.00297447,-0.02207036,0.01989186,0.01089101,-0.01864113,-0.00367449,0.01481387,0.07746733,-0.00512313,-0.08694918,-0.07588685,0.05519636,0.01194887,-0.03585923,0.05985551,0.06356009,0.00013963,-0.03138984,0.14412025,-0.02639522,0.00145995,0.03969546,0.01298545,0.00181673,-0.00148528,-0.05319768,-0.064406,0.06833941,0.00631233,-0.004341,-0.00758161,0.03804992,-0.05182196,0.04725302,-0.02597798,-0.0516204,0.01224905,-0.01600675,0.01687833,0.04103652,-0.00563534,0.09876134,-0.03318386,-0.00302723,-0.00187108,0.01387248,-0.02112486,-0.0049222,0.01037423,-0.01050096,0.03022146,-0.05695763,-0.0353662,0.03631048,-0.03065007,-0.00329897,-0.05837382,-0.02150466,-0.01336704,-0.06242353,-0.0015159,0.0003841,-0.03207098,-0.02266983,0.10546079,-0.04103208,0.07244034,-0.03303773,-0.0358701,-0.0161567,0.04210153,-0.05047093,-0.01338667,0.03260022,0.04582407,0.03748456,-0.00333273,-0.09242867,0.0328355,0.07755025,-0.02263837,-0.05515372,0.1091074,0.00952918,-0.07646935,-0.06401562,-0.0276247,0.00589612,-0.01044423,-0.02553685,-0.02665943,-0.00189302,-0.04444096,0.08187182,0.01650775,0.02943753,-0.01732861,0.02386786,0.04099362,0.07523216,-0.04759885,-0.01294306,0.0199851,0.01568924,-0.04788631,0.02270436,0.02122926,-0.00890374,-0.00659123,-0.06511373,0.03234386,0.07887696,-0.01942437,0.02839596,-0.02710143,0.01775755,0.03824141,-0.0122623,-0.02803966,0.08477063,0.13696626,0.02553251,-0.00106494,-0.10317875,0.02034356,-0.03230532,-0.02548609,0.01171744,0.02071037,-0.08094735,0.00139143,0.09322295,0.05187952,-0.04293688,0.02836057,-0.00479945,0.03168159,-0.00160574,0.06618299,0.01721616,0.00575409,-0.06544373,-0.18577965,-0.0251117,0.01943406,-0.12134693,0.06091512,0.01548769,0.03526078,-0.04701507,0.00136503,0.02238808,0.08864548,-0.01641591,-0.00214955,0.00877215,-0.02789853,0.00985042,-0.02903192,-0.02923069,0.00370943,0.0286059,-0.01706956,-0.03436345,-0.00278084,-0.04086117,-0.00965482,-0.04607832,0.1664841,0.02437937,0.0318017,-0.02259461,-0.00127056,0.00161224,0.01218693,-0.18399327,0.00224308,0.06706219,-0.04229038,-0.0136756,0.0178347,-0.0151606,0.03063933,0.01807007,-0.03638862,-0.10229491,-0.0246641,-0.06488324,-0.03249636,-0.01436761,-0.01251136,0.03265855,0.00634951,0.05555753,-0.01675283,0.00747948,0.00480438,-0.0479294,-0.04875831,0.03782002,0.0409575,-0.03016553,0.07735745,-0.04876993,-0.02677372,-0.02839785,0.0913925,-0.04463775,0.00746672,-0.03292025,0.02410439,-0.06477784,0.01869297,0.06063917,0.00493051,0.02743384,0.00454538,0.04838119,0.00192557,-0.0246438,-0.0191176,-0.01706567,0.01094411,-0.0404275,0.00987396,-0.02073223,-0.04719304,0.04827533,-0.01289699,-0.03985235,0.05114555,-0.01290512,-0.05753638,-0.01161901,-0.02694212,-0.00528567,0.06574462,0.01287128,-0.20942466,0.01634435,0.04986345,-0.04611276,-0.00656051,0.0617651,0.05623585,-0.02158912,-0.09845657,-0.02312221,0.03280036,0.0075576,0.01417192,-0.00292341,0.01024804,0.00645574,0.06620615,-0.03061528,0.06198573,-0.13540639,0.07028417,0.06541039,0.23165825,-0.00829572,-0.01127311,0.01695796,-0.02275034,0.00942924,0.05121632,-0.01977967,0.0079273,0.03031047,0.0868616,0.00148941,-0.00403882,0.088304,0.00825557,-0.02757973,0.04474334,-0.01201709,-0.05419942,-0.01395166,-0.02258822,0.01883104,0.07443371,-0.09415779,-0.02687508,-0.01816453,0.00504294,-0.04531687,-0.00697369,-0.0548137,0.0187352,-0.00091682,0.12259831,0.04786608,0.03487634,-0.00075504,-0.11810056,-0.00602546,-0.00326853,0.00932445,0.05338483,-0.00343581,-0.00711048]}},"text":null,"length":0,"last_read":{"hash":"c51bd682257bb9b1bfa715193fedfc198ec10a50a9a271720ed5809363a438ff","at":1736406355037},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Version tools#{2}","lines":[165,166],"size":75,"outlinks":[{"title":"https://github.com/npm/node-semver","target":"https://github.com/npm/node-semver","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Notes from Zoom call Friday Apr 28, 2023": {"path":null,"last_embed":{"hash":"0b6c2233713face8a62b63b80ac022bad4bb678aacf1d210e6e745b08a36274a","tokens":459},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06153221,-0.03462305,-0.0233926,-0.02677994,0.00073265,0.01159188,-0.12099351,0.04043761,0.01790136,-0.01168357,-0.01001179,-0.01794108,0.01189352,-0.00433179,0.00307498,0.01940304,-0.02692294,0.04533352,-0.03237693,-0.0208056,0.09379844,-0.05769969,0.03226351,-0.06676697,-0.01308661,0.06052937,-0.02641108,-0.03034081,-0.00809519,-0.24370843,-0.02466709,-0.00710257,-0.0501557,0.0558146,-0.04495114,-0.01481628,-0.05115565,0.03918552,-0.04337266,0.02256962,0.0239799,0.04221251,-0.03267099,-0.00984712,-0.00529355,-0.00581411,-0.05572612,-0.0161848,-0.03596452,-0.01097769,0.00854635,-0.07578853,-0.02972328,0.05434475,-0.0206495,0.03604547,0.0274584,-0.00269878,0.03836812,0.00726709,0.09428548,0.02564927,-0.2269083,0.11358425,0.00484423,0.0287555,-0.0328532,-0.02331384,0.0381739,0.05089895,0.00298122,0.02577432,-0.00700415,0.06788935,0.04333673,0.0105807,0.01760984,0.0212865,0.02183354,-0.00323749,-0.02439459,0.01929479,-0.01907989,0.01322505,-0.00107406,-0.01631531,0.0204294,-0.02447686,0.09172609,-0.02212037,-0.02584666,-0.09002776,0.04242341,-0.00293722,-0.04428424,0.0211903,0.04042589,-0.01540452,-0.06416471,0.10326625,-0.03643472,0.03027766,0.06871119,0.00034859,0.04812083,-0.0480013,-0.06752581,-0.04988767,0.01368873,-0.02713309,0.00118386,0.00209932,0.04008118,-0.05459631,0.01069729,0.04457681,-0.01075122,0.01468766,-0.00076855,-0.01914632,0.0022952,-0.01311082,0.05026587,-0.01968463,-0.00522845,0.04503745,0.04542971,-0.02045852,-0.00967104,0.00669036,0.03188227,0.00980494,-0.07822467,0.01482557,0.01189236,-0.01047832,0.05979229,-0.07825413,0.00664016,-0.00834871,-0.05633824,0.01545511,-0.00745143,-0.05381249,-0.04019748,0.11862217,0.01357364,0.009737,-0.08869934,-0.11280673,-0.00226851,0.04253463,-0.04038035,0.0125523,0.07109523,0.05011813,0.0341068,0.11379199,-0.07920574,0.03172373,0.02025972,-0.00326657,-0.05070962,0.1000471,-0.02074423,-0.08868361,-0.0349827,-0.01109466,0.01568901,0.03479936,-0.00419462,0.01048292,0.02609522,-0.01271064,0.04915156,0.01178213,0.01278651,0.01262709,-0.0031476,0.08907796,0.06054577,-0.03070447,-0.03521312,0.01037486,0.01397179,-0.08664548,-0.00019415,-0.01505946,0.02131803,-0.02486229,-0.06455061,0.03456927,0.09001908,0.05074185,0.0594602,-0.04858171,0.02759967,-0.02429794,0.02820635,-0.04881322,0.0875294,0.1254905,0.00736064,0.04546579,-0.02976077,-0.02115407,0.00996062,-0.02970304,0.03555756,0.03786601,-0.0651622,-0.00577615,0.01998122,0.08718077,-0.04934107,-0.01074932,-0.03553895,0.04922596,0.01326935,0.04352945,-0.00985396,0.00655107,-0.05002246,-0.19443987,0.00396221,0.00001184,-0.0492824,-0.04097175,-0.05921941,0.06314589,-0.00974523,0.01359237,0.03702058,0.10859661,-0.02885398,-0.03776884,0.01242907,-0.01787606,0.0163827,-0.03596878,0.04697547,-0.01848273,-0.00394554,-0.02984059,0.02371155,-0.03738032,-0.02639852,0.09276621,-0.01792836,0.16921477,0.06461555,-0.00640204,0.02388285,0.01525896,0.01964905,-0.02583564,-0.11782482,0.00075566,0.02705961,-0.03497363,0.02654208,-0.00309168,0.00748159,0.00623628,0.00828371,0.00606894,-0.14266756,-0.05204491,-0.01632274,-0.0259505,-0.00430912,-0.05827904,0.01316001,0.02050026,-0.01907356,-0.01960886,-0.0023566,0.01986994,-0.03652079,-0.04486612,0.01831471,0.02500883,-0.02964011,0.07843897,0.00076504,-0.00731858,-0.01474854,0.05531676,-0.03876692,-0.04462769,-0.03806553,-0.00939617,-0.06072895,0.0466425,0.12396137,0.03363537,0.01709378,0.02022802,0.05719478,-0.00464577,-0.01752104,-0.0122055,0.01835141,-0.04297174,-0.04040502,0.03855951,0.03387065,0.00823561,0.09472013,-0.03278121,0.01625368,-0.00632855,0.01863207,-0.04773392,0.04724636,-0.06485946,0.01754105,0.02332708,0.00931141,-0.24853703,0.03646257,0.02760106,-0.00476168,0.0004898,0.06309259,0.08251616,-0.01815008,-0.07867575,-0.02694803,-0.04167661,0.03079065,0.00419432,-0.01056326,-0.01372531,0.0448385,0.03221236,-0.07993601,0.03177609,-0.06978472,0.04928821,0.02335553,0.22976112,-0.01248171,-0.04214077,0.00429752,-0.02567008,0.02025359,0.00532431,0.02086596,0.00982601,0.01385053,0.09584125,-0.00898821,-0.00929399,0.08058168,-0.01218356,-0.03028197,-0.00079434,-0.02027596,0.0068886,0.00758526,0.02338782,0.01149733,0.05874396,-0.06154901,-0.0235065,0.00956884,-0.01513838,-0.04356177,-0.00460545,-0.04280496,-0.02637356,-0.0186305,0.07456333,0.08589794,0.04268796,0.00432994,-0.13442841,0.02035905,-0.00405797,-0.02285741,0.01815818,0.02458354,0.03325454]}},"text":null,"length":0,"last_read":{"hash":"0b6c2233713face8a62b63b80ac022bad4bb678aacf1d210e6e745b08a36274a","at":1736406355043},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Notes from Zoom call Friday Apr 28, 2023","lines":[167,237],"size":2128,"outlinks":[{"title":"Screenshot 2023-04-28 at 10.36.02 AM.png","target":"Versioning-workflow/Screenshot2023-04-28at10.36.02AM.png","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notion/au21-project/Devops/Versioning-workflow.md#Notes#Notes from Zoom call Friday Apr 28, 2023#{1}": {"path":null,"last_embed":{"hash":"83a48a7801e9271eca1df81434b150eaa0ae758a5ddb3eca54e468d42d03d609","tokens":460},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06080952,-0.03480298,-0.02514609,-0.02776927,-0.00188866,0.01162556,-0.11711371,0.04108519,0.01939082,-0.01162756,-0.01161011,-0.01824397,0.01357518,-0.00371977,0.00635017,0.02308469,-0.02721812,0.04464537,-0.03326195,-0.0215241,0.09624962,-0.05807131,0.03098787,-0.06700943,-0.01191782,0.05754296,-0.02750701,-0.03051763,-0.00992069,-0.24318364,-0.02416833,-0.00523763,-0.04816215,0.05206338,-0.04343465,-0.01623671,-0.05018238,0.03936762,-0.04257406,0.02069196,0.02175815,0.0425637,-0.03184947,-0.00953999,-0.0048902,-0.00639718,-0.05503318,-0.01780985,-0.03644573,-0.01044001,0.00813433,-0.07374936,-0.02907807,0.05483562,-0.02052206,0.03523626,0.02702392,-0.00410955,0.03979138,0.00601838,0.09415566,0.02743574,-0.22759897,0.11600567,0.00319205,0.02901571,-0.03485933,-0.02067442,0.03888818,0.04861663,0.00237993,0.02503682,-0.00721535,0.06829201,0.04677011,0.01002422,0.0175771,0.02277995,0.02233669,-0.0036987,-0.02288902,0.01741733,-0.01966334,0.0138115,-0.00069615,-0.01619858,0.02098733,-0.02627512,0.094974,-0.02110955,-0.02853094,-0.0892299,0.04017795,-0.0051763,-0.04587857,0.02035535,0.0417416,-0.0158556,-0.06341236,0.1018526,-0.03770925,0.03062531,0.0697271,0.00054843,0.04962621,-0.04954731,-0.06719861,-0.05106343,0.01162859,-0.02381944,0.00162396,0.00012274,0.03704702,-0.05673642,0.00919891,0.04550661,-0.00821844,0.01177238,-0.00048923,-0.02055243,-0.00117937,-0.01245129,0.04912974,-0.01994999,-0.00535983,0.04651231,0.04581257,-0.01885608,-0.00779329,0.00441518,0.03113191,0.00927517,-0.07745287,0.01798667,0.01231826,-0.01129229,0.06233615,-0.07828666,0.00515846,-0.010662,-0.05608005,0.01406276,-0.0084185,-0.05527244,-0.03950316,0.11828401,0.01286124,0.00769846,-0.08940572,-0.11475237,-0.00356245,0.04173415,-0.04026085,0.01383409,0.07044344,0.05031054,0.03514599,0.11507796,-0.07859625,0.03248023,0.01857325,-0.00315745,-0.04864931,0.09941871,-0.02273809,-0.08830532,-0.03561271,-0.00725837,0.01486689,0.0367773,-0.0036065,0.0127397,0.02629591,-0.01367967,0.04979802,0.01271261,0.00993848,0.01422473,-0.00407933,0.09031815,0.05975452,-0.03176241,-0.03295983,0.00764793,0.01299842,-0.08846384,0.00178727,-0.01720835,0.02124223,-0.02704558,-0.06467251,0.03595124,0.09297375,0.05225458,0.056691,-0.04755701,0.02901294,-0.0251905,0.03133088,-0.04918592,0.08864079,0.1227131,0.00753541,0.04770684,-0.02825797,-0.02156987,0.01186138,-0.02868261,0.03969339,0.0374884,-0.06408004,-0.00552022,0.01791325,0.08513718,-0.04711188,-0.01100078,-0.0360416,0.04984105,0.01387688,0.04355805,-0.01044132,0.00713663,-0.05177362,-0.19345385,0.00501637,-0.00103821,-0.04826343,-0.04246299,-0.05996684,0.06424841,-0.00728354,0.01356592,0.036366,0.11136146,-0.02756453,-0.03862287,0.01254474,-0.01921071,0.01590781,-0.03720322,0.04911514,-0.02265197,-0.0042754,-0.0315637,0.02602736,-0.03795315,-0.02577118,0.09450006,-0.01636531,0.16683275,0.06693091,-0.0059933,0.0198023,0.01590232,0.02125161,-0.02469367,-0.11686943,0.00113581,0.02484517,-0.03440628,0.02737914,-0.0041958,0.00847006,0.00463397,0.00870338,0.0046081,-0.14155158,-0.05292154,-0.01521235,-0.02534977,-0.00147479,-0.05784125,0.01349492,0.01990062,-0.02245345,-0.01829725,-0.00465429,0.01993867,-0.03589034,-0.04504991,0.01725471,0.02300887,-0.02790375,0.07751322,0.0032292,-0.008139,-0.01537705,0.05451756,-0.0386686,-0.045254,-0.03596276,-0.0092574,-0.05932575,0.04749606,0.12640233,0.03522734,0.02019706,0.02002499,0.05667841,-0.00594793,-0.01668039,-0.0131517,0.01874207,-0.04274895,-0.03702424,0.04364326,0.03666997,0.00802766,0.09341262,-0.03251548,0.02022023,-0.00528755,0.01926125,-0.04730805,0.04819965,-0.06670405,0.01666344,0.02254074,0.00946266,-0.25018227,0.03703716,0.02460819,-0.00467002,0.00127485,0.06243349,0.08425738,-0.02062551,-0.07655767,-0.02797336,-0.04297905,0.03027875,0.0047199,-0.01041218,-0.01395647,0.04554476,0.03250983,-0.08053736,0.02983739,-0.06777978,0.04800831,0.02294288,0.22931062,-0.01494762,-0.03978814,0.00179833,-0.02409781,0.01715349,0.00420153,0.02088216,0.01150116,0.01295061,0.09540612,-0.00993936,-0.00879493,0.0767236,-0.01202292,-0.02747243,-0.00239561,-0.02227439,0.00877161,0.00496569,0.02413215,0.01048214,0.05966389,-0.06243922,-0.01904087,0.00901552,-0.01607037,-0.04177603,-0.00450012,-0.04244379,-0.02493103,-0.0198821,0.07191256,0.08349263,0.0442768,0.0041883,-0.13343532,0.01739605,-0.00411108,-0.0239069,0.01682688,0.02031526,0.03437113]}},"text":null,"length":0,"last_read":{"hash":"83a48a7801e9271eca1df81434b150eaa0ae758a5ddb3eca54e468d42d03d609","at":1736406355065},"key":"notion/au21-project/Devops/Versioning-workflow.md#Notes#Notes from Zoom call Friday Apr 28, 2023#{1}","lines":[169,237],"size":2083,"outlinks":[{"title":"Screenshot 2023-04-28 at 10.36.02 AM.png","target":"Versioning-workflow/Screenshot2023-04-28at10.36.02AM.png","line":3}],"class_name":"SmartBlock"},
