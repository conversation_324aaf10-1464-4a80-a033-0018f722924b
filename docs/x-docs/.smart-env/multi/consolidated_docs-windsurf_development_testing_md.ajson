
"smart_sources:consolidated/docs-windsurf/development/testing.md": {"path":"consolidated/docs-windsurf/development/testing.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"5a4aee11aa0b2a2940edbef7b839e7da93b91f51e3ae1c260829f3a8a7644736","at":1736310457261},"class_name":"SmartSource","outlinks":[],"last_import":{"mtime":1736310130421,"size":4834,"at":1736310457261,"hash":"5a4aee11aa0b2a2940edbef7b839e7da93b91f51e3ae1c260829f3a8a7644736"},"blocks":{"#Testing Guidelines":[1,242],"#Testing Guidelines#Overview":[3,5],"#Testing Guidelines#Overview#{1}":[4,5],"#Testing Guidelines#Testing Levels":[6,152],"#Testing Guidelines#Testing Levels#1. Unit Testing":[8,45],"#Testing Guidelines#Testing Levels#1. Unit Testing#Frontend Components":[10,29],"#Testing Guidelines#Testing Levels#1. Unit Testing#Frontend Components#{1}":[11,29],"#Testing Guidelines#Testing Levels#1. Unit Testing#Backend Services":[30,45],"#Testing Guidelines#Testing Levels#1. Unit Testing#Backend Services#{1}":[31,45],"#Testing Guidelines#Testing Levels#2. Integration Testing":[46,81],"#Testing Guidelines#Testing Levels#2. Integration Testing#WebSocket Communication":[48,65],"#Testing Guidelines#Testing Levels#2. Integration Testing#WebSocket Communication#{1}":[49,65],"#Testing Guidelines#Testing Levels#2. Integration Testing#State Management":[66,81],"#Testing Guidelines#Testing Levels#2. Integration Testing#State Management#{1}":[67,81],"#Testing Guidelines#Testing Levels#3. End-to-End Testing":[82,108],"#Testing Guidelines#Testing Levels#3. End-to-End Testing#Trading Flow":[84,108],"#Testing Guidelines#Testing Levels#3. End-to-End Testing#Trading Flow#{1}":[85,108],"#Testing Guidelines#Testing Levels#4. Performance Testing":[109,152],"#Testing Guidelines#Testing Levels#4. Performance Testing#Load Testing":[111,131],"#Testing Guidelines#Testing Levels#4. Performance Testing#Load Testing#{1}":[112,131],"#Testing Guidelines#Testing Levels#4. Performance Testing#Stress Testing":[132,152],"#Testing Guidelines#Testing Levels#4. Performance Testing#Stress Testing#{1}":[133,152],"#Testing Guidelines#Testing Infrastructure":[153,196],"#Testing Guidelines#Testing Infrastructure#1. Test Environment":[155,175],"#Testing Guidelines#Testing Infrastructure#1. Test Environment#{1}":[156,175],"#Testing Guidelines#Testing Infrastructure#2. CI/CD Integration":[176,196],"#Testing Guidelines#Testing Infrastructure#2. CI/CD Integration#{1}":[177,196],"#Testing Guidelines#Best Practices":[197,238],"#Testing Guidelines#Best Practices#1. Test Organization":[199,204],"#Testing Guidelines#Best Practices#1. Test Organization#{1}":[200,200],"#Testing Guidelines#Best Practices#1. Test Organization#{2}":[201,201],"#Testing Guidelines#Best Practices#1. Test Organization#{3}":[202,202],"#Testing Guidelines#Best Practices#1. Test Organization#{4}":[203,204],"#Testing Guidelines#Best Practices#2. Test Coverage":[205,210],"#Testing Guidelines#Best Practices#2. Test Coverage#{1}":[206,206],"#Testing Guidelines#Best Practices#2. Test Coverage#{2}":[207,207],"#Testing Guidelines#Best Practices#2. Test Coverage#{3}":[208,208],"#Testing Guidelines#Best Practices#2. Test Coverage#{4}":[209,210],"#Testing Guidelines#Best Practices#3. Performance Goals":[211,216],"#Testing Guidelines#Best Practices#3. Performance Goals#{1}":[212,212],"#Testing Guidelines#Best Practices#3. Performance Goals#{2}":[213,213],"#Testing Guidelines#Best Practices#3. Performance Goals#{3}":[214,214],"#Testing Guidelines#Best Practices#3. Performance Goals#{4}":[215,216],"#Testing Guidelines#Best Practices#4. Quality Gates":[217,238],"#Testing Guidelines#Best Practices#4. Quality Gates#{1}":[218,238],"#Testing Guidelines#Version":[239,242],"#Testing Guidelines#Version#{1}":[240,240],"#Testing Guidelines#Version#{2}":[241,242]}},