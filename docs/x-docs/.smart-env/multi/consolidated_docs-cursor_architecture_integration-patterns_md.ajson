
"smart_sources:consolidated/docs-cursor/architecture/integration-patterns.md": {"path":"consolidated/docs-cursor/architecture/integration-patterns.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"c6c2d86790bf7a7d478cbbe2fdcd99b7f4fc92a4d8a41ab37768be028e2cf74f","at":1736310457123},"class_name":"SmartSource","outlinks":[{"title":"System Architecture","target":"./system-architecture.md","line":298},{"title":"State Management","target":"./state-management.md","line":299},{"title":"Performance Monitoring","target":"./performance-monitoring.md","line":300}],"last_import":{"mtime":1736310129762,"size":7561,"at":1736310457123,"hash":"c6c2d86790bf7a7d478cbbe2fdcd99b7f4fc92a4d8a41ab37768be028e2cf74f"},"blocks":{"#Integration Patterns":[1,304],"#Integration Patterns#Overview":[3,5],"#Integration Patterns#Overview#{1}":[4,5],"#Integration Patterns#Core Integration Mechanisms":[6,79],"#Integration Patterns#Core Integration Mechanisms#1. WebSocket Communication":[8,39],"#Integration Patterns#Core Integration Mechanisms#1. WebSocket Communication#{1}":[9,39],"#Integration Patterns#Core Integration Mechanisms#2. REST API Integration":[40,79],"#Integration Patterns#Core Integration Mechanisms#2. REST API Integration#{1}":[41,79],"#Integration Patterns#Frontend Integration":[80,143],"#Integration Patterns#Frontend Integration#1. State Synchronization":[82,118],"#Integration Patterns#Frontend Integration#1. State Synchronization#{1}":[83,118],"#Integration Patterns#Frontend Integration#2. Command Dispatch":[119,143],"#Integration Patterns#Frontend Integration#2. Command Dispatch#{1}":[120,143],"#Integration Patterns#Backend Integration":[144,184],"#Integration Patterns#Backend Integration#1. Session Management":[146,167],"#Integration Patterns#Backend Integration#1. Session Management#{1}":[147,167],"#Integration Patterns#Backend Integration#2. Message Processing":[168,184],"#Integration Patterns#Backend Integration#2. Message Processing#{1}":[169,184],"#Integration Patterns#Security Integration":[185,241],"#Integration Patterns#Security Integration#1. Authentication Flow":[187,212],"#Integration Patterns#Security Integration#1. Authentication Flow#{1}":[188,212],"#Integration Patterns#Security Integration#2. Authorization":[213,241],"#Integration Patterns#Security Integration#2. Authorization#{1}":[214,241],"#Integration Patterns#Error Handling":[242,296],"#Integration Patterns#Error Handling#1. Integration Errors":[244,273],"#Integration Patterns#Error Handling#1. Integration Errors#{1}":[245,273],"#Integration Patterns#Error Handling#2. Recovery Strategies":[274,296],"#Integration Patterns#Error Handling#2. Recovery Strategies#{1}":[275,296],"#Integration Patterns#Related Documentation":[297,301],"#Integration Patterns#Related Documentation#{1}":[298,298],"#Integration Patterns#Related Documentation#{2}":[299,299],"#Integration Patterns#Related Documentation#{3}":[300,301],"#Integration Patterns#Version":[302,304],"#Integration Patterns#Version#{1}":[303,304]}},