
"smart_sources:consolidated/docs-cursor/design/react-shadcn-implementation.md": {"path":"consolidated/docs-cursor/design/react-shadcn-implementation.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"c52f2e32f4259610afb9244f06c92a11e3dd6adbd0cd806456d58a86a8976ccf","at":1736310457142},"class_name":"SmartSource","outlinks":[{"title":"Component Patterns","target":"./component-patterns.md","line":257},{"title":"Design Tokens","target":"./design-tokens.md","line":258},{"title":"Technical Design Considerations","target":"./technical-design-considerations.md","line":259},{"title":"Animation Patterns","target":"./animation-patterns.md","line":260}],"last_import":{"mtime":1736310129778,"size":5357,"at":1736310457142,"hash":"c52f2e32f4259610afb9244f06c92a11e3dd6adbd0cd806456d58a86a8976ccf"},"blocks":{"#React and Shadcn UI Implementation Guide":[1,263],"#React and Shadcn UI Implementation Guide#Overview":[3,5],"#React and Shadcn UI Implementation Guide#Overview#{1}":[4,5],"#React and Shadcn UI Implementation Guide#Technology Stack":[6,28],"#React and Shadcn UI Implementation Guide#Technology Stack#Core Technologies":[8,14],"#React and Shadcn UI Implementation Guide#Technology Stack#Core Technologies#{1}":[9,9],"#React and Shadcn UI Implementation Guide#Technology Stack#Core Technologies#{2}":[10,10],"#React and Shadcn UI Implementation Guide#Technology Stack#Core Technologies#{3}":[11,11],"#React and Shadcn UI Implementation Guide#Technology Stack#Core Technologies#{4}":[12,12],"#React and Shadcn UI Implementation Guide#Technology Stack#Core Technologies#{5}":[13,14],"#React and Shadcn UI Implementation Guide#Technology Stack#Key Dependencies":[15,28],"#React and Shadcn UI Implementation Guide#Technology Stack#Key Dependencies#{1}":[16,28],"#React and Shadcn UI Implementation Guide#Component Architecture":[29,125],"#React and Shadcn UI Implementation Guide#Component Architecture#1. Component Structure":[31,68],"#React and Shadcn UI Implementation Guide#Component Architecture#1. Component Structure#{1}":[32,68],"#React and Shadcn UI Implementation Guide#Component Architecture#2. State Management":[69,97],"#React and Shadcn UI Implementation Guide#Component Architecture#2. State Management#{1}":[70,97],"#React and Shadcn UI Implementation Guide#Component Architecture#3. Component Integration":[98,125],"#React and Shadcn UI Implementation Guide#Component Architecture#3. Component Integration#{1}":[99,125],"#React and Shadcn UI Implementation Guide#Implementation Guidelines":[126,156],"#React and Shadcn UI Implementation Guide#Implementation Guidelines#1. Component Design":[128,135],"#React and Shadcn UI Implementation Guide#Implementation Guidelines#1. Component Design#{1}":[129,129],"#React and Shadcn UI Implementation Guide#Implementation Guidelines#1. Component Design#{2}":[130,130],"#React and Shadcn UI Implementation Guide#Implementation Guidelines#1. Component Design#{3}":[131,131],"#React and Shadcn UI Implementation Guide#Implementation Guidelines#1. Component Design#{4}":[132,132],"#React and Shadcn UI Implementation Guide#Implementation Guidelines#1. Component Design#{5}":[133,133],"#React and Shadcn UI Implementation Guide#Implementation Guidelines#1. Component Design#{6}":[134,135],"#React and Shadcn UI Implementation Guide#Implementation Guidelines#2. State Management":[136,142],"#React and Shadcn UI Implementation Guide#Implementation Guidelines#2. State Management#{1}":[137,137],"#React and Shadcn UI Implementation Guide#Implementation Guidelines#2. State Management#{2}":[138,138],"#React and Shadcn UI Implementation Guide#Implementation Guidelines#2. State Management#{3}":[139,139],"#React and Shadcn UI Implementation Guide#Implementation Guidelines#2. State Management#{4}":[140,140],"#React and Shadcn UI Implementation Guide#Implementation Guidelines#2. State Management#{5}":[141,142],"#React and Shadcn UI Implementation Guide#Implementation Guidelines#3. Form Handling":[143,149],"#React and Shadcn UI Implementation Guide#Implementation Guidelines#3. Form Handling#{1}":[144,144],"#React and Shadcn UI Implementation Guide#Implementation Guidelines#3. Form Handling#{2}":[145,145],"#React and Shadcn UI Implementation Guide#Implementation Guidelines#3. Form Handling#{3}":[146,146],"#React and Shadcn UI Implementation Guide#Implementation Guidelines#3. Form Handling#{4}":[147,147],"#React and Shadcn UI Implementation Guide#Implementation Guidelines#3. Form Handling#{5}":[148,149],"#React and Shadcn UI Implementation Guide#Implementation Guidelines#4. Table Implementation":[150,156],"#React and Shadcn UI Implementation Guide#Implementation Guidelines#4. Table Implementation#{1}":[151,151],"#React and Shadcn UI Implementation Guide#Implementation Guidelines#4. Table Implementation#{2}":[152,152],"#React and Shadcn UI Implementation Guide#Implementation Guidelines#4. Table Implementation#{3}":[153,153],"#React and Shadcn UI Implementation Guide#Implementation Guidelines#4. Table Implementation#{4}":[154,154],"#React and Shadcn UI Implementation Guide#Implementation Guidelines#4. Table Implementation#{5}":[155,156],"#React and Shadcn UI Implementation Guide#Best Practices":[157,186],"#React and Shadcn UI Implementation Guide#Best Practices#1. Component Development":[159,165],"#React and Shadcn UI Implementation Guide#Best Practices#1. Component Development#{1}":[160,160],"#React and Shadcn UI Implementation Guide#Best Practices#1. Component Development#{2}":[161,161],"#React and Shadcn UI Implementation Guide#Best Practices#1. Component Development#{3}":[162,162],"#React and Shadcn UI Implementation Guide#Best Practices#1. Component Development#{4}":[163,163],"#React and Shadcn UI Implementation Guide#Best Practices#1. Component Development#{5}":[164,165],"#React and Shadcn UI Implementation Guide#Best Practices#2. State Management":[166,172],"#React and Shadcn UI Implementation Guide#Best Practices#2. State Management#{1}":[167,167],"#React and Shadcn UI Implementation Guide#Best Practices#2. State Management#{2}":[168,168],"#React and Shadcn UI Implementation Guide#Best Practices#2. State Management#{3}":[169,169],"#React and Shadcn UI Implementation Guide#Best Practices#2. State Management#{4}":[170,170],"#React and Shadcn UI Implementation Guide#Best Practices#2. State Management#{5}":[171,172],"#React and Shadcn UI Implementation Guide#Best Practices#3. Performance":[173,179],"#React and Shadcn UI Implementation Guide#Best Practices#3. Performance#{1}":[174,174],"#React and Shadcn UI Implementation Guide#Best Practices#3. Performance#{2}":[175,175],"#React and Shadcn UI Implementation Guide#Best Practices#3. Performance#{3}":[176,176],"#React and Shadcn UI Implementation Guide#Best Practices#3. Performance#{4}":[177,177],"#React and Shadcn UI Implementation Guide#Best Practices#3. Performance#{5}":[178,179],"#React and Shadcn UI Implementation Guide#Best Practices#4. Testing":[180,186],"#React and Shadcn UI Implementation Guide#Best Practices#4. Testing#{1}":[181,181],"#React and Shadcn UI Implementation Guide#Best Practices#4. Testing#{2}":[182,182],"#React and Shadcn UI Implementation Guide#Best Practices#4. Testing#{3}":[183,183],"#React and Shadcn UI Implementation Guide#Best Practices#4. Testing#{4}":[184,184],"#React and Shadcn UI Implementation Guide#Best Practices#4. Testing#{5}":[185,186],"#React and Shadcn UI Implementation Guide#Common Patterns":[187,255],"#React and Shadcn UI Implementation Guide#Common Patterns#1. Form Components":[189,219],"#React and Shadcn UI Implementation Guide#Common Patterns#1. Form Components#{1}":[190,219],"#React and Shadcn UI Implementation Guide#Common Patterns#2. Data Display":[220,255],"#React and Shadcn UI Implementation Guide#Common Patterns#2. Data Display#{1}":[221,255],"#React and Shadcn UI Implementation Guide#Related Documentation":[256,261],"#React and Shadcn UI Implementation Guide#Related Documentation#{1}":[257,257],"#React and Shadcn UI Implementation Guide#Related Documentation#{2}":[258,258],"#React and Shadcn UI Implementation Guide#Related Documentation#{3}":[259,259],"#React and Shadcn UI Implementation Guide#Related Documentation#{4}":[260,261],"#React and Shadcn UI Implementation Guide#Version":[262,263],"#React and Shadcn UI Implementation Guide#Version#{1}":[263,263]}},