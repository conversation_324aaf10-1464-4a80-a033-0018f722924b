# REST API Documentation

This document describes the REST API endpoints and their usage.

## Authentication

### Login
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "string",
  "password": "string"
}

Response: 200 OK
{
  "token": "string",
  "user": {
    "id": "string",
    "email": "string",
    "role": "string"
  }
}
```

### Refresh Token
```http
POST /api/v1/auth/refresh
Authorization: Bearer <token>

Response: 200 OK
{
  "token": "string"
}
```

## Auctions

### List Auctions
```http
GET /api/v1/auctions
Authorization: Bearer <token>

Response: 200 OK
{
  "auctions": [
    {
      "id": "string",
      "title": "string",
      "description": "string",
      "currentPrice": "number",
      "endTime": "string"
    }
  ]
}
```

### Create Auction
```http
POST /api/v1/auctions
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "string",
  "description": "string",
  "startPrice": "number",
  "endTime": "string"
}

Response: 201 Created
{
  "id": "string",
  "title": "string",
  "description": "string",
  "currentPrice": "number",
  "endTime": "string"
}
```

## Bids

### Place Bid
```http
POST /api/v1/auctions/{auctionId}/bids
Authorization: Bearer <token>
Content-Type: application/json

{
  "amount": "number"
}

Response: 201 Created
{
  "id": "string",
  "amount": "number",
  "timestamp": "string"
}
```

### Get Auction Bids
```http
GET /api/v1/auctions/{auctionId}/bids
Authorization: Bearer <token>

Response: 200 OK
{
  "bids": [
    {
      "id": "string",
      "amount": "number",
      "userId": "string",
      "timestamp": "string"
    }
  ]
}
```

## Error Responses

### 400 Bad Request
```http
{
  "error": "string",
  "message": "string",
  "details": {}
}
```

### 401 Unauthorized
```http
{
  "error": "Unauthorized",
  "message": "Invalid or expired token"
}
```

### 404 Not Found
```http
{
  "error": "Not Found",
  "message": "Resource not found"
}