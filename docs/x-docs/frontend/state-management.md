# State Management

This document explains how state is managed across the frontend application.

## Overview

### State Categories
1. Application State
2. UI State
3. Server Cache
4. Form State
5. Router State

## Vialto Store

### Store Structure
```typescript
interface RootStore {
  auth: AuthStore;
  auction: AuctionStore;
  user: UserStore;
  notification: NotificationStore;
}
```

### Store Modules

#### Auth Store
```typescript
interface AuthStore {
  user: User | null;
  isAuthenticated: boolean;
  token: string | null;
  login: (credentials: Credentials) => Promise<void>;
  logout: () => void;
}
```

#### Auction Store
```typescript
interface AuctionStore {
  auctions: Auction[];
  currentAuction: Auction | null;
  bids: Bid[];
  fetchAuctions: () => Promise<void>;
  placeBid: (bid: Bid) => Promise<void>;
}
```

## State Management Patterns

### Composables
```typescript
// User authentication composable
const useAuth = () => {
  const store = useStore();
  
  return {
    isAuthenticated: computed(() => store.auth.isAuthenticated),
    login: store.auth.login,
    logout: store.auth.logout
  };
};
```

### Component State
```typescript
// Local component state
const useCounter = () => {
  const count = ref(0);
  const increment = () => count.value++;
  const decrement = () => count.value--;
  
  return {
    count,
    increment,
    decrement
  };
};
```

## Best Practices

### State Organization
- Keep state close to where it's used
- Avoid redundant state
- Use computed properties
- Normalize complex data

### Performance
- Use shallow refs when possible
- Avoid deep watchers
- Batch updates
- Lazy load stores

### Data Flow
- One-way data flow
- Props down, events up
- Avoid state duplication
- Use actions for side effects

## Common Patterns

### Loading States
```typescript
const useAsync = <T>(fn: () => Promise<T>) => {
  const isLoading = ref(false);
  const error = ref<Error | null>(null);
  const data = ref<T | null>(null);

  const execute = async () => {
    isLoading.value = true;
    try {
      data.value = await fn();
    } catch (err) {
      error.value = err as Error;
    } finally {
      isLoading.value = false;
    }
  };

  return {
    isLoading,
    error,
    data,
    execute
  };
};
```

### Form Handling
```typescript
const useForm = <T>(initialValues: T) => {
  const values = ref(initialValues);
  const errors = ref({});
  
  const validate = () => {
    // Validation logic
  };
  
  const submit = async () => {
    if (validate()) {
      // Submit logic
    }
  };
  
  return {
    values,
    errors,
    validate,
    submit
  };
};