# Design Tokens

## Color Palette

### Primary Colors
```css
--primary: #2563eb; /* Blue 600 - Primary actions, key interactions */
--primary-foreground: #ffffff;
--primary-hover: #1d4ed8; /* Blue 700 */
--primary-pressed: #1e40af; /* Blue 800 */
```

### Secondary Colors
```css
--secondary: #475569; /* Slate 600 - Supporting elements */
--secondary-foreground: #ffffff;
--secondary-hover: #334155; /* Slate 700 */
--secondary-pressed: #1e293b; /* Slate 800 */
```

### Semantic Colors
```css
--success: #16a34a; /* Green 600 - Buy/Profit */
--success-foreground: #ffffff;
--error: #dc2626; /* Red 600 - Sell/Loss */
--error-foreground: #ffffff;
--warning: #d97706; /* Amber 600 - Alerts */
--warning-foreground: #ffffff;
--info: #0284c7; /* Sky 600 - Info */
--info-foreground: #ffffff;
```

### Neutral Colors
```css
--background: #ffffff;
--foreground: #0f172a; /* Slate 900 */
--muted: #f1f5f9; /* Slate 100 */
--muted-foreground: #64748b; /* Slate 500 */
--border: #e2e8f0; /* Slate 200 */
```

### Dark Mode Colors
```css
--background-dark: #0f172a; /* Slate 900 */
--foreground-dark: #f8fafc; /* Slate 50 */
--muted-dark: #1e293b; /* Slate 800 */
--muted-foreground-dark: #94a3b8; /* Slate 400 */
--border-dark: #334155; /* Slate 700 */
```

## Typography

### Font Families
```css
--font-sans: "Inter", system-ui, -apple-system, sans-serif;
--font-mono: "JetBrains Mono", monospace;
```

### Font Sizes
```css
--font-size-xs: 0.75rem;    /* 12px */
--font-size-sm: 0.875rem;   /* 14px */
--font-size-base: 1rem;     /* 16px */
--font-size-lg: 1.125rem;   /* 18px */
--font-size-xl: 1.25rem;    /* 20px */
--font-size-2xl: 1.5rem;    /* 24px */
--font-size-3xl: 1.875rem;  /* 30px */
--font-size-4xl: 2.25rem;   /* 36px */
```

### Line Heights
```css
--line-height-tight: 1.25;
--line-height-normal: 1.5;
--line-height-relaxed: 1.75;
```

### Font Weights
```css
--font-weight-normal: 400;
--font-weight-medium: 500;
--font-weight-semibold: 600;
--font-weight-bold: 700;
```

### Letter Spacing
```css
--letter-spacing-tight: -0.025em;
--letter-spacing-normal: 0em;
--letter-spacing-wide: 0.025em;
```

## Spacing

### Base Grid (8px)
```css
--spacing-px: 1px;
--spacing-0.5: 0.125rem;  /* 2px */
--spacing-1: 0.25rem;     /* 4px */
--spacing-2: 0.5rem;      /* 8px - Base unit */
--spacing-3: 0.75rem;     /* 12px */
--spacing-4: 1rem;        /* 16px */
--spacing-6: 1.5rem;      /* 24px */
--spacing-8: 2rem;        /* 32px */
--spacing-12: 3rem;       /* 48px */
--spacing-16: 4rem;       /* 64px */
```

### Layout Spacing
```css
--container-padding: var(--spacing-4);
--section-spacing: var(--spacing-8);
--card-padding: var(--spacing-4);
--input-padding: var(--spacing-2);
```

## Elevation

### Shadows
```css
--shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
--shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
--shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
--shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
```

### Z-Index
```css
--z-index-dropdown: 1000;
--z-index-sticky: 1020;
--z-index-fixed: 1030;
--z-index-modal-backdrop: 1040;
--z-index-modal: 1050;
--z-index-popover: 1060;
--z-index-tooltip: 1070;
```

## Border Radius
```css
--radius-sm: 0.125rem;   /* 2px */
--radius: 0.375rem;      /* 6px */
--radius-md: 0.5rem;     /* 8px */
--radius-lg: 0.75rem;    /* 12px */
--radius-full: 9999px;
```

## Transitions
```css
--transition-all: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
--transition-colors: background-color 0.15s cubic-bezier(0.4, 0, 0.2, 1), border-color 0.15s cubic-bezier(0.4, 0, 0.2, 1), color 0.15s cubic-bezier(0.4, 0, 0.2, 1);