# State Management Architecture

## Overview
The AU25 Auction Platform implements a unique state management approach that prioritizes reliability and simplicity over traditional event sourcing patterns. This document details the implementation of our full state reconstruction pattern.

## Core Concepts

### 1. Full State Reconstruction
Instead of traditional event sourcing or delta updates, we:
- Send complete state trees on each update
- Utilize ObjectDB's in-memory cache for efficiency
- Filter state based on user roles and permissions
- Maintain consistent state across all clients

### 2. Command-Driven Updates
```typescript
interface CommandFlow {
  client: {
    send: ClientCommand;
    receive: LiveClientStore;
  };
  server: {
    process: CommandProcessor;
    generate: StateGenerator;
    filter: RoleBasedFilter;
  };
}
```

## Implementation Details

### 1. Client Commands
```kotlin
sealed class ClientCommand {
    // Base command interface
    interface Command {
        val type: CommandType
        val timestamp: Instant
        val sessionId: String
    }

    // Specific command types
    data class PlaceBid(
        override val sessionId: String,
        val auctionId: String,
        val quantity: Int,
        val price: BigDecimal
    ) : Command {
        override val type = CommandType.PLACE_BID
        override val timestamp = Instant.now()
    }

    data class UpdatePreferences(
        override val sessionId: String,
        val preferences: UserPreferences
    ) : Command {
        override val type = CommandType.UPDATE_PREFERENCES
        override val timestamp = Instant.now()
    }
}
```

### 2. State Structure
```kotlin
// Core state interface
interface ClientState {
    val version: Long
    val timestamp: Instant
    val data: StateData
}

// Main state container
data class StateData(
    val auctions: Map<String, AuctionState>,
    val user: UserState,
    val preferences: PreferencesState,
    val notifications: List<Notification>
)

// Auction-specific state
data class AuctionState(
    val id: String,
    val status: AuctionStatus,
    val currentRound: RoundState,
    val previousRounds: List<RoundState>,
    val participants: Map<String, ParticipantState>
)
```

### 3. Role-Based Filtering
```kotlin
class StateFilter {
    fun filterForRole(
        state: StateData,
        role: UserRole
    ): StateData {
        return when (role) {
            UserRole.ADMIN -> state
            UserRole.AUCTIONEER -> filterForAuctioneer(state)
            UserRole.TRADER -> filterForTrader(state)
            UserRole.OBSERVER -> filterForObserver(state)
        }
    }

    private fun filterForTrader(state: StateData): StateData {
        return state.copy(
            auctions = state.auctions.mapValues { (_, auction) ->
                filterAuctionForTrader(auction)
            }
        )
    }
}
```

## State Flow Patterns

### 1. Command Processing
```mermaid
sequenceDiagram
    participant Client
    participant CommandProcessor
    participant StateManager
    participant ObjectDB
    
    Client->>CommandProcessor: Send Command
    CommandProcessor->>StateManager: Process Command
    StateManager->>ObjectDB: Update State
    StateManager->>StateManager: Generate New State
    StateManager->>StateManager: Filter State
    StateManager->>Client: Send Full State
```

### 2. State Synchronization
```typescript
class StateSynchronizer {
    private currentState: ClientState;
    private commandQueue: ClientCommand[];
    
    async processCommand(command: ClientCommand): Promise<void> {
        // Add to queue
        this.commandQueue.push(command);
        
        // Process command
        await this.sendCommand(command);
        
        // Wait for state update
        await this.waitForStateUpdate();
        
        // Remove from queue
        this.commandQueue = this.commandQueue.filter(
            cmd => cmd.id !== command.id
        );
    }
    
    private async waitForStateUpdate(): Promise<void> {
        return new Promise((resolve) => {
            const unsubscribe = subscribe(state => {
                if (this.isStateUpdated(state)) {
                    unsubscribe();
                    resolve();
                }
            });
        });
    }
}
```

## Performance Considerations

### 1. Memory Management
```kotlin
class StateManager {
    private val stateCache = ConcurrentHashMap<String, StateData>()
    
    fun optimizeMemory() {
        // Remove old states
        stateCache.entries
            .filter { it.value.timestamp.isBefore(threshold) }
            .forEach { stateCache.remove(it.key) }
        
        // Compact current states
        stateCache.values.forEach { state ->
            state.auctions.values.forEach { auction ->
                auction.previousRounds = auction.previousRounds
                    .takeLast(MAX_ROUNDS_TO_KEEP)
            }
        }
    }
}
```

### 2. Bandwidth Optimization
```typescript
interface BandwidthOptimization {
    compression: {
        enabled: boolean;
        algorithm: 'gzip' | 'brotli';
        level: number;
    };
    batching: {
        enabled: boolean;
        maxBatchSize: number;
        maxWaitTime: number;
    };
    prioritization: {
        criticalUpdates: string[];
        batchableUpdates: string[];
    };
}
```

## Error Handling

### 1. State Validation
```typescript
interface StateValidation {
    validateState(state: ClientState): ValidationResult;
    validateCommand(command: ClientCommand): ValidationResult;
    validateTransition(
        prevState: ClientState,
        command: ClientCommand,
        nextState: ClientState
    ): ValidationResult;
}

interface ValidationResult {
    isValid: boolean;
    errors: ValidationError[];
    warnings: ValidationWarning[];
}
```

### 2. Recovery Strategies
```typescript
class StateRecovery {
    async recoverFromError(error: StateError): Promise<void> {
        switch (error.type) {
            case 'desync':
                await this.requestFullState();
                break;
            case 'corruption':
                await this.rollbackToLastValid();
                break;
            case 'conflict':
                await this.resolveConflict(error.details);
                break;
        }
    }
}
```

## Related Documentation
- [System Architecture](./system-architecture.md)
- [Integration Patterns](./integration-patterns.md)
- [Performance Monitoring](./performance-monitoring.md)

## Version
Last Updated: January 7, 2025
State Management Version: 1.0 