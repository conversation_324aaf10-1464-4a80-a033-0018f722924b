# AU25 Domain Model and Commands

## Overview
This document outlines the core domain model and command system for the AU25 Auction Platform, defining the fundamental business entities, their relationships, and the commands that operate on them.

## Domain Model

### Core Entities

#### 1. Auction System
```typescript
interface Auction {
  auctionName: string;
  closed: boolean;
  hidden: boolean;
  notice: string;
  startingTime?: Date;
  commonStateText: string;
  auctioneerStateText: string;
  tradingCompanies: CompanyProxy[];
  messages: AuctionMessage[];
  usersSeenAuction: Set<PersonProxy>;
  companiesSeenAuction: Set<CompanyProxy>;
  counterpartyCreditLimits: CounterpartyCreditLimit[];
}

interface DeAuction extends Auction {
  settings: DeAuctionSettings;
  auctioneerState: DeAuctioneerState;
  commonState: DeCommonState;
  autopilotMode: AutopilotMode;
  priceDecimalPlaces: number;
  awardedRound?: DeRound;
  revisedOrders: DeOrder[];
  rounds: DeRound[];
  deTradingCompanies: DeTradingCompany[];
}

interface DeAuctionSettings {
  useCounterpartyCredits: boolean;
  startingPriceAnnouncementMins: number;
  roundOpenMinSecs: number;
  roundClosedMinSecs: number;
  roundOrangeSecs: number;
  roundRedSecs: number;
  costMultiplier: number;
  quantityUnits: string;
  quantityMinimum: number;
  quantityStep: number;
  priceUnits: string;
  priceDecimalPlaces: number;
  priceRule: DePriceRule;
}
```

#### 2. Users and Companies
```typescript
interface Person {
  username: string;
  password: string;
  company?: Company;
  email: string;
  isObserver: boolean;
  isTester: boolean;
  phone: string;
  role: AuUserRole;
}

interface Company {
  longname: string;
  shortname: string;
}

interface CompanyProxy {
  companyId: number;
  shortnameAtAuctionTime: string;
  longnameAtAuctionTime: string;
}

enum AuUserRole {
  AUCTIONEER,
  TRADER,
  OBSERVER,
  ADMIN
}
```

#### 3. Session Management
```typescript
interface AuSession {
  sessionId: string;
  created: Date;
  browserName?: string;
  browserVersion?: string;
  browserOs?: string;
  socketStateLabel: string;
  socketLastClosed?: Date;
  auction?: Auction;
  user?: Person;
  pageLabel: string;
  lastPing: Date;
  terminationTime?: Date;
  terminationReasonLabel?: string;
}
```

## Command System

### Session Commands

#### 1. Session Management
```typescript
interface SessionCommands {
  create: {
    // SessionCreateCommand
    purpose: "Creates or retrieves a session";
    validation: [
      "Requires non-null session_id",
      "Checks for existing session"
    ];
    actions: [
      "Creates new session if needed",
      "Saves session to database"
    ];
  };
  
  terminate: {
    // SessionTerminateCommand
    purpose: "Terminates an active session";
    params: {
      reason: SessionTerminationReason;
    };
    validation: [
      "Requires valid session",
      "Prevents re-termination"
    ];
    actions: [
      "Terminates with reason",
      "Updates session state",
      "Logs termination"
    ];
  };
}
```

### User Commands

#### 1. Authentication and Management
```typescript
interface UserCommands {
  login: {
    // LoginCommand
    purpose: "Authenticates users";
    params: {
      username: string;
      password: string;
    };
    validation: [
      "Non-blank credentials",
      "User exists and active",
      "Password matches"
    ];
    actions: [
      "Terminates existing sessions",
      "Creates new session",
      "Logs session details"
    ];
  };
  
  save: {
    // UserSaveCommand
    purpose: "Creates/updates users";
    params: {
      company_id: string;
      email: string;
      password: string;
      phone: string;
      role: AuUserRole;
      user_id: string;
      username: string;
    };
    validation: [
      "Requires auctioneer role",
      "Valid credentials format",
      "Unique username",
      "Company required for traders"
    ];
    actions: [
      "Creates/updates user",
      "Manages company links",
      "Updates database"
    ];
  };
  
  delete: {
    // UserDeleteCommand
    purpose: "Removes users";
    params: {
      user_id: string;
    };
    validation: [
      "Requires auctioneer role",
      "User exists",
      "Not in open auctions"
    ];
    actions: [
      "Marks as deleted",
      "Terminates sessions",
      "Updates database"
    ];
  };
}
```

### Auction Commands

#### 1. Auction Management
```typescript
interface AuctionCommands {
  row: {
    // AuctionRowCommand
    purpose: "Manages auction visibility";
    params: {
      auction_id: string;
      instruction: "HIDE" | "UNHIDE" | "DELETE";
    };
    validation: [
      "Requires auctioneer role",
      "Valid auction state"
    ];
    actions: [
      "Updates visibility",
      "Manages user redirects",
      "Updates database"
    ];
  };
  
  select: {
    // AuctionSelectCommand
    purpose: "Handles auction selection";
    params: {
      auction_id: string;
    };
    validation: [
      "Valid session",
      "User has access",
      "Auction not deleted"
    ];
    actions: [
      "Sets current auction",
      "Updates page state",
      "Tracks auction views"
    ];
  };
}
```

### Company Commands

#### 1. Company Management
```typescript
interface CompanyCommands {
  save: {
    // CompanySaveCommand
    purpose: "Creates/updates companies";
    params: {
      company_id: string;
      company_shortname: string;
      company_longname: string;
    };
    validation: [
      "Requires auctioneer role",
      "Valid name format",
      "Unique names",
      "Not in open auctions"
    ];
    actions: [
      "Creates/updates company",
      "Manages user sessions",
      "Updates database"
    ];
  };
  
  delete: {
    // CompanyDeleteCommand
    purpose: "Removes companies";
    params: {
      company_id: string;
    };
    validation: [
      "Requires auctioneer role",
      "No auction history",
      "No active traders"
    ];
    actions: [
      "Deletes company",
      "Updates database"
    ];
  };
}
```

### System Commands

#### 1. Database Management
```typescript
interface SystemCommands {
  dbInit: {
    // DbInitCommand
    purpose: "Initializes database";
    validation: [
      "Environment check"
    ];
    actions: [
      "Clears database",
      "Creates default admin"
    ];
  };
  
  dbDeleteAuctions: {
    // DbDeleteAuctionsCommand
    purpose: "Removes all auctions";
    validation: [
      "Environment check"
    ];
    actions: [
      "Deletes all auctions",
      "Updates database"
    ];
  };
}
```

### Communication Commands

#### 1. Message Management
```typescript
interface CommunicationCommands {
  messageSend: {
    // MessageSendCommand
    purpose: "Sends auction messages";
    params: {
      auction_id: string;
      message: string;
    };
    validation: [
      "Valid session",
      "Valid auction",
      "User has access"
    ];
    actions: [
      "Creates message",
      "Updates auction",
      "Saves to database"
    ];
  };
  
  errorsSend: {
    // ErrorsSendCommand
    purpose: "Sends error messages";
    params: {
      auction_id: string;
      trader_session_id: string;
      error: string;
    };
    validation: [
      "Valid auction",
      "Non-blank error"
    ];
    actions: [
      "Creates error message",
      "Updates auction",
      "Saves to database"
    ];
  };
}
```

## Related Documentation
- [Technical Architecture](../architecture/system-architecture.md)
- [Development Guidelines](../development/guidelines.md)
- [State Management](../development/state-management.md)

## Version
Last Updated: January 7, 2025
Domain Model Version: 1.0 