this project is a multi repo, it has:
- gradle/           - a directory of gradle projects
- nodes/packages/   - a directory of node packages
- python/          - a directory of python scripts

The rules of the auction are at: /docs/design/A.auction-rules.md

The framework has an interesting architecture:
see: /docs/prompts/responses/20250106-1648.backend-stabilization-suggestions.md
- it is a spa which connects to a quarkus websocket server
- it uses websockets after that for all communication with the serer
- ie: it publishes commands to the server and subscribes to events from the server
- There is no request/response at all, no REST API, no sql database
- the frontend is MVVM using either vue3 pinia, or react vialto

Currently the project runs.  
- the frontend is an nx/vue2/ant-design-vue project at: /nodejs/packages/au21-frontend
- the backend is a quarkus project at: /gradle/au21-engine

However the UI is unsatisfactory, and also is too old to improve: it uses nx/webpack/vue2/ant-design-vue 1x, all of which are deprecated.

We initially planned to build the new user interface in vue3/naiveui, using the pinx naiveui template. That project is at: /nodejs/packages/au25-naiveui

Then we decided to redo it in react/shadcn because claude sonnet 3.5 is good a producing ui prototypes! That project is at: /nodejs/packages/react-mobx-vi

Periodically I will ask you to create a git commit message: 
- Starts with a type (feat, docs, etc.)
- Has a concise subject line
- Includes a detailed description of changes
- References related documentation
and then commit the all changes (ie: not just the ones you edited) to git and push to remote.
NOTE: YOU MAY NOT EVER EDIT THE FILE: [docs/CHANGELOG.oriFgin.dev.md](../CHANGELOG.origin.dev.md) yourself, that can only be done by executing the just task: update-origin-dev-changelog.

Note: 
- I prefer a process of requirements -> design -> implemtanion -> testing -> deployment.
- So, don't write code unles you understand the requirements and design.

Ok, now, looking at the project files:  
- docs/project/au25-auction.prd.md
- docs/project/au25-auction.plan.md
- docs/project/au25-auction.status.md

What do you suggest we work on next?

