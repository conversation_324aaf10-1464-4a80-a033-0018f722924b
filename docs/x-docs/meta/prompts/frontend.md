# Frontend Documentation Prompts

Use these prompts to generate or update frontend documentation.

## Component Documentation

```prompt
Document the [Component Name] component:

1. Purpose
- What problem does this component solve?
- What are its main responsibilities?

2. Props/Inputs
- What props does it accept?
- Which are required vs optional?
- What are their types and defaults?

3. Events/Outputs
- What events does it emit?
- When are they triggered?
- What data do they contain?

4. Usage Examples
- Basic usage
- Common variations
- Edge cases
```

## State Management

```prompt
Document state management for [Feature Name]:

1. Store Structure
- What data is stored?
- How is it organized?
- What are the types?

2. Actions/Mutations
- What actions are available?
- When should they be used?
- What side effects occur?

3. Selectors/Getters
- What derived data is available?
- How is it computed?
- When should it be used?

4. Integration
- How does it connect to components?
- What are the performance considerations?
```

## UI/UX Guidelines

```prompt
Document UI/UX guidelines for [Feature Area]:

1. Visual Design
- Color scheme
- Typography
- Spacing
- Icons/Images

2. Interaction Design
- User flows
- Feedback mechanisms
- Error states
- Loading states

3. Accessibility
- ARIA roles
- Keyboard navigation
- Screen reader support
- Color contrast

4. Responsive Design
- Breakpoints
- Layout changes
- Touch targets
- Performance
```

## Testing Strategy

```prompt
Document testing approach for [Frontend Feature]:

1. Unit Tests
- Component testing
- Store testing
- Utility testing
- Mock strategies

2. Integration Tests
- Component integration
- Store integration
- API integration
- Error scenarios

3. E2E Tests
- Critical paths
- User flows
- Edge cases
- Performance tests

4. Test Data
- Fixtures
- Factories
- Mocks
- Stubs