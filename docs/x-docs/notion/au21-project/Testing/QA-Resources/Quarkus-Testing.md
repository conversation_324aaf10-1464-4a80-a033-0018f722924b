# Quarkus Testing

Owner: <PERSON>

Need to run charlize first, to access 8080

- (as currently the 4040 is hard-coded to dev1)

```bash
nx serve charlize
```

1) debug the main: this works (but not on 4040 currently)

- the debugger works, but kotest would have to be run separately

2) Attach debugger to ./gradle quarkusDev (works)

- [https://www.jetbrains.com/help/idea/quarkus.html#debug](https://www.jetbrains.com/help/idea/quarkus.html#debug)

```bash
#ie:
# 1) run quarkus  
./gradlew quarkusDev
 
# 2) then attach debugger:     
# From the main menu, select Run | Attach to Process.
```

3) Continuous (didn't work):  

- [https://www.javaadvent.com/2021/12/continuous-testing-with-quarkus.html](https://www.javaadvent.com/2021/12/continuous-testing-with-quarkus.html)

installing cli (requires jbang):

```
# install jbang: 
curl -Ls https://sh.jbang.dev | bash -s - app install --fresh --force quarkus@quarkusio

# add quarkus-cli to jbang:    
jbang trust add https://repo1.maven.org/maven2/io/quarkus/quarkus-cli/

# install quarkus-test
curl -Ls https://sh.jbang.dev | bash -s - app install --fresh --force quarkus@quarkusio\n

# run quarkus:
quarkus
```

- [https://quarkus.io/guides/cli-tooling](https://quarkus.io/guides/cli-tooling)

# Mocking

```kotlin
  fun fix_time(){
       DateTimeUtils.setCurrentMillisFixed(DateTime().millis)
  }

@QuarkusTest
@TestInstance(PER_CLASS)
class Scenario01 : AuTestBase() {

    @BeforeAll
    fun before_all(){
        fix_time()
    }

...
```