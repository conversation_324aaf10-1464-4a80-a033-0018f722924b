# Class Diagrams

Owner: <PERSON>

Currently done in 3 steps

- kotlin classes → puml
    - Plantuml-writer
    - au21-engine/src/main/kotlin/exp/plantuml-writer.kt
    - Note: you can specify the classes with any combination of:
        - base class, to get the subclasses,
        - annotation class, to get the annotated class
        - or name the classes indivdually.
- puml → svg
    
    ```bash
     ./gradlew plantUml `
    ```
    
    - ie: inputs and outputs defined in build.gradle
- svg → pdf
    - For Linux
    
    ```bash
    inkscape <input>.svg --export-pdf=<output>.pdf
    ```