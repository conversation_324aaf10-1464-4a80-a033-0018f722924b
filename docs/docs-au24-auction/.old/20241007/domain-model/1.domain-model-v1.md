Certainly. I'll create the entire updated document, including the revised TypeScript interfaces,
Mermaid diagram, and comprehensive documentation. I'll rearrange the content for clarity and ensure
all important details and constraints are included.

Here's the updated document:

# Auction Management System

## Overview

The **Auction Management System** facilitates hosting and participation in auctions by multiple
companies. It supports various user roles, groupings, and auction types, ensuring data privacy,
role-based access control, and flexibility in user and auction management. The system accommodates
both live and mock environments, enabling seamless transitions between production and testing
scenarios.

## Domain Model

```typescript
// Enumerations:
enum UserRole {
	SUPER_USER,
	SYSTEM_ADMIN,
	HOST_USER
}

enum GroupRole {
	ADMIN,
	AUCTIONEER,
	TRADER,
	INTERNAL_OBSERVER,
	EXTERNAL_OBSERVER
}

enum AuctionType {
	ONE_SIDED,
	TWO_SIDED_BROKER,
	TWO_SIDED_DEALER
}

// Interfaces:
interface System {
	hosts: Host[];
	users: User[];
}

interface User {
	id: string;
	username: string;
	email: string;
	passwordHash: string;
	role: UserRole;
	isMock: boolean;
}

interface UserGroup {
	id: string;
	name: string;
	role: GroupRole;
	users: User[];
}

interface Host {
	id: string;
	name: string;
	auctionGroups: AuctionGroup[];
	adminGroup: UserGroup;
	auctioneerGroup: UserGroup;
	traderGroups: UserGroup[];
	internalObserverGroup: UserGroup;
	externalObserverGroups: UserGroup[];
	isMock: boolean;
}

interface AuctionGroup {
	id: string;
	name: string;
	auctions: Auction[];
}

interface Auction {
	id: string;
	name: string;
	auctionType: AuctionType;
	traderGroups: UserGroup[];
	externalObserverGroups: UserGroup[];
	isMock: boolean;
}
```

## Class Diagram

```mermaid
classDiagram
    class UserRole {
        <<enumeration>>
        SUPER_USER
        SYSTEM_ADMIN
        HOST_USER
    }

    class GroupRole {
        <<enumeration>>
        ADMIN
        AUCTIONEER
        TRADER
        INTERNAL_OBSERVER
        EXTERNAL_OBSERVER
    }

    class AuctionType {
        <<enumeration>>
        ONE_SIDED
        TWO_SIDED_BROKER
        TWO_SIDED_DEALER
    }

    class System {
        +Host[] hosts
        +User[] users
    }

    class User {
        +string id
        +string username
        +string email
        +string passwordHash
        +UserRole role
        +boolean isMock
    }

    class UserGroup {
        +string id
        +string name
        +GroupRole role
        +User[] users
    }

    class Host {
        +string id
        +string name
        +AuctionGroup[] auctionGroups
        +UserGroup adminGroup
        +UserGroup auctioneerGroup
        +UserGroup[] traderGroups
        +UserGroup internalObserverGroup
        +UserGroup[] externalObserverGroups
        +boolean isMock
    }

    class AuctionGroup {
        +string id
        +string name
        +Auction[] auctions
    }

    class Auction {
        +string id
        +string name
        +AuctionType auctionType
        +UserGroup[] traderGroups
        +UserGroup[] externalObserverGroups
        +boolean isMock
    }

System "1" o-- "0..*" Host: hosts
System "1" o-- "0..*" User: users
Host "1" o-- "0..*" AuctionGroup: auctionGroups
Host "1" o-- "1" UserGroup: adminGroup
Host "1" o-- "1" UserGroup: auctioneerGroup
Host "1" o-- "0..*" UserGroup : traderGroups
Host "1" o-- "1" UserGroup: internalObserverGroup
Host "1" o-- "0..*" UserGroup: externalObserverGroups
UserGroup "1" o-- "0..*" User: users
AuctionGroup "1" o-- "0..*" Auction: auctions
Auction "1" o-- "0..*" UserGroup: traderGroups
Auction "1" o-- "0..*" UserGroup: externalObservers
Auction "1" --> "1" AuctionType: auctionType
User "1" --> "1" UserRole: role
UserGroup "1" --> "1" GroupRole: role
```

## Entities and Relationships

### Core Entities

- **System**: Contains multiple Host and User instances.
- **Host**: Represents a company or entity managing auctions. Contains AuctionGroups and UserGroups.
- **UserGroup**: Contains multiple User instances with a specific GroupRole.
- **AuctionGroup**: Contains multiple Auction instances.
- **Auction**: Represents an individual auction event.
- **User**: Represents a user in the system.

## Roles and Permissions

### User Roles

1. **SUPER_USER** and **SYSTEM_ADMIN**:
    - Can create and edit Hosts.
    - Manage system-wide settings and access logs.
    - Have visibility of all Users and Auctions across all Hosts.
    - Can only appear in adminGroup (GroupRole=ADMIN).
    - SYSTEM_ADMIN may have restricted access to financially sensitive information in the future.
    - Can set a Host to isMock
    - Can create and manage mock users for the host.

2. **HOST_USER**:
    - Can belong to multiple UserGroups in the same or different Hosts.
    - Restricted to one GroupRole per Host.
    - Excluded from UserGroups with GroupRole of ADMIN.
    - Visibility limited to assigned UserGroups and GroupRoles.

### Group Roles

1. **ADMIN**:
    - Reserved for SUPER_USER and SYSTEM_ADMIN roles.
    - Excluded from other GroupRoles within the Host.

2. **AUCTIONEER**:
    - Can create and manage all UserGroups except ADMIN.
    - Can invite users to join all UserGroups except ADMIN.
    - Can create and manage Auctions.
    - Restricted from viewing users outside their Host.

3. **TRADER**:
    - Participates in auctions without visibility into other traders.
    - Can belong to multiple traderGroups within the same Host.
    - Restricted to one traderGroup per Auction.

4. **INTERNAL_OBSERVER**:
    - View-only access similar to AUCTIONEER.
    - Cannot create, edit, or delete UserGroups, AuctionGroups, or Auctions.

5. **EXTERNAL_OBSERVER**:
    - View-only access with restrictions on certain Auction information.
    - Cannot see Trader identities.

## Mock Objects and Test Environment

The system supports mock objects (Users, Auctions, and Hosts) for testing, demonstrations, and
training.

### Mock Object Properties

- User, Auction, and Host entities include an `isMock` boolean property.
- Mock objects coexist with real objects, maintaining the same access controls but with more
  flexible authentication for mock users.

### Creation and Management of Mock Objects

1. **Host-Level Mock Management**:
    - If a Host has `isMock` set to true, all HOST_USER User types can be mocked.
    - For non-mock Hosts, only TRADER and EXTERNAL_OBSERVER user types can be mocked.
        - as the auctioneer can manage those groups on a per-auction basis.

2. **Auction-Level Mock Management**:
    - Auctions containing any mock users must have `isMock` set to true when created (immutable).

3. **Mock User Creation**:
    - SYSTEM_ADMIN and SUPER_USER roles can create and edit mock users based on templates.
    - Naming convention: `<host prefix>-<group>-<group-number><group member letter>`
        - where `<group>` can be, for example: 't' for TRADER, 'eo' for EXTERNAL_OBSERVER
        - for GroupRoles that only have a single UserGroup, `<group>` can be omitted.
        - as discussed these Hosts would have to have isMock set to true:
    - Example: Host "Boardwalk" with prefix "BE" and 3 mock trading groups (2 traders each):
        - Users: BE-t1a, BE-t1b, BE-t2a, BE-t2b, BE-t3a, BE-t3b
        - Groups: BE-t1, BE-t2, BE-t3
        - and for Auctioneers: BE-a1, BE-a2, etc.

4. **Mock Auction Naming**:
    - Prefix "MOCK AUCTION " for all mock auctions.

### Constraints and Rules

1. Non-mock Hosts allow only mock TRADER and EXTERNAL_OBSERVER users.
2. Mock Hosts cannot have non-mock Auctions (I think, not sure about tests).
3. UserGroups with mock users can only associate with mock Auctions.
4. Mock users cannot have email usernames and must follow the specified naming convention.

## Data Privacy and Isolation

- Strict data isolation per Host.
- Users see only information pertinent to their assigned groups and roles within their Host.
- AUCTIONEER and other roles cannot access information about users or Hosts outside their assigned
  HostGroups.

## Access Control

- Enforced through Role-Based Access Control (RBAC) based on UserRole and GroupRole.
- Backend ensures consistent permission management.
- Each role is associated with specific permissions governing actions like creating Hosts, managing
  Auctions, and viewing user information.

## User Management

### User Movement

- Users can transition between different real-world companies within the same Host.
- Visibility of auctions depends on the current GroupRole.
- Historical visibility mimics real-world behavior but may be revised in the future.

### User Transfers Between Companies

- Remove the user from all UserGroups of the former company.
- Add the user to relevant UserGroups of the new company.
- Manage ongoing auction participation appropriately.

### Invitation Workflow

1. AUCTIONEER roles send invitations via email. SUPER_USER and SYSTEM_ADMIN can also send
   invitations.
2. Invited users accept and are assigned to appropriate UserGroups.
3. New accounts are created upon acceptance if the user doesn't exist.
4. Users are added to traderGroups without AUCTIONEER knowing existing system users.

## Auction Management

### Auction Types

- ONE_SIDED
- TWO_SIDED_BROKER
- TWO_SIDED_DEALER

### Credit Management

- CreditAccounts should be scoped per UserGroup to handle multiple concurrent auctions.
- Implement checks to prevent overstepping credit limits during trading operations.
- Different auction types have specific credit and budget constraints:
    1. One-to-many: Dollar constraints (budgets, credit) and volume limits.
    2. Many-to-many (Broker mode): Individual and aggregate limits for trading companies.
    3. Double-sided Dealer mode: Auctioneer has budget and credit limits with each trading group.

## User Interface Considerations

- Provide clear visual indicators to distinguish mock objects from real ones.
- Ensure users are aware when participating in a mock auction.

## Pending Considerations

1. Visibility of past auctions from previous HostGroups for users.
2. Management of test users across Hosts.

## Future Enhancements

- Configurable visibility settings for past auctions.
- Enhanced test environment controls.
- User interface improvements for distinguishing between Hosts and Groups.
- Audit and logging for critical operations.

This document provides a comprehensive overview of the Auction Management System, including its
domain model, roles and permissions, mock object management, and key constraints. The information is
organized for clarity while maintaining all important details and constraints.
