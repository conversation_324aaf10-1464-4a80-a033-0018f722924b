```typescript
// Enums

export enum Activity {
  HIGH,
  MED,
  LOW
}

export enum AuctionDesign {
  MULTI_ROUND,
  DOUBLE_DUTCH,
  ROF<PERSON>
}

export enum MessageType {
  AUCTIONEER_TO_TRADER,
  TRADER_TO_AUCTIONEER,
  AUCTIONEER_TO_ALL
}

export enum MRAuctioneerPageFixedBlotter {
  ROUNDPRICE,
  TOTALVOL,
  ACTIVITY,
  BIDDERS
}

export enum MRAwardPageFixedRow {
  ROUND_PRICE,
  TOTALVOL,
  BIDDERS
}

export enum MRClockLabel {
  PRICE,
  TERM
}

export enum MRCommand {
  GO,
  PAUSE,
  RESTART_ROUND,
  END_ROUND,
  NEXT_ROUND
}

export enum MRState {
  ROUND_INIT,
  ROUND_PRICE_SET,
  ROUND_CLOCK_RUNNING,
  ROUND_CLOCK_PAUSED,
  ROUND_CLOSED,
  AWARDING_AUCTION,
  AUCTION_CLOSED
}

export enum MRStopMode {
  LT,
  LE,
  ZERO
}

export enum MRVisibility {
  ALL,
  FIRST_ROUND,
  ELIGIB<PERSON>ITY
}

export enum Operator {
  GT,
  GE
}

export enum PriceDirection {
  UP,
  DOWN
}

export enum Role {
  ADMIN,
  AUCTIONEER,
  TRADER,
  INTERNAL_OBSERVER
}

export enum SessionOfflineReason {
  LOGGED_OFF,
  PAGE_UNLOADED,
  HIGH_LATENCY,
  SESSION_STALE,
  FORCED_OFF,
  SWEPT,
  RE_LOGGED_IN
}

export enum TradeType {
  BUY,
  SELL
}

// Interfaces

export interface Auction {
  auctionName: string;
  city: string;
  closed: boolean;
  hidden: boolean;
  link: string;
  logEntries: LogEntry[];
  messages: Message[];
  notice: string;
}

export interface LogEntry {
  sessionId: number;
  text: string;
  timestamp: Date;
  username: string;
}

export interface LoginEntry {
  browserAgent: string;
  ipAddress: string;
  loginTime: Date;
  logoutTime: Date;
}

export interface Message {
  body: string;
  messageType: MessageType;
  recipient: Person;
  sender: Person;
  timestamp: Date;
}

export interface MRAuction {
  auctionName: string;
  awardedRound: MRRound;
  city: string;
  closed: boolean;
  hidden: boolean;
  link: string;
  logEntries: LogEntry[];
  messages: Message[];
  observers: Person[];
  notice: string;
  rounds: MRRound[];
  scheduledStart: Date;
  settings: MRSettings;
  state: MRState;
  timeRemaining: number;
  traders: MRTrader[];
}

export interface MRBid {
  awardedVolume: number;
  hasBid: boolean;
  round: MRRound;
  timestamp: Date;
  trader: MRTrader;
  volume: number;
}

export interface MRPriceRule {
  highChange: number;
  highLabel: string;
  highLimit: number;
  highOperator: string;
  lowChange: number;
  lowLabel: string;
  medChange: number;
  medLabel: string;
  medLimit: number;
  medOperator: string;
}

export interface MRRound {
  activity: string;
  auction: MRAuction;
  bids: MRBid[];
  price: number;
  roundNum: number;
  started: boolean;
}

export interface MRSettings {
  clockLabel: MRClockLabel;
  direction: string;
  followingRoundDuration: number;
  initialEligibility: number;
  initialRoundDuration: number;
  minimumVolume: number;
  priceDecimalPlaces: number;
  priceLabel: string;
  priceRule: MRPriceRule;
  stopMode: MRStopMode;
  stopVolume: number;
  volumeDecrement: number;
  volumeLabel: string;
  visibility: MRVisibility;
}

export interface MRTemplate {
  clockLabel: MRClockLabel;
  description: string;
  direction: string;
  followingRoundDuration: number;
  highChange: number;
  highLabel: string;
  highLimit: number;
  highOperator: string;
  initialEligibility: number;
  initialRoundDuration: number;
  lowChange: number;
  lowLabel: string;
  medChange: number;
  medLabel: string;
  medLimit: number;
  medOperator: string;
  minimumVolume: number;
  priceDecimalPlaces: number;
  priceLabel: string;
  stopMode: string;
  stopVolume: number;
  volumeDecrement: number;
  volumeLabel: string;
  visibility: string;
}

export interface MRTrader {
  bids: MRBid[];
  creditLimit: number;
  eligibility: number;
  has_seen_auction: boolean;
  initialUsername: string;
  isBlinded: boolean;
  user: Person;
}

export interface PageView {
  auctionId: number;
  pageName: string;
  timestamp: Date;
}

export interface Person {
  company: string;
  email: string;
  firstname: string;
  im: string;
  lastname: string;
  mobile: string;
  password: string;
  role: Role;
  username: string;
  work: string;
}

export interface Site {
  activeSessions: UserSession[];
}

export interface SystemMessage {
  formattedTimestamp: string;
  text: string;
  timestamp: Date;
}

export interface UserSession {
  isListeningToSessionMessages: boolean;
  lastPing: number;
  offlineReason: SessionOfflineReason;
  offlineTimestamp: Date;
  onlineTimestamp: Date;
  pageViews: PageView[];
  remoteAddress: string;
  sessionId: string;
  user: Person;
  userAgent: string;
}
```
