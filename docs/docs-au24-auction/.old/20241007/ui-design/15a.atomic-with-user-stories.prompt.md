We are designing a vue3 application.
The application uses: Vue3 (script setup lang=ts), NaiveUI widgets, Pinia, Vueuse eventbus.
Note: always use script setup lang=ts
Note for props my preference is to specify the type, whether it's required, and defaults, one by
one, property by property.

The current requirement is to figure out the Molecules and Organisms (of Atomic Design Principles)
Note: The Atoms are the NaiveUI components.
We especially want to figure out reusable (Base) Molecules.

This is an unusual application is that it has NO backend and has NO request/response semantics.
Instead it's fully reactive, and client-side only.

This ui consists of:
- a) vue3 components (Pages, Templates, Organisms, Molecules) build on NaiveUI Atoms.
- b) Pinia stores: remoteStore and localStore
- c) A 'black box' component that exposes functions
- b) Vueuse event buses: blackBoxBus, localBus

The UI works like this:
- black box can:
    - emit events on bus
    - mutate remote pinia store state
- vue3 components can:
    - listen for black box or other eventbus events
    - react to pinia remoteStore state but cannot mutate it
    - react to and mutate pinia local store state
    - can communicate with each other using:
        - props
        - events
        - expose
        - provide/inject
        - local event bus
        - Pinia localStore
- NB: the Pinia remoteStore can only be mutated by the black box, not by any vue component.

We will present:

(1) domain model
(2) backend functions: these are exposed as black box functions
- how the black box works is out of scope
- for the purposes of designing the user interface we are not concerned with how, or when, the 
  blackbox interacts with the backend, if at all. That's why we call it a black box
- NB: what happens inside a black box function is only provided for reference.
(3) User interface User Stories
- these are user stories that are revelant for the functionality of the user interface only
- ie: apart from calling black box functions the user stories, what happens next is not important
- user interface components react to blackbox events and remoteStore state
- how that state changes or why is not of concern to the UI, it's reactive, no proactive
- so we don't think in terms of a user flow, but rather in terms of user interface components 
  that react to the pinia stores, and the eventbuses, and call the black functions as needed.
(4) proposed ui components
- these are some thoughts about the UI components
- just for reference.

We will we then create an Atomic Design document based on NaiveUI widgets as the Atoms.

Your tasks:
- Firstly: for each group of user stories in section (3) below list the Vue component Molecules 
and Organisms that will be needed. 
- Secondly: once you've done that for every group of User Stories in section (3), list the 
Templates and Pages that will be needed.

## (1) Domain Model

### Entities and relationship Model

```typescript
enum UserRole {
	ADMIN_USER = "ADMIN_USER",
	HOST_USER = "HOST_USER"
}

enum GroupRole {
	ADMIN = "ADMIN",
	AUCTIONEER = "AUCTIONEER",
	TRADER = "TRADER",
	INTERNAL_OBSERVER = "INTERNAL_OBSERVER",
	EXTERNAL_OBSERVER = "EXTERNAL_OBSERVER"
}

enum AuctionType {
	ONE_SIDED = "ONE_SIDED",
	TWO_SIDED_BROKER = "TWO_SIDED_BROKER",
	TWO_SIDED_DEALER = "TWO_SIDED_DEALER"
}

enum Side {
	BUY = "BUY",
	SELL = "SELL"
}

enum AuctionState {
	CREATED = "CREATED",
	ACTIVE = "ACTIVE",
	COMPLETED = "COMPLETED",
	CANCELLED = "CANCELLED",
// Add other relevant states
}

interface FinancialLimits {
	creditLimit: number;
	budget: number;
	usedCredit: number;
	usedBudget: number;
}

interface GroupedCounterpartyLimits {
	totalLimits: FinancialLimits;
	counterpartyLimits: {
		counterparty: UserGroup<GroupRole.TRADER>,
		limits: FinancialLimits
	}[];
}

interface System {
	hosts: Host[];
	users: User[];
}

interface User {
	readonly id: string;
	username: string;
	email: string;
	passwordHash: string;
	role: UserRole;
	isMock: boolean;
}

interface UserGroup<T extends GroupRole = GroupRole> {
	readonly id: string;
	name: string;
	role: T;
	users: User[];
}

interface Host {
	readonly id: string;
	name: string;
	auctions: Auction[];
	adminGroup: UserGroup<GroupRole.ADMIN>;
	auctioneerGroup: UserGroup<GroupRole.AUCTIONEER>;
	traderGroups: UserGroup<GroupRole.TRADER>[];
	internalObserverGroup: UserGroup<GroupRole.INTERNAL_OBSERVER>;
	externalObserverGroups: UserGroup<GroupRole.EXTERNAL_OBSERVER>[];
	isMock: boolean;
	auctioneerLimitPool: GroupedCounterpartyLimits;
	traderLimitPoolTable: {
		tradingGroup: UserGroup<GroupRole.TRADER>,
		limits: GroupedCounterpartyLimits
	}[];
}

interface Auction {
	readonly id: string;
	name: string;
	isMock: boolean;
	tags: Set<string>;
	currentState: AuctionState;
	createdBy: User;
	externalObserverGroups: UserGroup<GroupRole.EXTERNAL_OBSERVER>[];
	settings: AuctionSettings;
	rounds: Round[];
	traders: AuctionTradingGroup[];
	auctioneerLimits: GroupedCounterpartyLimits; // used in Broker mode:
}

interface AuctionSettings {
	auctionType: AuctionType;
	defaultInitialMaxBuyQuantity: number;
	defaultInitialMaxSellQuantity: number;
	valueAdjustmentFactor: number;
}

interface AuctionTradingGroup {
	tradingGroup: UserGroup<GroupRole.TRADER>;
	counterpartyLimits: GroupedCounterpartyLimits; // used in broker mode
	isLoggedOn: boolean;
}

interface Round {
	number: number;
	price: number;
	traderActivities: RoundTraderActivity[];
}

interface RoundTraderActivity {
	tradingGroup: UserGroup<GroupRole.TRADER>;
	side: Side;
	quantity: number;
	quantityConstraints: QuantityConstraints
}

interface QuantityConstraints {
	creditLimit: number; // Should be >= 0
	budget: number; // Should be >= 0
	usedCredit: number; // Should be >= 0 and <= creditLimit
	usedBudget: number; // Should be >= 0 and <= budget
}
```


### Core Entities

- **System**: Contains multiple Host and User instances.
- **Host**: Represents a company or entity managing auctions. Contains AuctionGroups and UserGroups.
- **UserGroup**: Contains multiple User instances with a specific GroupRole.
- **AuctionGroup**: Contains multiple Auction instances.
- **Auction**: Represents an individual auction event.
- **User**: Represents a user in the system.

### Roles and Permissions

#### User Roles

1. **SUPER_USER** and **SYSTEM_ADMIN**:
    - Can create and edit Hosts.
    - Manage system-wide settings and access logs.
    - Have visibility of all Users and Auctions across all Hosts.
    - Can only appear in adminGroup (GroupRole=ADMIN).
    - SYSTEM_ADMIN may have restricted access to financially sensitive information in the future.
    - Can set a Host to isMock
    - Can create and manage mock users for the host.

2. **HOST_USER**:
    - Can belong to multiple UserGroups in the same or different Hosts.
    - Restricted to one GroupRole per Host.
    - Excluded from UserGroups with GroupRole of ADMIN.
    - Visibility limited to assigned UserGroups and GroupRoles.

#### Group Roles

1. **ADMIN**:
    - Reserved for SUPER_USER and SYSTEM_ADMIN roles.
    - Excluded from other GroupRoles within the Host.

2. **AUCTIONEER**:
    - Can create and manage all UserGroups except ADMIN.
    - Can invite users to join all UserGroups except ADMIN.
    - Can create and manage Auctions.
    - Restricted from viewing users outside their Host.

3. **TRADER**:
    - Participates in auctions without visibility into other traders.
    - Can belong to multiple traderGroups within the same Host.
    - Restricted to one traderGroup per Auction.

4. **INTERNAL_OBSERVER**:
    - View-only access similar to AUCTIONEER.
    - Cannot create, edit, or delete UserGroups, AuctionGroups, or Auctions.

5. **EXTERNAL_OBSERVER**:
    - View-only access with restrictions on certain Auction information.
    - Cannot see Trader identities.

### Mock Objects and Test Environment

The system supports mock objects (Users, Auctions, and Hosts) for testing, demonstrations, and
training.

#### Mock Object Properties

- User, Auction, and Host entities include an isMock boolean property.
- Mock objects coexist with real objects, maintaining the same access controls but with more
  flexible authentication for mock users.

#### Creation and Management of Mock Objects

1. **Host-Level Mock Management**:
    - If a Host has isMock set to true, all HOST_USER User types can be mocked.
    - For non-mock Hosts, only TRADER and EXTERNAL_OBSERVER user types can be mocked.
        - as the auctioneer can manage those groups on a per-auction basis.

2. **Auction-Level Mock Management**:
    - Auctions containing any mock users must have isMock set to true when created (immutable).

3. **Mock User Creation**:
    - SYSTEM_ADMIN and SUPER_USER roles can create and edit mock users based on templates.
    - Naming convention: <host prefix>-<group>-<group-number><group member letter>
        - where <group> can be, for example: 't' for TRADER, 'eo' for EXTERNAL_OBSERVER
        - for GroupRoles that only have a single UserGroup, <group> can be omitted.
        - as discussed these Hosts would have to have isMock set to true:
    - Example: Host "Boardwalk" with prefix "BE" and 3 mock trading groups (2 traders each):
        - Users: BE-t1a, BE-t1b, BE-t2a, BE-t2b, BE-t3a, BE-t3b
        - Groups: BE-t1, BE-t2, BE-t3
        - and for Auctioneers: BE-a1, BE-a2, etc.

4. **Mock Auction Naming**:
    - Prefix "MOCK AUCTION " for all mock auctions.

### Constraints and Rules

1. Non-mock Hosts allow only mock TRADER and EXTERNAL_OBSERVER users.
2. Mock Hosts cannot have non-mock Auctions (I think, not sure about tests).
3. UserGroups with mock users can only associate with mock Auctions.
4. Mock users cannot have email usernames and must follow the specified naming convention.

### Data Privacy and Isolation

- Strict data isolation per Host.
- Users see only information pertinent to their assigned groups and roles within their Host.
- AUCTIONEER and other roles cannot access information about users or Hosts outside their assigned
  HostGroups.

### Access Control

- Enforced through Role-Based Access Control (RBAC) based on UserRole and GroupRole.
- Backend ensures consistent permission management.
- Each role is associated with specific permissions governing actions like creating Hosts, managing
  Auctions, and viewing user information.

### User Management

#### User Movement

- Users can transition between different real-world companies within the same Host.
- Visibility of auctions depends on the current GroupRole.
- Historical visibility mimics real-world behavior but may be revised in the future.

#### User Transfers Between Companies

- Remove the user from all UserGroups of the former company.
- Add the user to relevant UserGroups of the new company.
- Manage ongoing auction participation appropriately.

#### User Invitation Workflow

1. AUCTIONEER roles send invitations via email. SUPER_USER and SYSTEM_ADMIN can also send
   invitations.
2. Invited users accept and are assigned to appropriate UserGroups.
3. New accounts are created upon acceptance if the user doesn't exist.
4. Users are added to traderGroups without AUCTIONEER knowing existing system users.

### Auction Management

#### Auction Types

- ONE_SIDED
- TWO_SIDED_BROKER
- TWO_SIDED_DEALER

These will be discussed below.

### Credit Management

- CreditAccounts should be scoped per UserGroup to handle multiple concurrent auctions.
- Implement checks to prevent overstepping credit limits during trading operations.
- Different auction types have specific credit and budget constraints:
    1. One-to-many: Dollar constraints (budgets, credit) and volume limits.
    2. Many-to-many (Broker mode): Individual and aggregate limits for trading companies.
    3. Double-sided Dealer mode: Auctioneer has budget and credit limits with each trading group.

### User Interface Considerations

- Provide clear visual indicators to distinguish mock objects from real ones.
- Ensure users are aware when participating in a mock auction.

### Pending Considerations

1. Visibility of past auctions from previous HostGroups for users.
2. Management of test users across Hosts.

### Future Enhancements

- Configurable visibility settings for past auctions.
- Enhanced test environment controls.
- User interface improvements for distinguishing between Hosts and Groups.
- Audit and logging for critical operations.

This document provides a comprehensive overview of the Auction Management System, including its
domain model, roles and permissions, mock object management, and key constraints. The information is
organized for clarity while maintaining all important details and constraints.

## (2) Backend functions

Typically we group creation and editing of objects into one backend function, which allows us to
centralize the backend validation, and also allows use to use the same frontend form to
create and edit the object. ie: in terms of CRUD, we combine Create and Update.

For example, assume we have a User object with properties: username, role and password. Then we
would have two backend functions UserSave, and UserDelete. The UserSave function would have an
isCreate boolean property and would know whether the request is to create a new user or edit the
existing User object. Note: we name these backend functions as Entity + action, eg: UserSave,
UserDelete

Note also that this is push based system, there is no need to issue Get or Read commands, the
backend knows what the user needs to see.

### **1. System Management Functions**

Note: currently we don't have any defined System Management requirements

#### **SystemSave**

- **Description**: Create or update system-wide settings, including global configurations and
  system-level parameters.
- **Purpose**: Allows **SUPER_USER** and **SYSTEM_ADMIN** roles to manage overall system
  configurations, ensuring the platform operates according to organizational policies.

#### **SystemDelete**

- **Description**: Reset or remove system-wide settings if necessary.
- **Purpose**: Enables administrators to clean up obsolete configurations, though typically used
  cautiously to maintain system integrity.

### **2. Host Management Functions**

#### **HostSave**

- **Description**: Create or update a Host, including its properties and associated UserGroups. This
  includes setting the isMock property.
- **Purpose**: Allows **SUPER_USER** and **SYSTEM_ADMIN** roles to manage Hosts, facilitating the
  organization of auctions and users within the system.

#### **HostSetMock**

- **Description**: Set or update the isMock status of a Host independently.
- **Purpose**: Allows administrators to enable or disable mock environments for a Host separately
  from other Host settings, providing flexibility in testing and training scenarios.

### **3. User Management Functions**

Note: typically the SuperUser or SystemAdmin would add users to the auctioneer role, and then
that user would be able to issue User invitations, and associate them with the relevant
UserGroup in the Host. We need to decide if this is done from a Host Page or a User Page.

#### **UserSave**

- **Description**: Create or update a user. Handles both creation and editing based on an isCreate
  flag.
- **Purpose**: Manages user accounts, including mock users, ensuring appropriate roles and
  permissions are assigned.

#### **UserDelete**

- **Description**: Delete a user from the system.
- **Purpose**: Allows for the removal of users when necessary, maintaining security and compliance.

#### **UserInvite**

- **Description**: Send an invitation to a new user to join the platform.
- **Purpose**: Facilitates the onboarding of new users by **AUCTIONEER**, **SUPER_USER**, and *
  *SYSTEM_ADMIN** roles.

#### **UserAcceptInvitation**

- **Description**: Accept an invitation and create or activate a user account.
- **Purpose**: Enables invited users to join the platform securely and be placed into the correct
  UserGroups.

#### **UserTransfer**

Note: Unclear if this is needed!
Once a user is added to a Host it's unclear what changing roles means?
ie: if they were a trader and now change to an Auctioneer, for example if they switched companies,
then it's unclear what happens? Presumably they lose the ability to see the host from the
perspective of the old role (and in some cases that means there are no users in a trading group
in which they previously traded!).
So: if we allow users to transfer (and probably we should), then we need to track trader and
user groups in completed auctions in a immutable way, and probably too, we should have some way
of indicating that that trader is no longer a trader on this host and/or that their role has
changed (and they no longer have access to that auction).

- **Description**: Transfer a user between different UserGroups or Hosts, managing their roles and
  permissions accordingly.
- **Purpose**: Handles the movement of users between companies or roles, updating group memberships
  and access as needed.

#### **UserLogin**

- **Description**: Authenticate a user and establish a session.
- **Purpose**: Allows users to log into the system securely.

#### **UserLogoff**

- **Description**: Terminate a user's session.
- **Purpose**: Enables users to log off from the system, ensuring account security.

### **4. UserGroup Management Functions**

#### **UserGroupSave**

- **Description**: Create or update a UserGroup, including its name, role, and associated users.
- **Purpose**: Manages the grouping of users with specific roles within a Host, crucial for access
  control and permissions.
- Note: adding an removing users to and from trading groups, needs to be considered as above
  regarding changing roles of users, how do we indicate that users are different.
- One possible solution is that we create a separate user object, with the same username, mark
  the first as inactive, or transfered, and then create some property of User eg: "transferred from"
    - or transferred to, or both.
    - Note: UserGroups themselves can never change roles, ever, nor can they be transferred to
      other hosts.
    - this is just an issue if we allow a human to switch companies, and hence either switch user
      groups, while maintaining their same username / email address

#### **UserGroupDelete**

- **Description**: Delete a UserGroup from the system.
- **Purpose**: Allows for the cleanup of obsolete or unused groups, maintaining system integrity.

#### **UserGroupAddUser**

- **Description**: Add a user to a UserGroup.
- **Purpose**: Manages user memberships in groups, essential for role assignments and permissions.

#### **UserGroupRemoveUser**

- **Description**: Remove a user from a UserGroup.
- **Purpose**: Updates group memberships when users change roles or companies.

### **5. Auction Management Functions**

#### **AuctionSaveOneSided**

- **Description**: Create or update a one-sided auction with specified settings.
- **Purpose**: Allows **AUCTIONEER** roles to set up and modify one-sided auctions, including mock
  auctions.

#### **AuctionSaveDoubleSided**

- **Description**: Create or update a double-sided auction with specified settings.
- **Purpose**: Enables the creation and modification of two-sided auctions, including setting
  auction type and value adjustment factors.

#### **AuctionDelete**

- **Description**: Delete an auction (if not started).
- **Purpose**: Allows administrators to remove auctions that are no longer needed before they start.

#### **AuctionSetPrice**

- **Description**: Set or update the starting price of an auction.
- **Purpose**: Manages the initial price setting, crucial for clock auctions.

#### **AuctionSetConstraints**

- **Description**: Create or change bidder quantity and financial constraints.
- **Purpose**: Ensures compliance with auction rules and prevents irrational bidding.

#### **AuctionChangeState**

- **Description**: Change the state of the auction.
- **Actions**:
    - **START**: Starts the auction or the current round.
    - **PAUSE**: Pauses the auction or the current round.
    - **UNPAUSE**: Resumes the auction or the current round.
    - **END_ROUND**: Ends the current round.
    - **NEXT_ROUND**: Advances to the next round, implementing price movement logic, including price
      reversal if applicable.
    - **END_AUCTION**: Ends the auction.
- **Purpose**: Controls the flow of the auction rounds and overall auction lifecycle.

#### **AuctionAddTradingGroup**

- **Description**: Add a trading group to an auction.
- **Purpose**: Manages participants in an auction, essential for setting up trading relationships.

#### **AuctionRemoveTradingGroup**

- **Description**: Remove a trading group from an auction.
- **Purpose**: Updates the list of participants as needed.

#### **AuctionSetTradingGroups**

- **Description**: Set the list of trading groups participating in an auction.
- **Purpose**: Allows for bulk addition or removal of trading groups, useful when managing multiple
  groups at once.

#### **AuctionCalculateAward**

- **Description**: Calculate the auction results based on submitted orders.
- **Purpose**: Determines the market-clearing price and allocations.

#### **AuctionAward**

- **Description**: Finalize the auction results and notify participants.
- **Purpose**: Completes the auction process, initiating settlement procedures.

### **6. Trader Activity Management Functions**

#### **TraderSubmitOrder**

- **Description**: Submit or update a buy or sell order for a round.
- **Purpose**: Allows traders to participate in auctions, with backend validation against auction
  rules.

#### **TraderCancelOrder**

- **Description**: Cancel a previously submitted order before the round ends.
- **Purpose**: Provides flexibility for traders to adjust their participation, ensuring their orders
  reflect current intentions.

### **7. Financial Limit Management Functions**

Unclear if we need separate functions for Traders and Auctioneers

#### **TraderFinancialLimitsSave**

- **Description**: Set or update financial limits (credit limits and budgets) for a trader or group.
- **Purpose**: Manages credit and budget constraints, essential for two-sided auctions.

#### **TraderFinancialLimitsDelete**

- **Description**: Remove or reset financial limits for traders or groups.
- **Purpose**: Allows for the adjustment of financial constraints when they are no longer
  applicable.

#### **AuctioneerLimitAllocate**

- **Description**: Set or update financial limits (credit limits and budgets) for the auctioner
  group.
- **Purpose**: Distributes financial resources to auctions, ensuring they have the necessary limits
  in place.

#### **AuctioneerLimitDeallocate**

- **Description**: Deallocate financial limits from auctions back to auctioneer limits pool when
  auctions conclude.
- **Purpose**: Frees up financial resources, maintaining efficient use of limit pools.

### **8. Validation and Rule Enforcement Functions**

#### **ValidateAuctionSettings**

- **Description**: Ensure that auction settings comply with predefined rules and constraints before
  creation or updates.
- **Purpose**: Centralizes validation logic, preventing invalid auctions from being created or
  modified.

#### **ValidateTraderOrder**

- **Description**: Confirm that submitted trader orders adhere to auction rules, quantity
  constraints, and financial limits.
- **Purpose**: Ensures that all orders are valid and compliant, maintaining the integrity of the
  auction process.

### **9. Audit and Logging Functions**

Unclear what these do?

#### **AuditLogCreate**

- **Description**: Record significant events and actions, such as auction state changes, order
  submissions, and financial limit adjustments.
- **Purpose**: Supports accountability and tracking, facilitating troubleshooting and compliance.

#### **AuditLogRetrieve**

- **Description**: Access and review audit logs for monitoring and compliance purposes.
- **Purpose**: Enables administrators and auditors to examine historical actions within the system.

### **10. Access Control Functions**

Unclear what these do?

#### **AccessControlCheck**

- **Description**: Verify that users have the necessary permissions to perform requested actions
  based on their UserRole and GroupRole.
- **Purpose**: Centralizes permission checks, enhancing security and maintainability.

### **11. Notification Functions**

Unclear what they do?

#### **NotificationSend**

- **Description**: Dispatch notifications to users regarding important events, such as auction
  starts, order confirmations, and award results.
- **Purpose**: Improves user engagement and communication, ensuring users are informed of relevant
  updates.

### **12. Reporting and Analytics Functions**

Need more requirements

#### **ReportGenerate**

- **Description**: Create reports on auction outcomes, user activities, and financial summaries.
- **Purpose**: Provides insights and data for users and administrators, aiding in decision-making
  and performance evaluation.

### **13. System Health Monitoring Functions**

#### **SystemHealthCheck**

- **Description**: Monitor and report on the health and performance of the backend system.
- **Purpose**: Ensures reliability and availability, allowing administrators to proactively address
  issues.

### **Additional Considerations**

- **Data Push Mechanism**:

    - **Backend-Driven Updates**:
        - The backend proactively pushes data to the frontend based on a **PageSet** command.
        - **PageSet Command**:
            - **Description**: Informs the backend of the current page or context the user is
              viewing.
            - **Purpose**: Allows the backend to determine what data the user needs and when to
              update it.
        - **Note**: Specific data fetching functions may be added in the future as requirements
          evolve.

- **Mock Object Handling**:

    - Managed via the isMock property within existing functions.
    - **Note**: Separate functions for mock object management can be added if stricter separation is
      required.

- **Price Reversal in Auctions**:

    - Managed within **AuctionChangeState** and auction parameters.
    - No separate functions needed, as it is integrated into the auction state logic.

- **Access Control Enforcement**:
    - All functions enforce role-based access control, ensuring users can only perform actions
      permitted by their roles.
    - The **AccessControlCheck** function centralizes these permission checks.

- **Auction Rule Validation**:
    - handled by the backend

- **Matching Algorithm logic**:
    - currently impemented by the backend, it may I supposed be neccessary to override calculated
      awards and/or edit bids and have the system reapply the matching algorithm and/or recalculate
      awards

## (3) User Stories only for the User Interface

Below are User stories relating only to the user interface.
- Backend-related stories are out of scope.

**User Stories - User Interface Only**

Note: the domain models roles as follows:

```typescript
type User = {
	// other properties
    userRole: 'ADMIN' | 'HOST_USER'
}
type Host = {
	// other properties
    groups: UserGroup[]
}
type UserGroup = {
	// other properties
    groupRole: 'AUCTIONEER' | 'TRADER' | 'EXTERNAL_OBSERVER' | 'INTERNAL_OBSERVER' | 'ADMIN'
    users: User[]
}
```
in other words:
- a User can only have one of: ADMIN or HOST userRole in our system
    - ADMIN user:
        - can perform system-wide actions
        - can belong only to a host UserGroup with groupRole 'ADMIN'
        - can never be in any group that is not ADMIN grouprole
        - so: an ADMIN User is always an ADMIN GroupRole whether at a system or host level.
    - HOST user:
        - can only have one grouprole per host
            - ie: can be in more than one host group as long as that group always has the same role
        - can be in a different grouprole in another host
        - can never be in any group with ADMIN grouprole
        - cannot perform non-host (ie: system level) actions


From the persective of the user interface, we simplify this and use the groupRole as the 'Who'
in tables below, because a user can only have one grouprole per host,
and only the ADMIN user can have system-wide permissions as well as host permissions.

### **1. System Administration and Host Management**

|     | **Who** | **What**                                                     | **Why**                                                                                              |
|-----|---------|--------------------------------------------------------------|------------------------------------------------------------------------------------------------------|
| 1.1 | ADMIN   | Create, update, and delete Hosts, including setting `isMock` | To manage different auction environments and facilitate testing/training without affecting real data |
| 1.2 | ADMIN   | Configure system-wide settings and access logs               | To ensure the platform operates according to policies and compliance standards                       |
| 1.3 | ADMIN   | Create and manage mock Users and environments                | To enable testing and training without impacting production data                                     |
| 1.4 | ADMIN   | Delete obsolete Users and Hosts                              | To maintain system security and integrity                                                            |


#### **2. User Management**

|     | **Who**           | **What**                                                                                            | **Why**                                                                     |
|-----|-------------------|-----------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------|
| 2.1 | ADMIN             | Create and update user accounts (including mock users), assigning appropriate roles and permissions | So users can participate in the platform effectively.                       |
| 2.2 | ADMIN             | Delete user accounts                                                                                | So users can be removed when necessary to maintain security and compliance. |
| 2.3 | AUCTIONEER, ADMIN | Send invitations to new users via email                                                             | So I can onboard them to the platform.                                      |
| 2.4 | New User          | Accept an invitation and be placed into the correct UserGroups                                      | So I can start participating in auctions.                                   |
| 2.5 | ADMIN             | Transfer a user between different UserGroups or Hosts, managing their roles and permissions         | So users can move between companies or roles seamlessly.                    |
| 2.6 | User              | Log into the system securely                                                                        | So I can access my account and perform my duties.                           |
| 2.7 | User              | Log off from the system                                                                             | So my account remains secure when not in use.                               |
| 2.8 | User              | Update my profile information and change my password                                                | To keep my account up to date and secure.                                   |


### **3. Role and Permission Management**

|     | **Who**           | **What**                                             | **Why**                                                      |
|-----|-------------------|------------------------------------------------------|--------------------------------------------------------------|
| 3.1 | ADMIN             | Manage UserRoles and GroupRoles                      | To ensure appropriate access control                         |
| 3.2 | AUCTIONEER        | Create and manage UserGroups within their Host       | To organize users based on roles and permissions             |
| 3.3 | TRADER            | Belong to multiple traderGroups within the same Host | To participate in various Auctions                           |
| 3.4 | EXTERNAL_OBSERVER | View Auctions without Trader identities              | To monitor market activity while maintaining confidentiality |


### **4. Auction Management**

|     | **Who**    | **What**                                                       | **Why**                                                                    |
|-----|------------|----------------------------------------------------------------|----------------------------------------------------------------------------|
| 4.1 | AUCTIONEER | Create and update one-sided auctions with specific settings    | So I can manage auction events tailored to particular needs.               |
| 4.2 | AUCTIONEER | Create and update double-sided auctions with specific settings | So I can manage more complex auction scenarios involving multiple parties. |
| 4.3 | ADMIN      | Delete auctions that have not yet started                      | So unnecessary or incorrect auctions can be removed before they go live.   |
| 4.4 | AUCTIONEER | Set or update the starting price of an auction                 | So the auction can begin with an appropriate initial price.                |
| 4.5 | AUCTIONEER | Create or change bidder quantity and financial constraints     | So the auction rules are enforced and prevent irrational bidding.          |
| 4.6 | AUCTIONEER | Change the state of an auction (e.g., start, pause, end)       | So I can control the auction lifecycle effectively.                        |
| 4.7 | AUCTIONEER | Add trading groups to an auction                               | So participants can engage in trading activities.                          |
| 4.8 | AUCTIONEER | Remove trading groups from an auction                          | So the list of participants can be updated as needed.                      |
| 4.9 | AUCTIONEER | Set the list of trading groups participating in an auction     | So bulk management of participants is possible.                            |
| 4.10| AUCTIONEER | Calculate auction awards based on submitted orders             | So results are determined accurately and fairly.                           |
| 4.11| AUCTIONEER | Finalize auction results and notify participants               | So the auction process is completed and stakeholders are informed.         |
| 4.12| Trader     | View active Auctions I am participating in                     | So I can submit orders accordingly.                                        |
| 4.13| AUCTIONEER | Create and configure Auctions (one-sided or double-sided)      | So trading events can be facilitated effectively.                          |
| 4.14| AUCTIONEER | Start, pause, resume, and end Auctions and Rounds              | So I can control the flow of trading events.                               |
| 4.15| AUCTIONEER | Set the starting price and price movement logic of an Auction  | So the auction reflects market conditions.                                 |
| 4.16| AUCTIONEER | Add or remove trading groups from an Auction                   | So I can manage participant involvement.                                   |
| 4.17| AUCTIONEER | Calculate and finalize Auction awards                          | So participants receive their allocations appropriately.                   |


### **5. Trader Activities**

|     | **Who** | **What**                                    | **Why**                                |
|-----|---------|---------------------------------------------|----------------------------------------|
| 5.1 | TRADER  | Submit buy or sell orders                   | To participate in trading              |
| 5.2 | TRADER  | Cancel orders before Round ends             | To adjust trading strategy             |
| 5.3 | TRADER  | View financial limits and constraints       | To know trading capacity               |
| 5.4 | TRADER  | Monitor used credit and budget in real-time | To manage remaining limits effectively |
| 5.5 | TRADER  | Receive notifications about Auction events  | To stay informed                       |
| 5.6 | Trader  | View active Auctions I am participating in  | So I can submit orders accordingly.    |


### **6. Financial Limit Management**

|     | **Who**    | **What**                                                                                            | **Why**                                                             |
|-----|------------|-----------------------------------------------------------------------------------------------------|---------------------------------------------------------------------|
| 6.1 | ADMIN      | Set or update financial limits (credit and budget) for traders or groups                            | So trading activities remain within predefined constraints.         |
| 6.2 | ADMIN      | Remove or reset financial limits for traders or groups                                              | So financial constraints can be adjusted when no longer applicable. |
| 6.3 | ADMIN      | Allocate financial limits to the auctioneer group                                                   | So auctions have the necessary resources to operate.                |
| 6.4 | ADMIN      | Deallocate financial limits from auctions back to the auctioneer limits pool when auctions conclude | So resources are efficiently managed.                               |
| 6.5 | AUCTIONEER | Set or update financial limits (credit and budget) for trading groups                               | To enforce credit and budget constraints.                           |
| 6.6 | AUCTIONEER | Manage the auctioneer limit pool to allocate resources appropriately across Auctions                | To ensure auctions have necessary financial resources.              |
| 6.7 | TRADER     | Prevent overstepping credit limits during trading operations                                        | To ensure compliance and maintain financial integrity.              |


### **7. Mock Objects and Testing Environment**

|     | **Who**    | **What**                               | **Why**                                              |
|-----|------------|----------------------------------------|------------------------------------------------------|
| 7.1 | ADMIN      | Create mock Users, Auctions, and Hosts | For testing and training without impacting real data |
| 7.2 | TRADER     | Participate in mock Auctions           | To practice trading risk-free                        |
| 7.3 | AUCTIONEER | Distinguish mock objects in the UI     | To avoid operational confusion                       |


### **8. Notifications and Communications**

|     | **Who**    | **What**                        | **Why**                                  |
|-----|------------|---------------------------------|------------------------------------------|
| 8.1 | USER       | Receive important notifications | To stay informed about relevant events   |
| 8.2 | USER       | Manage notification preferences | To control update frequency and channels |
| 8.3 | AUCTIONEER | Send notifications to Traders   | To communicate critical Auction updates  |


### **9. Audit and Compliance**

|     | **Who** | **What**                          | **Why**                                       |
|-----|---------|-----------------------------------|-----------------------------------------------|
| 9.1 | ADMIN   | Audit and log critical operations | For accountability and compliance purposes    |
| 9.2 | ADMIN   | Retrieve and review audit logs    | To monitor activities and troubleshoot issues |


### **10. Access Control and Security**

|      | **Who** | **What**                               | **Why**                                   |
|------|---------|----------------------------------------|-------------------------------------------|
| 10.1 | ADMIN   | Enforce role-based access control      | To ensure security and proper permissions |
| 10.2 | USER    | Restricted access to unauthorized data | To maintain data privacy and security     |
| 10.3 | USER    | Secure authentication mechanisms       | To protect account integrity              |


### **11. Reporting and Analytics**

|      | **Who**    | **What**                       | **Why**                                           |
|------|------------|--------------------------------|---------------------------------------------------|
| 11.1 | ADMIN      | Generate comprehensive reports | To inform decision-making and analyze performance |
| 11.2 | AUCTIONEER | View analytical dashboards     | To assess Auction performance                     |


### **12. System Health and Monitoring**

|      | **Who** | **What**                               | **Why**                                 |
|------|---------|----------------------------------------|-----------------------------------------|
| 12.1 | ADMIN   | Monitor system health and performance  | To proactively address potential issues |
| 12.2 | ADMIN   | Receive performance degradation alerts | To maintain system reliability          |


### **13. User Interface and Experience**

|      | **Who** | **What**                                   | **Why**                           |
|------|---------|--------------------------------------------|-----------------------------------|
| 13.1 | USER    | Intuitive and responsive UI                | To navigate efficiently           |
| 13.2 | USER    | Clear indicators for mock vs. real objects | To avoid operational confusion    |
| 13.3 | USER    | Accessibility features                     | To ensure usability for all users |


### **14. Future Enhancements and Considerations**

|      | **Who** | **What**                                  | **Why**                                         |
|------|---------|-------------------------------------------|-------------------------------------------------|
| 14.1 | ADMIN   | Configurable visibility for past Auctions | To control long-term data access                |
| 14.2 | USER    | Multi-language support                    | To use the platform in preferred language       |
| 14.3 | ADMIN   | Ensure legal and regulatory compliance    | To meet necessary standards and avoid penalties |
| 14.4 | ADMIN   | Conduct regular security audits           | To maintain platform security                   |



## (4) Thoughts on UI Design

### **Proposed UI Components**

#### **1. Overall Layout**

- **MainLayout Component:**
    - **Includes:** Navbar, SideBar, PageContainer, Footer.
    - **Comments:**
        - **Navbar:** Ensure it includes navigation links relevant to user roles (e.g., Auctioneer,
          Trader).
        - **SideBar:** Should dynamically display menu items based on user roles and permissions.
        - **PageContainer:** Appropriately structured to load different pages based on routing.
        - **Footer:** Can include system information, version, and quick links.

#### **2. Page Components**

##### **a. HomePage**

- **Components:**
    - **SystemDashboard:** Utilizes `n-card`, `n-statistic`, `n-chart`.
- **Comments:**
    - **Supports Functionality:** Yes, provides an overview of system metrics.
    - **Suggestions:**
        - Incorporate `n-tooltip` for additional information on statistics.
        - Use `n-carousel` if dynamic or rotating metrics display is needed.

##### **b. HostPage**

- **Components:**
    - **AuctionGroupPanel:** `n-tabs`, `n-button`.
    - **AuctionGroupList:** `n-table`, `n-pagination`, `n-button`.
    - **AuctionGroupDetail:** `n-form`, `n-input`, `n-select`.
    - **FinancialLimitPoolManager:** `n-form`, `n-input`, `n-select`, `n-button`.
- **Comments:**
    - **Supports Functionality:** Yes, covers management of Auction Groups and Financial Limits.
    - **Suggestions:**
        - Consider adding `n-modal` for creating/editing Auction Groups to enhance UX.
        - Use `n-popconfirm` for delete actions to prevent accidental removals.

##### **c. AuctioneerPage**

- **Components:**
    - **AuctioneerPanel:** `n-tabs`, `n-button`.
    - **AuctioneerList:** `n-table`, `n-pagination`, `n-button`.
    - **AuctioneerDetail:** `n-form`, `n-input`, `n-select`.
- **Comments:**
    - **Supports Functionality:** Yes, facilitates management of Auctioneers.
    - **Suggestions:**
        - Implement `n-tree` if Auctioneers have hierarchical relationships.
        - Utilize `n-tag` for role or status indicators within lists.

##### **d. TraderPage**

- **Components:**
    - **TraderPanel:** `n-tabs`, `n-button`.
    - **TraderList:** `n-table`, `n-pagination`, `n-button`.
    - **TraderDetail:** `n-form`, `n-input`, `n-select`.
- **Comments:**
    - **Supports Functionality:** Yes, allows for comprehensive trader management.
    - **Suggestions:**
        - Integrate `n-avatar` for trader identification in lists.
        - Use `n-collapse` for expandable trader details if necessary.

##### **e. CreditLimitPage**

- **Components:**
    - **FinancialLimitsPanel:** `n-tabs`, `n-button`.
    - **FinancialLimitsForm:** `n-form`, `n-input`, `n-slider`, `n-button`.
    - **FinancialLimitsDisplay:** `n-card`, `n-statistic`.
    - **LimitAllocationDashboard:** `n-card`, `n-statistic`, `n-progress`.
- **Comments:**
    - **Supports Functionality:** Yes, manages financial constraints effectively.
    - **Suggestions:**
        - Utilize `n-tooltip` on sliders to show real-time values.
        - Implement `n-progress` bars to visualize limit utilizations clearly.

##### **f. AuditTrailPage**

- **Components:**
    - **AuditTrailPanel:** `n-table`, `n-pagination`.
    - **AuditTrailViewer:** `n-table`, `n-pagination`, `n-button`.
- **Comments:**
    - **Supports Functionality:** Yes, provides detailed audit logs.
    - **Suggestions:**
        - Add `n-filter` components to allow users to search and filter audit logs.
        - Use `n-modal` to view detailed audit entries without navigating away.

##### **g. NotificationPage**

- **Components:**
    - **NotificationPanel:** `n-alert`, `n-notification`.
    - **NotificationSystem:** `n-alert`, `n-notification`.
- **Comments:**
    - **Supports Functionality:** Partially. While alerts and notifications are covered, managing
      notification settings is unclear.
    - **Suggestions:**
        - Incorporate `n-switch` or `n-checkbox` for users to customize their notification
          preferences.
        - Use `n-dropdown` for filtering notification types.

##### **h. UserManagementPage**

- **Components:**
    - **UserManagementPanel:** `n-table`, `n-form`, `n-button`.
    - **UserList:** `n-table`, `n-pagination`, `n-button`.
    - **UserDetail:** `n-form`, `n-input`, `n-select`.
- **Comments:**
    - **Supports Functionality:** Yes, handles comprehensive user management.
    - **Suggestions:**
        - Implement `n-avatar` and `n-tag` for quick user identification and status.
        - Use `n-tree` if users have hierarchical roles or belong to nested groups.

#### **3. Reusable Components**

- **Chat Component:**
    - **Comments:**
        - Ensure it uses `n-input`, `n-button`, and possibly `n-scrollbar` for message display.
        - Implement real-time communication with WebSockets or similar technologies.

- **Modals and Forms:**
    - **Comments:**
        - Use `n-modal` for creating/editing entities to keep users on the same page.
        - Ensure forms use validation components like `n-form-item` with validation rules.

#### **4. Additional Complex Components and Widgets**

To fully support the backend functionalities and provide a seamless user experience, consider
integrating the following complex components:

- **Auction Management Widgets:**
    - **AuctionDashboard:** Combines multiple `n-card` and `n-chart` components to visualize auction
      metrics.
    - **RoundControlPanel:** Utilizes `n-button-group` and `n-dropdown` for auction state controls (
      start, pause, end round).

- **Financial Visualization:**
    - **LimitAllocationGraph:** Uses `n-chart` to display financial limit allocations dynamically.
    - **CreditUsageTracker:** Incorporates `n-progress` and `n-statistic` to monitor credit and
      budget usage.

- **User Activity Monitoring:**
    - **UserActivityFeed:** Employs `n-list` and `n-avatar` to show real-time user activities and
      actions.

- **Responsive Design:**
    - Ensure all components are responsive using NaiveUI’s grid system (`n-row`, `n-col`) to support
      various screen sizes.

#### **5. Accessibility and User Experience Enhancements**

- **Accessibility:**
    - Use `aria` attributes and ensure keyboard navigation support across all components.
    - Ensure color contrasts meet accessibility standards, especially for `n-chart` and `n-progress`
      components.

- **User Experience:**
    - Implement loading indicators (`n-spin`) for data-fetching operations to enhance user feedback.
    - Utilize `n-tooltip` and `n-popover` to provide additional information without cluttering the
      UI.

#### **6. Potential Missing Components**

- **ReportingPage:**
    - Given the backend includes reporting and analytics functions, a dedicated ReportingPage might
      be beneficial.
    - **Components:**
        - **ReportGenerator:** `n-form`, `n-select`, `n-button`.
        - **ReportDisplay:** `n-table`, `n-chart`.

- **SettingsPage:**
    - For system-wide settings and user preferences.
    - **Components:**
        - **SystemSettingsForm:** `n-form`, `n-input`, `n-select`, `n-switch`.
        - **UserPreferences:** `n-form`, `n-select`, `n-checkbox`.

#### **7. Security Considerations**

- **Access Control Indicators:**
    - Visually indicate restricted sections using `n-tag` or `n-icon` to enhance security awareness.

- **Audit Trail Access:**
    - Ensure only authorized roles can access and interact with AuditTrailPage.

#### **8. Suggested Mermaid Diagram**

Based on the review and suggestions, here's an updated Mermaid diagram arranged left to right,
incorporating the proposed enhancements and ensuring a clear, hierarchical structure:

```mermaid
graph LR
    MainLayout["MainLayout<br/>Components:<br/>Navbar, SideBar, PageContainer, Footer"]
    MainLayout --> HomePage
    MainLayout --> HostPage
    MainLayout --> AuctioneerPage
    MainLayout --> TraderPage
    MainLayout --> UserManagementPage
    MainLayout --> CreditLimitPage
    MainLayout --> AuditTrailPage
    MainLayout --> NotificationPage
    MainLayout --> ReportingPage
    MainLayout --> SettingsPage

    subgraph HomePage
        SystemDashboard["SystemDashboard<br/>Widgets:<br/>n-card, n-statistic, n-chart, n-tooltip"]
    end

    subgraph HostPage
        AuctionGroupPanel["AuctionGroupPanel<br/>Widgets:<br/>n-tabs, n-button, n-modal"]
        AuctionGroupPanel --> AuctionGroupList["AuctionGroupList<br/>Widgets:<br/>n-table, n-pagination, n-button, n-popconfirm"]
        AuctionGroupPanel --> AuctionGroupDetail["AuctionGroupDetail<br/>Widgets:<br/>n-form, n-input, n-select, n-tooltip"]
        AuctionGroupPanel --> FinancialLimitPoolManager["FinancialLimitPoolManager<br/>Widgets:<br/>n-form, n-input, n-select, n-button, n-modal"]
    end

    subgraph AuctioneerPage
        AuctioneerPanel["AuctioneerPanel<br/>Widgets:<br/>n-tabs, n-button, n-button-group"]
        AuctioneerPanel --> AuctioneerList["AuctioneerList<br/>Widgets:<br/>n-table, n-pagination, n-button, n-tag"]
        AuctioneerPanel --> AuctioneerDetail["AuctioneerDetail<br/>Widgets:<br/>n-form, n-input, n-select, n-avatar"]
        AuctioneerPanel --> RoundControlPanel["RoundControlPanel<br/>Widgets:<br/>n-button-group, n-dropdown"]
    end

    subgraph TraderPage
        TraderPanel["TraderPanel<br/>Widgets:<br/>n-tabs, n-button"]
        TraderPanel --> TraderList["TraderList<br/>Widgets:<br/>n-table, n-pagination, n-button, n-avatar"]
        TraderPanel --> TraderDetail["TraderDetail<br/>Widgets:<br/>n-form, n-input, n-select"]
        TraderPanel --> Chat["Chat<br/>Widgets:<br/>n-input, n-button, n-scrollbar"]
    end

    subgraph UserManagementPage
        UserManagementPanel["UserManagementPanel<br/>Widgets:<br/>n-table, n-form, n-button"]
        UserManagementPanel --> UserList["UserList<br/>Widgets:<br/>n-table, n-pagination, n-button, n-avatar, n-tag"]
        UserManagementPanel --> UserDetail["UserDetail<br/>Widgets:<br/>n-form, n-input, n-select, n-tree"]
    end

    subgraph CreditLimitPage
        FinancialLimitsPanel["FinancialLimitsPanel<br/>Widgets:<br/>n-tabs, n-button"]
        FinancialLimitsPanel --> FinancialLimitsForm["FinancialLimitsForm<br/>Widgets:<br/>n-form, n-input, n-slider, n-button, n-tooltip"]
        FinancialLimitsPanel --> FinancialLimitsDisplay["FinancialLimitsDisplay<br/>Widgets:<br/>n-card, n-statistic, n-progress"]
        FinancialLimitsPanel --> LimitAllocationDashboard["LimitAllocationDashboard<br/>Widgets:<br/>n-card, n-statistic, n-progress, n-chart"]
    end

    subgraph AuditTrailPage
        AuditTrailPanel["AuditTrailPanel<br/>Widgets:<br/>n-table, n-pagination, n-filter, n-modal"]
        AuditTrailPanel --> AuditTrailViewer["AuditTrailViewer<br/>Widgets:<br/>n-table, n-pagination, n-button"]
    end

    subgraph NotificationPage
        NotificationPanel["NotificationPanel<br/>Widgets:<br/>n-alert, n-notification, n-switch, n-dropdown"]
        NotificationPanel --> NotificationSystem["NotificationSystem<br/>Widgets:<br/>n-alert, n-notification, n-switch, n-dropdown"]
    end

    subgraph ReportingPage
        ReportGenerator["ReportGenerator<br/>Widgets:<br/>n-form, n-select, n-button"]
        ReportGenerator --> ReportDisplay["ReportDisplay<br/>Widgets:<br/>n-table, n-chart"]
    end

    subgraph SettingsPage
        SystemSettingsForm["SystemSettingsForm<br/>Widgets:<br/>n-form, n-input, n-select, n-switch"]
        SystemSettingsForm --> UserPreferences["UserPreferences<br/>Widgets:<br/>n-form, n-select, n-checkbox"]
    end
```
