Our goal today is determine the backend business logic that needs to be implemented to support and
auction platform we are building.

Below you will find:

(1) The final Domain model we will be using, in typescript.
Potential changes to this model are our scope for the purposes of this discussion. 
We also present the Mermaid diagram of that model, and provide an overview with relevant 
constraints.

(2) A description of our auctions, including with their quantity and monetary value constraints

(3) "Current Proposed Backend Functions: "An initial attempt at listing the backend functions 
needed to support the domain model and other functionality and constraints discused in the domain model and our auctions.

Your goal is to review the backend functions and ensure that we have all the functions we need 
to implement the domain model and our auctions. We don't need detail, just a simple description 
of each function.

NOTES: 

a) NB: This is a design task, NO CODE is to be generated.

b) Typically we group creation and editing of objects into one backend function, which allows us to
centralize the backend validation, and also on the frontend allows use to use the same form to
create and edit the object.

For example, assume we have a User object with properties: username, role and password. Then we
would have two backend functions UserSave, and UserDelete. The UserSave function would have an
isCreate boolean property and would know whether the request is to create a new user or edit the
existing User object. Note: we name these backend functions as Entity + action, eg: UserSave,
UserDelete.


## (1) Domain Model

### Entities and relationship Model


```typescript

enum UserRole {
	SUPER_USER = "SUPER_USER",
	SYSTEM_ADMIN = "SYSTEM_ADMIN",
	HOST_USER = "HOST_USER"
}

enum GroupRole {
	ADMIN = "ADMIN",
	AUCTIONEER = "AUCTIONEER",
	TRADER = "TRADER",
	INTERNAL_OBSERVER = "INTERNAL_OBSERVER",
	EXTERNAL_OBSERVER = "EXTERNAL_OBSERVER"
}

enum AuctionType {
	ONE_SIDED = "ONE_SIDED",
	TWO_SIDED_BROKER = "TWO_SIDED_BROKER",
	TWO_SIDED_DEALER = "TWO_SIDED_DEALER"
}

enum Side {
	BUY = "BUY",
	SELL = "SELL"
}

enum AuctionState {
	CREATED = "CREATED",
	ACTIVE = "ACTIVE",
	COMPLETED = "COMPLETED",
	CANCELLED = "CANCELLED",
	// Add other relevant states
}

interface FinancialLimits {
	creditLimit: number;
	budget: number;
	usedCredit: number;
	usedBudget: number;
}

interface GroupedCounterpartyLimits {
	totalLimits: FinancialLimits;
	counterpartyLimits: {
		counterparty: UserGroup<GroupRole.TRADER>,
		limits: FinancialLimits
	}[];
}

interface System {
	hosts: Host[];
	users: User[];
}

interface User {
	readonly id: string;
	username: string;
	email: string;
	passwordHash: string;
	role: UserRole;
	isMock: boolean;
}

interface UserGroup<T extends GroupRole = GroupRole> {
	readonly id: string;
	name: string;
	role: T;
	users: User[];
}

interface Host {
	readonly id: string;
	name: string;
	auctions: Auction[];
	adminGroup: UserGroup<GroupRole.ADMIN>;
	auctioneerGroup: UserGroup<GroupRole.AUCTIONEER>;
	traderGroups: UserGroup<GroupRole.TRADER>[];
	internalObserverGroup: UserGroup<GroupRole.INTERNAL_OBSERVER>;
	externalObserverGroups: UserGroup<GroupRole.EXTERNAL_OBSERVER>[];
	isMock: boolean;
	auctioneerLimitPool: GroupedCounterpartyLimits;
	traderLimitPoolTable: {
		tradingGroup: UserGroup<GroupRole.TRADER>,
		limits: GroupedCounterpartyLimits
	}[];
}

interface Auction {
	readonly id: string;
	name: string;
	isMock: boolean;
	tags: Set<string>;
	currentState: AuctionState;
	createdBy: User;
	externalObserverGroups: UserGroup<GroupRole.EXTERNAL_OBSERVER>[];
	settings: AuctionSettings;
	rounds: Round[];
	traders: AuctionTradingGroup[];
	auctioneerLimits: GroupedCounterpartyLimits; // used in Broker mode:
}

interface AuctionSettings {
	auctionType: AuctionType;
	defaultInitialMaxBuyQuantity: number;
	defaultInitialMaxSellQuantity: number;
	valueAdjustmentFactor: number;
}

interface AuctionTradingGroup {
	tradingGroup: UserGroup<GroupRole.TRADER>;
	counterpartyLimits: GroupedCounterpartyLimits; // used in broker mode
	isLoggedOn: boolean;
}

interface Round {
	number: number;
	price: number;
	traderActivities: RoundTraderActivity[];
}

interface RoundTraderActivity {
	tradingGroup: UserGroup<GroupRole.TRADER>;
	side: Side;
	quantity: number;
	quantityConstraints: QuantityConstraints
}

interface QuantityConstraints {
	creditLimit: number; // Should be >= 0
	budget: number; // Should be >= 0
	usedCredit: number; // Should be >= 0 and <= creditLimit
	usedBudget: number; // Should be >= 0 and <= budget
}
```


### Class Diagram

```typescript

enum UserRole {
	SUPER_USER = "SUPER_USER",
	SYSTEM_ADMIN = "SYSTEM_ADMIN",
	HOST_USER = "HOST_USER"
}

enum GroupRole {
	ADMIN = "ADMIN",
	AUCTIONEER = "AUCTIONEER",
	TRADER = "TRADER",
	INTERNAL_OBSERVER = "INTERNAL_OBSERVER",
	EXTERNAL_OBSERVER = "EXTERNAL_OBSERVER"
}

enum AuctionType {
	ONE_SIDED = "ONE_SIDED",
	TWO_SIDED_BROKER = "TWO_SIDED_BROKER",
	TWO_SIDED_DEALER = "TWO_SIDED_DEALER"
}

enum Side {
	BUY = "BUY",
	SELL = "SELL"
}

enum AuctionState {
	CREATED = "CREATED",
	ACTIVE = "ACTIVE",
	COMPLETED = "COMPLETED",
	CANCELLED = "CANCELLED",
	// Add other relevant states
}

interface FinancialLimits {
	creditLimit: number;
	budget: number;
	usedCredit: number;
	usedBudget: number;
}

interface GroupedCounterpartyLimits {
	totalLimits: FinancialLimits;
	counterpartyLimits: {
		counterparty: UserGroup<GroupRole.TRADER>,
		limits: FinancialLimits
	}[];
}

interface System {
	hosts: Host[];
	users: User[];
}

interface User {
	readonly id: string;
	username: string;
	email: string;
	passwordHash: string;
	role: UserRole;
	isMock: boolean;
}

interface UserGroup<T extends GroupRole = GroupRole> {
	readonly id: string;
	name: string;
	role: T;
	users: User[];
}

interface Host {
	readonly id: string;
	name: string;
	auctions: Auction[];
	adminGroup: UserGroup<GroupRole.ADMIN>;
	auctioneerGroup: UserGroup<GroupRole.AUCTIONEER>;
	traderGroups: UserGroup<GroupRole.TRADER>[];
	internalObserverGroup: UserGroup<GroupRole.INTERNAL_OBSERVER>;
	externalObserverGroups: UserGroup<GroupRole.EXTERNAL_OBSERVER>[];
	isMock: boolean;
	auctioneerLimitPool: GroupedCounterpartyLimits;
	traderLimitPoolTable: {
		tradingGroup: UserGroup<GroupRole.TRADER>,
		limits: GroupedCounterpartyLimits
	}[];
}

interface Auction {
	readonly id: string;
	name: string;
	isMock: boolean;
	tags: Set<string>;
	currentState: AuctionState;
	createdBy: User;
	externalObserverGroups: UserGroup<GroupRole.EXTERNAL_OBSERVER>[];
	settings: AuctionSettings;
	rounds: Round[];
	traders: AuctionTradingGroup[];
	auctioneerLimits: GroupedCounterpartyLimits; // used in Broker mode:
}

interface AuctionSettings {
	auctionType: AuctionType;
	defaultInitialMaxBuyQuantity: number;
	defaultInitialMaxSellQuantity: number;
	valueAdjustmentFactor: number;
}

interface AuctionTradingGroup {
	tradingGroup: UserGroup<GroupRole.TRADER>;
	counterpartyLimits: GroupedCounterpartyLimits; // used in broker mode
	isLoggedOn: boolean;
}

interface Round {
	number: number;
	price: number;
	traderActivities: RoundTraderActivity[];
}

interface RoundTraderActivity {
	tradingGroup: UserGroup<GroupRole.TRADER>;
	side: Side;
	quantity: number;
	quantityConstraints: QuantityConstraints
}

interface QuantityConstraints {
	creditLimit: number; // Should be >= 0
	budget: number; // Should be >= 0
	usedCredit: number; // Should be >= 0 and <= creditLimit
	usedBudget: number; // Should be >= 0 and <= budget
}
```



```mermaid
classDiagram
    class System {
        +hosts: Host[]
        +users: User[]
    }

    class User {
        +id: string
        +username: string
        +email: string
        +passwordHash: string
        +role: UserRole
        +isMock: boolean
    }

    class UserGroup {
        +id: string
        +name: string
        +role: GroupRole
        +users: User[]
    }

    class Host {
        +id: string
        +name: string
        +auctions: Auction[]
        +adminGroup: UserGroup
        +auctioneerGroup: UserGroup
        +traderGroups: UserGroup[]
        +internalObserverGroup: UserGroup
        +externalObserverGroups: UserGroup[]
        +isMock: boolean
        +auctioneerLimitPool: GroupedCounterpartyLimits
        +traderLimitPoolTable: TraderLimitPool[]
    }

    class Auction {
        +id: string
        +name: string
        +isMock: boolean
        +tags: Set~string~
        +currentState: AuctionState
        +createdBy: User
        +externalObserverGroups: UserGroup[]
        +settings: AuctionSettings
        +rounds: Round[]
        +traders: AuctionTradingGroup[]
        +auctioneerLimits: GroupedCounterpartyLimits
    }

    class AuctionSettings {
        +auctionType: AuctionType
        +defaultInitialMaxBuyQuantity: number
        +defaultInitialMaxSellQuantity: number
        +valueAdjustmentFactor: number
    }

    class AuctionTradingGroup {
        +tradingGroup: UserGroup
        +counterpartyLimits: GroupedCounterpartyLimits
        +isLoggedOn: boolean
    }

    class Round {
        +number: number
        +price: number
        +traderActivities: RoundTraderActivity[]
    }

    class RoundTraderActivity {
        +tradingGroup: UserGroup
        +side: Side
        +quantity: number
        +quantityConstraints: QuantityConstraints
    }

    class GroupedCounterpartyLimits {
        +totalLimits: FinancialLimits
        +counterpartyLimits: CounterPartyLimit[]
    }

    class FinancialLimits {
        +creditLimit: number
        +budget: number
        +usedCredit: number
        +usedBudget: number
    }

    class CounterPartyLimit {
        +counterparty: UserGroup
        +limits: FinancialLimits
    }

    class TraderLimitPool {
        +tradingGroup: UserGroup
        +limits: GroupedCounterpartyLimits
    }

    class QuantityConstraints {
        +creditLimit: number
        +budget: number
        +usedCredit: number
        +usedBudget: number
    }

    System "1" --> "*" Host
    System "1" --> "*" User
    Host "1" --> "*" Auction
    Host "1" --> "*" UserGroup
    Host "1" --> "1" GroupedCounterpartyLimits : auctioneerLimitPool
    Host "1" --> "*" TraderLimitPool : traderLimitPoolTable
    UserGroup "1" --> "*" User
    Auction "1" --> "*" Round
    Auction "1" --> "1" AuctionSettings
    Auction "1" --> "*" AuctionTradingGroup : traders
    Auction "1" --> "1" GroupedCounterpartyLimits : auctioneerLimits
    Round "1" --> "*" RoundTraderActivity
    RoundTraderActivity --> "1" UserGroup : tradingGroup
    RoundTraderActivity --> "1" QuantityConstraints
    GroupedCounterpartyLimits "1" --> "1" FinancialLimits : totalLimits
    GroupedCounterpartyLimits "1" --> "*" CounterPartyLimit : counterpartyLimits
    CounterPartyLimit --> "1" UserGroup : counterparty
    CounterPartyLimit "1" --> "1" FinancialLimits : limits
    TraderLimitPool --> "1" UserGroup : tradingGroup
    TraderLimitPool --> "1" GroupedCounterpartyLimits : limits
    AuctionTradingGroup --> "1" UserGroup : tradingGroup
    AuctionTradingGroup --> "1" GroupedCounterpartyLimits : counterpartyLimits
```

### Core Entities

- **System**: Contains multiple Host and User instances.
- **Host**: Represents a company or entity managing auctions. Contains AuctionGroups and UserGroups.
- **UserGroup**: Contains multiple User instances with a specific GroupRole.
- **AuctionGroup**: Contains multiple Auction instances.
- **Auction**: Represents an individual auction event.
- **User**: Represents a user in the system.

### Roles and Permissions

#### User Roles

1. **SUPER_USER** and **SYSTEM_ADMIN**:
    - Can create and edit Hosts.
    - Manage system-wide settings and access logs.
    - Have visibility of all Users and Auctions across all Hosts.
    - Can only appear in adminGroup (GroupRole=ADMIN).
    - SYSTEM_ADMIN may have restricted access to financially sensitive information in the future.
    - Can set a Host to isMock
    - Can create and manage mock users for the host.

2. **HOST_USER**:
    - Can belong to multiple UserGroups in the same or different Hosts.
    - Restricted to one GroupRole per Host.
    - Excluded from UserGroups with GroupRole of ADMIN.
    - Visibility limited to assigned UserGroups and GroupRoles.

#### Group Roles

1. **ADMIN**:
    - Reserved for SUPER_USER and SYSTEM_ADMIN roles.
    - Excluded from other GroupRoles within the Host.

2. **AUCTIONEER**:
    - Can create and manage all UserGroups except ADMIN.
    - Can invite users to join all UserGroups except ADMIN.
    - Can create and manage Auctions.
    - Restricted from viewing users outside their Host.

3. **TRADER**:
    - Participates in auctions without visibility into other traders.
    - Can belong to multiple traderGroups within the same Host.
    - Restricted to one traderGroup per Auction.

4. **INTERNAL_OBSERVER**:
    - View-only access similar to AUCTIONEER.
    - Cannot create, edit, or delete UserGroups, AuctionGroups, or Auctions.

5. **EXTERNAL_OBSERVER**:
    - View-only access with restrictions on certain Auction information.
    - Cannot see Trader identities.

### Mock Objects and Test Environment

The system supports mock objects (Users, Auctions, and Hosts) for testing, demonstrations, and
training.

#### Mock Object Properties

- User, Auction, and Host entities include an `isMock` boolean property.
- Mock objects coexist with real objects, maintaining the same access controls but with more
  flexible authentication for mock users.

#### Creation and Management of Mock Objects

1. **Host-Level Mock Management**:
    - If a Host has `isMock` set to true, all HOST_USER User types can be mocked.
    - For non-mock Hosts, only TRADER and EXTERNAL_OBSERVER user types can be mocked.
        - as the auctioneer can manage those groups on a per-auction basis.

2. **Auction-Level Mock Management**:
    - Auctions containing any mock users must have `isMock` set to true when created (immutable).

3. **Mock User Creation**:
    - SYSTEM_ADMIN and SUPER_USER roles can create and edit mock users based on templates.
    - Naming convention: `<host prefix>-<group>-<group-number><group member letter>`
        - where `<group>` can be, for example: 't' for TRADER, 'eo' for EXTERNAL_OBSERVER
        - for GroupRoles that only have a single UserGroup, `<group>` can be omitted.
        - as discussed these Hosts would have to have isMock set to true:
    - Example: Host "Boardwalk" with prefix "BE" and 3 mock trading groups (2 traders each):
        - Users: BE-t1a, BE-t1b, BE-t2a, BE-t2b, BE-t3a, BE-t3b
        - Groups: BE-t1, BE-t2, BE-t3
        - and for Auctioneers: BE-a1, BE-a2, etc.

4. **Mock Auction Naming**:
    - Prefix "MOCK AUCTION " for all mock auctions.

### Constraints and Rules

1. Non-mock Hosts allow only mock TRADER and EXTERNAL_OBSERVER users.
2. Mock Hosts cannot have non-mock Auctions (I think, not sure about tests).
3. UserGroups with mock users can only associate with mock Auctions.
4. Mock users cannot have email usernames and must follow the specified naming convention.

### Data Privacy and Isolation

- Strict data isolation per Host.
- Users see only information pertinent to their assigned groups and roles within their Host.
- AUCTIONEER and other roles cannot access information about users or Hosts outside their assigned
  HostGroups.

### Access Control

- Enforced through Role-Based Access Control (RBAC) based on UserRole and GroupRole.
- Backend ensures consistent permission management.
- Each role is associated with specific permissions governing actions like creating Hosts, managing
  Auctions, and viewing user information.

### User Management

#### User Movement

- Users can transition between different real-world companies within the same Host.
- Visibility of auctions depends on the current GroupRole.
- Historical visibility mimics real-world behavior but may be revised in the future.

#### User Transfers Between Companies

- Remove the user from all UserGroups of the former company.
- Add the user to relevant UserGroups of the new company.
- Manage ongoing auction participation appropriately.

#### User Invitation Workflow

1. AUCTIONEER roles send invitations via email. SUPER_USER and SYSTEM_ADMIN can also send
   invitations.
2. Invited users accept and are assigned to appropriate UserGroups.
3. New accounts are created upon acceptance if the user doesn't exist.
4. Users are added to traderGroups without AUCTIONEER knowing existing system users.

### Auction Management

#### Auction Types

- ONE_SIDED
- TWO_SIDED_BROKER
- TWO_SIDED_DEALER

These will be discussed below.

### Credit Management

- CreditAccounts should be scoped per UserGroup to handle multiple concurrent auctions.
- Implement checks to prevent overstepping credit limits during trading operations.
- Different auction types have specific credit and budget constraints:
    1. One-to-many: Dollar constraints (budgets, credit) and volume limits.
    2. Many-to-many (Broker mode): Individual and aggregate limits for trading companies.
    3. Double-sided Dealer mode: Auctioneer has budget and credit limits with each trading group.

### User Interface Considerations

- Provide clear visual indicators to distinguish mock objects from real ones.
- Ensure users are aware when participating in a mock auction.

### Pending Considerations

1. Visibility of past auctions from previous HostGroups for users.
2. Management of test users across Hosts.

### Future Enhancements

- Configurable visibility settings for past auctions.
- Enhanced test environment controls.
- User interface improvements for distinguishing between Hosts and Groups.
- Audit and logging for critical operations.

This document provides a comprehensive overview of the Auction Management System, including its
domain model, roles and permissions, mock object management, and key constraints. The information is
organized for clarity while maintaining all important details and constraints.

## (2) Clock Auction Overview

All of our auction designs are "clock auctions".

Clock auctions work like this:

- the auction proceeds in a series of rounds
- at the start of each round the platform sets a round price
- traders indicate the quantity they are willing to transact at that round price.
- the round price increases or decreases (depending on the type of auction, as explained below)
- No transactions occur until the auction ends, and the system determines a round price which
  represents the market clearing price (details out of scope).
- Very important: traders NEVER enter prices, instead they ONLY submit quantities at the round
  price.
- rules exist to enforce rational bidding, ie: traders may NOT indicate a willingness to:
    - buy more quantity at a higher price or less at a lower price.
    - sell more quantity at a lower price or less at a higher price.

Clock auctions may be have: 
- one or two price clocks (One sided vs Two sided)
- price may move in a direction which is worse for trader (English clock)
- price may move in a direction which is better for the trader (Dutch Clock)

Note: these definitions are consistent with those of Vernon Smith et al, as explained for
example in their experiments with double-sided clock auctions

### English vs Dutch Clock auctions.

English clock auctions:

- the price ALWAYS gets **worse** each round for the traders, ie:
- For buying: the price would start low and increase each round for the buyers
- For Selling: the price would start hight and decrease each round for the sellers
  In both cases each round the price gets **worse** for the traders

Dutch clock aucions:

- the price ALWAYS get's **better** for traders, ie:
- For buying: the price starts hight and decreases each round for the buyers
- For Selling: the price starts low and increases each round for the sellers
  In both cases each round the price gets **better** for the traders

### One-sided vs Double-sided

One-sided auctions have one set of traders: buyers or sellers
There is only one price clock.

Double-sided clock auctions have two sets of traders: buyers and sellers.
They may have one or two price clocks, leading to 3 variations:

### Double Sided Clock Auctions

There are 3 types of Double Sided Clock Auctions (see Vernon Smith's experiments).

#### (a) Double-English Clock auction
There are two price clocks

- The buy-side clock starts low and increases in price (gets worse for the buyers)
- The sell-side clock starts high and decreases in price (gets worse for the sellers)
- in both cases the price is getting worse for traders hence English/English

#### (b) Double-Dutch Clock Auction
Again there are two price clocks

- The buy-side clock starts high and decreases (gets better for the buyers)
- The Sell-side clock starts low and increases (gets better for the sellers)
- In both cases the clock is getting better for the traders, hence: Dutch/Dutch

#### (c) Dutch/English Auction
Here there is one clock, and one clock direction .
The first round price is randomly chose to be above or below estimated market clearing price.
Buyers sumbit the either a buy quantity or sell quantity at the first round price.

If first round demand (buy) quantity is greater than supply (sell) quantity, the the price will 
decrease. So:
- for buyers the price is getting worse, ie: English Clock Auction
- for sellers the price is getting better, ie: Dutch Clock Auction

If first round supply is greater than demand, then the price decreases, so:
- for buyers the price is getting better, ie: Dutch Clock Auction
- for sellers the price is getting worse, ie: English Clock Auction

Note the term Dutch Auction is overloaded in the Auction literature.
The orginal Dutch auction was a flower auction in which the clock starts high and the first
buyers to submit a bid stops the clock and gets that lot of flowers at the last clock price
So: buy our definitions would be a One-Sided Descending Price Dutch Clock Auction.
ie: the price is descending, and getting better for the traders (who are buying) hence it is an
English auction. Typically in those auctions buyers don't indicate quantity they simply stop the
clock.

The term Dutch auction is also used in procurement auctions where the price starts high and
decreases, so it gets worse for the traders and by our definition it is actually an
One-sided Descending Price English Auction!

### Clock Auction Quantity Constraints

#### Max Quantity

- Max quantity a trader can enter in a round.
- This is often called Eligibility
- it is a Ceiling
- Initial Max Quantity (Initial Eligibility) is the maximum quantity a trader can enter in the
  first round.


#### Minimum Quantity

- this is often called Commitment, or Floor
- It is the minimum quantity a trader can enter in a round.
- for English clock auctions Min Quantity is alway zero, and not used.
- for Dutch clock auctions Initial Min Quantity is always zero and not used.

#### Preventing Irrational Bidding

Note:

- for English clock auctions max quantity can never increase in round price
    - because the price is getting worse and wanting more quantity at a worse price is irrational
- for Dutch clock auctions max quantity never changes during the auction, and minimum quantity
  can never be reduced
    - because the price is always getting better and wanting less quantity at a better price would
      also be irrational


#### One-sided clock auctions quantity constraints



| Quantity Constraint | English                | Dutch                                          |
|---------------------|------------------------|------------------------------------------------|
| Initial Min         | Not used (always zero) | Not used (always zero)                         |
| Initial Max         | Used                   | Used                                           |
| Round Min           | Not used (always zero) | Used                                           |
| Round Max           | Used                   | Not used (always same an initial max quantity) |

Notes:
- Inital Min Quanity is always zero in both English and Dutch versions, and not used 

One Sided English Clock auction uses:
  - Inital max quantity 
  - Round max quantity

One Sided Dutch Clock Auction uses:
- Inital Max quantity (max quantity doesn't change during the auction, round max 
  quantity is always the same as Initial round Quantity)
- Round Min quantity is used (and cannot decrease in subsequent rounds)


#### Two Sided Clock Auction Constraints

| Quanitity Constraint | English/English | Dutch/Dutch  | Dutch/English |
|----------------------|-----------------|--------------|---------------|
| Round Min Buy        | Not used (0)    | Used         | Used          |
| Round Max Buy        | Used            | Not used*    | Used          |
| Round Min Sell       | Not used (0)    | Used         | Used          |
| Round Max Sell       | Used            | Not used*    | Used          |

Notes:
In all 3 designs:
- Initial Min Buy and Sell Quantities:
  - are always zero and not used
- Initial Max Buy and Sell Quantities: 
  - are used in all 3 designs. 
- * In Dutch/Dutch auctions, the round max quantity is always the same as the initial max
    quantity, so it's not separately specified.

   
##### English / English auctions:

Uses these quantities:
- Initial Max Buy
- Initial Max Sell
- Round Max Buy
- Round Max Sell

Inital buy and sell quantities are always zero. 
Round buy and sell quantities are always zero. 

##### for Dutch / Dutch auctions:

Uses these quantities
- Initial Max Buy 
- Initial Max Sell 
- Round Min Buy
- Round Min Sell

Initial Min Buy and Sell quantities are always zero.
Round Max Buy and Sell quantities are always the same as their initial values.

##### Dutch / English auctions:

Traders can be either buyers or sellers, and that isn't know in advance of the auction.

So we used these quantities:
- Initial Max Buy
- Initial Max Sell
- Round Max Buy
- Round Max Sell
- Round Min Buy
- Round Min Sell

Initial Min Buy and Sell are always zero

### Activity

This is the total quantity submitted in a round

#### One-Sided Activity

Trader Activity vs Round activity:
- Trader Activity: quantity one trader has entered in a round
- Round Activity: quantity all traders have entered in a round

#### Double-sided activity:

- Trader Buy Activity: buy quantity submitted by one trader in one round
- Trader Sell Activity: sell quantity submitted by one trader in one round
- Round buy Activity: buy quantity submitted by all traders in one round
- Round Sell Activity: sell quantity submitted by all traders in one round

## (3) Our clock auctions

### ONE_SIDED

#### Description

Our one sided auction is an English Clock Auction (price gets worse for traders during the auction)

If the Auctioneer wishes to sell to Traders:
- The price starts low and increases round by round  

If the Auctioneer wishes to buy from Traders:
- the price starts high and decreases.

So for both sides the price is getting worse (therefore English)

Whether buying or selling, we set an initial max quantity.
Then every round after that the trader's max quantity is the trader's Activity in the prior round.
ie: the max quantity in the next round is equal to the quantity submitted in the prior round.
so: Traders cannot enter greater quantities in subsequent rounds than in priors rounds,
as doing so would be irrational (being willing to buy more at higher prices, or sell less at
lower prices)
This as also called monotonicity

#### Quantity constraints

These are the same as the English auction quantity constraints given in the table above:
- initial max quantity, and round max quantity

#### Monetary Constraints

We have run hundreds of these auctions and there has never been a requirement for a monetary
value constraint (ie: Credit Limit or Budget)

Interestingly in all of these auctions so far, the auctioneer has applied the default Initial
Max Buy Quantities to all traders, and never changed individual trader intial max quantities!
- though we have retained the ablitity to to that.

Additionnaly:
- in some cases the Price unit is based on an index price set elsewhere, at another time, so the
  monetary value of an order in an auction can't be calculated anyway
- Note: sometimes the auction is priced as a discount to an index, so it's possible to used a
  descending price auction to figure out a buying price (each round get's worse for the buyers,
  as the discount decreases, so it's still an English auction)
- also in many cases it's not possible to determine monetary value because it might, for example
  be based on a Term, with the buyer having differnt term auctions, at their discretion, after
  the auction.
    - eg: the Price Units could be Dth/day, and the buyer has the option of 12 or 24 month terms.

 
### b) TWO_SIDED_DEALER and TWO_SIDED_BROKER 

#### Dutch/English Double-Sided Price-Reversing Clock Auction Overview

Our TWO_SIDED_DEALER and TWO_SIDED_BROKER auctions are both Dutch/English Double Sided Clock 
Price-Reversing 
auctions.  

ie: there is one clock, setting the same round price for buyers and sellers.
But: but in our variant, the round price can reverse during the auction ! 

There many rules for this auction the most important being:
- Buying rule a Trader may not indicate a willingness to buy more at a higher price or less at a 
  lower price
- Selling rule: a Trader may not indicate a willingness to sell more at a lower price nor less at 
  a higher 
      price
- Switching rule: a trader may not indicate a willingness to either:
  - sell at a lower price than they were willing to buy at, or
  - buy at a highter price than they were willing to sell at

These rules are essentially the same as the English and Dutch one-sided clock auctions that 
prevent irrational bidding, but applied to buyers and sellers seeing the same price clock.

The difference between our TWO_SIDED_DEALER and TWO_SIDED_BROKER auctions is that:
- in the DEALER version, traders always transact directly with the auctioner and only indirectly
  with each other.
- in the BROKER version, traders transact directly with each other

Price reversal is an important added feature not seen in any other double sided clock auction.
It has it's several unique constraints:
- a) the price can only reverse one time.
- b) when the price reverses it does so in much smaller steps that the pre-reversal steps.
  - for example the price change before reversal might be 1 cent every round, but after it 
    reverses it might do so in 0.25 cent steps. 
- c) after reversal the price never reaches or passes through the pre-reversal round price
  - eg: if the price is increasing by 1 cent per round and then reverses in 0.25 cent decrements 
    it will at most do so for 3 rounds, as the next round after that would be the pre-reversal 
    round price.

#### More detailed explanation:

The first round price is set randomly to be some percentage higher or lower than the
estimated market clearing price.

Traders then Submit either a Buy or a Sell Order at the first round price.
If in the first round:

- Demand > Supply then the price increases in the next round
- Demand < Supply then the price decreases in the next round

The system continues until either:

a) there is a round were buy and sell quantity are exactly matched

- in which case the auction ends and the final round price is the market clearing price
- all traders receive their matched buy and sell volumes and the system determines the
  counterparties
- this could happen in the first round.

b) or there is a round at which the matched volume decreases.

- this round is called the Overshoot Round, and the round before is the Prior Overshoot Round
- what happens next is that round price reverses, and moves in the opposite direction.
- the post reversal round price changes are smaller that pre reversal.
- eg: pre reversal the round price might be increasing in 1 cent increaments,
    - and post reversal the round price decreases in 1/4 cent decrement.
- Now the auction then ends either:
    - if the matched volume does not increase
- or the next auction round would be at or through the Pre-reversal round price.
- eg: if the round price is initially increasing and the pre-reversal round price is 51 cents
    - and if the post-reversal price change is 0.25 cents
    - then post reversal the round price will decreas in 0.25 decrements until either the match
      stops increasing, or the round price is 50.25 cents
    - ie: after reversing the round price moves toward the pre-reversal round price in smaller
      steps but will stop before it reaches or would pass throuh that price again
- in all casses the market clearing price is the earliest round price which results in the
  greatest matched volume

So the interesting thing about this auction is that when the price is increasing:

- for buyers it an English Clock Auction (price is getting worse)
- but for sellers it is a Dutch Clock Auction (price is getting better)
  And the opposite is the case when the price is decreasing:
- for buyers it is a Dutch Clock Auction (price is getting better)
- for sellers it is an English Clock Auction (price is getting better)
  Also interestingly: when the price reverses:
- the English clock aucion becomes a Dutch Clock Auction for the traders on that side
- and the Dutch clock auctions becomes an English Clock auction for the traders on that side!

The quantity rules for Dutch and English clock auctions still apply in general, with some tweaks
to account for the price reversal, and also to account for buyers and sellers switching (when
they are able to)

For example:

- in the pre-reversal phase
    - if a trader submits a buy order and the price increases in the next round
        - then in that round their new max buy quantity will be equal to the that buy quantity
        - but because the price has increased they can switch to a seller.
        - The will not however ever be able to increase the order quantity even if the price
          reverses
        - Reason if the price reverses and they still want to buy, they will now be in a Dutch
          auction and cannot
        - and it can never increase even if, after that round the price reverses
        - they will however be able to switch to a seller because they
            - for buyers this is an English auction and their new max buy quantity is their prior
              round
              buy activity (buy quantity)
            - for sellers this a Dutch auction and their
            - because the price is increasing they can switch to become sellers and if the price
              continues increasing in the subsequent round then they their
                - then for buyers, like in English auctions, their new max buy quantity is the prior
                  round buy
                  quantity.
                - the sell min and max quantities do not change, and they can
                    - max buy quantity is equal to the prior round buy quantity
                    - their min buy quantity and both min and max sell quantitis do not change
                    - 
#### Quantity Constraints

Our Double-sided auctions are Dutch/English auctions and the quantity constraints are the same 
as the table above for Dutch/English auctions, ie:
- Initial Max Buy and Sell Quantities
- Round Min and Max Buy quantities
- Ronud Min and Max Sell quantities

#### Monetary Value Constraints

These auctions have not yet been implemented, so we're not sure that monetary value constraints 
are required (like our experience with one-sided auctions).

However as the transactions are now many-to-many, whether or not mediated by the auctioneer, we 
presume that unless we use a 3rd party credit solution (eg: letters of credit), we will need buy 
and sell monetary constraints for individual trader to trader limits, and for each trader for 
the auction.

ie: Credit and Budget limits per counterparty and per auction.
In the Dealer mode, the auctioneer needs credit and budget limits for 
the auction and for each trader.
In the Broker mode, each trader needs credit and budget limits for the auction and for each 
other trader (counterparty).

## (4) Backend functions

Typically we group creation and editing of objects into one backend function, which allows us to
centralize the backend validation, and also allows use to use the same frontend form to
create and edit the object. ie: in terms of CRUD, we combine Create and Update.

For example, assume we have a User object with properties: username, role and password. Then we
would have two backend functions UserSave, and UserDelete. The UserSave function would have an
isCreate boolean property and would know whether the request is to create a new user or edit the
existing User object. Note: we name these backend functions as Entity + action, eg: UserSave,
UserDelete

Note also that this is push based system, there is no need to issue Get or Read commands, the
backend knows what the user needs to see.

### **1. System Management Functions**

Note: currently we don't have any defined System Management requirements

#### **SystemSave**

- **Description**: Create or update system-wide settings, including global configurations and
  system-level parameters.
- **Purpose**: Allows **SUPER_USER** and **SYSTEM_ADMIN** roles to manage overall system
  configurations, ensuring the platform operates according to organizational policies.

#### **SystemDelete**

- **Description**: Reset or remove system-wide settings if necessary.
- **Purpose**: Enables administrators to clean up obsolete configurations, though typically used
  cautiously to maintain system integrity.

### **2. Host Management Functions**

#### **HostSave**

- **Description**: Create or update a Host, including its properties and associated UserGroups. This
  includes setting the `isMock` property.
- **Purpose**: Allows **SUPER_USER** and **SYSTEM_ADMIN** roles to manage Hosts, facilitating the
  organization of auctions and users within the system.

#### **HostSetMock**

- **Description**: Set or update the `isMock` status of a Host independently.
- **Purpose**: Allows administrators to enable or disable mock environments for a Host separately
  from other Host settings, providing flexibility in testing and training scenarios.

### **3. User Management Functions**

Note: typically the SuperUser or SystemAdmin would add users to the auctioneer role, and then
that user would be able to issue User invitations, and associate them with the relevant
UserGroup in the Host. We need to decide if this is done from a Host Page or a User Page.

#### **UserSave**

- **Description**: Create or update a user. Handles both creation and editing based on an `isCreate`
  flag.
- **Purpose**: Manages user accounts, including mock users, ensuring appropriate roles and
  permissions are assigned.

#### **UserDelete**

- **Description**: Delete a user from the system.
- **Purpose**: Allows for the removal of users when necessary, maintaining security and compliance.

#### **UserInvite**

- **Description**: Send an invitation to a new user to join the platform.
- **Purpose**: Facilitates the onboarding of new users by **AUCTIONEER**, **SUPER_USER**, and *
  *SYSTEM_ADMIN** roles.

#### **UserAcceptInvitation**

- **Description**: Accept an invitation and create or activate a user account.
- **Purpose**: Enables invited users to join the platform securely and be placed into the correct
  UserGroups.

#### **UserTransfer**

Note: Unclear if this is needed!
Once a user is added to a Host it's unclear what changing roles means?
ie: if they were a trader and now change to an Auctioneer, for example if they switched companies,
then it's unclear what happens? Presumably they lose the ability to see the host from the
perspective of the old role (and in some cases that means there are no users in a trading group
in which they previously traded!).
So: if we allow users to transfer (and probably we should), then we need to track trader and
user groups in completed auctions in a immutable way, and probably too, we should have some way
of indicating that that trader is no longer a trader on this host and/or that their role has
changed (and they no longer have access to that auction).

- **Description**: Transfer a user between different UserGroups or Hosts, managing their roles and
  permissions accordingly.
- **Purpose**: Handles the movement of users between companies or roles, updating group memberships
  and access as needed.

#### **UserLogin**

- **Description**: Authenticate a user and establish a session.
- **Purpose**: Allows users to log into the system securely.

#### **UserLogoff**

- **Description**: Terminate a user's session.
- **Purpose**: Enables users to log off from the system, ensuring account security.


### **4. UserGroup Management Functions**

#### **UserGroupSave**

- **Description**: Create or update a UserGroup, including its name, role, and associated users.
- **Purpose**: Manages the grouping of users with specific roles within a Host, crucial for access
  control and permissions.
- Note: adding an removing users to and from trading groups, needs to be considered as above
  regarding changing roles of users, how do we indicate that users are different.
- One possible solution is that we create a separate user object, with the same username, mark
  the first as inactive, or transfered, and then create some property of User eg: "transferred from"
    - or transferred to, or both.
    - Note: UserGroups themselves can never change roles, ever, nor can they be transferred to
      other hosts.
    - this is just an issue if we allow a human to switch companies, and hence either switch user
      groups, while maintaining their same username / email address

#### **UserGroupDelete**

- **Description**: Delete a UserGroup from the system.
- **Purpose**: Allows for the cleanup of obsolete or unused groups, maintaining system integrity.

#### **UserGroupAddUser**

- **Description**: Add a user to a UserGroup.
- **Purpose**: Manages user memberships in groups, essential for role assignments and permissions.

#### **UserGroupRemoveUser**

- **Description**: Remove a user from a UserGroup.
- **Purpose**: Updates group memberships when users change roles or companies.


### **5. Auction Management Functions**

#### **AuctionSaveOneSided**

- **Description**: Create or update a one-sided auction with specified settings.
- **Purpose**: Allows **AUCTIONEER** roles to set up and modify one-sided auctions, including mock
  auctions.

#### **AuctionSaveDoubleSided**

- **Description**: Create or update a double-sided auction with specified settings.
- **Purpose**: Enables the creation and modification of two-sided auctions, including setting
  auction type and value adjustment factors.

#### **AuctionDelete**

- **Description**: Delete an auction (if not started).
- **Purpose**: Allows administrators to remove auctions that are no longer needed before they start.

#### **AuctionSetPrice**

- **Description**: Set or update the starting price of an auction.
- **Purpose**: Manages the initial price setting, crucial for clock auctions.

#### **AuctionSetConstraints**

- **Description**: Create or change bidder quantity and financial constraints.
- **Purpose**: Ensures compliance with auction rules and prevents irrational bidding.

#### **AuctionChangeState**

- **Description**: Change the state of the auction.
- **Actions**:
    - **START**: Starts the auction or the current round.
    - **PAUSE**: Pauses the auction or the current round.
    - **UNPAUSE**: Resumes the auction or the current round.
    - **END_ROUND**: Ends the current round.
    - **NEXT_ROUND**: Advances to the next round, implementing price movement logic, including price
      reversal if applicable.
    - **END_AUCTION**: Ends the auction.
- **Purpose**: Controls the flow of the auction rounds and overall auction lifecycle.

#### **AuctionAddTradingGroup**

- **Description**: Add a trading group to an auction.
- **Purpose**: Manages participants in an auction, essential for setting up trading relationships.

#### **AuctionRemoveTradingGroup**

- **Description**: Remove a trading group from an auction.
- **Purpose**: Updates the list of participants as needed.

#### **AuctionSetTradingGroups**

- **Description**: Set the list of trading groups participating in an auction.
- **Purpose**: Allows for bulk addition or removal of trading groups, useful when managing multiple
  groups at once.

#### **AuctionCalculateAward**

- **Description**: Calculate the auction results based on submitted orders.
- **Purpose**: Determines the market-clearing price and allocations.

#### **AuctionAward**

- **Description**: Finalize the auction results and notify participants.
- **Purpose**: Completes the auction process, initiating settlement procedures.

### **6. Trader Activity Management Functions**

#### **TraderSubmitOrder**

- **Description**: Submit or update a buy or sell order for a round.
- **Purpose**: Allows traders to participate in auctions, with backend validation against auction
  rules.

#### **TraderCancelOrder**

- **Description**: Cancel a previously submitted order before the round ends.
- **Purpose**: Provides flexibility for traders to adjust their participation, ensuring their orders
  reflect current intentions.

### **7. Financial Limit Management Functions**

Unclear if we need separate functions for Traders and Auctioneers

#### **TraderFinancialLimitsSave**

- **Description**: Set or update financial limits (credit limits and budgets) for a trader or group.
- **Purpose**: Manages credit and budget constraints, essential for two-sided auctions.

#### **TraderFinancialLimitsDelete**

- **Description**: Remove or reset financial limits for traders or groups.
- **Purpose**: Allows for the adjustment of financial constraints when they are no longer
  applicable.

#### **AuctioneerLimitAllocate**

- **Description**: Set or update financial limits (credit limits and budgets) for the auctioner
  group.
- **Purpose**: Distributes financial resources to auctions, ensuring they have the necessary limits
  in place.

#### **AuctioneerLimitDeallocate**

- **Description**: Deallocate financial limits from auctions back to auctioneer limits pool when
  auctions conclude.
- **Purpose**: Frees up financial resources, maintaining efficient use of limit pools.

### **8. Validation and Rule Enforcement Functions**

#### **ValidateAuctionSettings**

- **Description**: Ensure that auction settings comply with predefined rules and constraints before
  creation or updates.
- **Purpose**: Centralizes validation logic, preventing invalid auctions from being created or
  modified.

#### **ValidateTraderOrder**

- **Description**: Confirm that submitted trader orders adhere to auction rules, quantity
  constraints, and financial limits.
- **Purpose**: Ensures that all orders are valid and compliant, maintaining the integrity of the
  auction process.

### **9. Audit and Logging Functions**

Unclear what these do?

#### **AuditLogCreate**

- **Description**: Record significant events and actions, such as auction state changes, order
  submissions, and financial limit adjustments.
- **Purpose**: Supports accountability and tracking, facilitating troubleshooting and compliance.

#### **AuditLogRetrieve**

- **Description**: Access and review audit logs for monitoring and compliance purposes.
- **Purpose**: Enables administrators and auditors to examine historical actions within the system.

### **10. Access Control Functions**

Unclear what these do?

#### **AccessControlCheck**

- **Description**: Verify that users have the necessary permissions to perform requested actions
  based on their UserRole and GroupRole.
- **Purpose**: Centralizes permission checks, enhancing security and maintainability.

### **11. Notification Functions**

Unclear what they do?

#### **NotificationSend**

- **Description**: Dispatch notifications to users regarding important events, such as auction
  starts, order confirmations, and award results.
- **Purpose**: Improves user engagement and communication, ensuring users are informed of relevant
  updates.

### **12. Reporting and Analytics Functions**

Need more requirements

#### **ReportGenerate**

- **Description**: Create reports on auction outcomes, user activities, and financial summaries.
- **Purpose**: Provides insights and data for users and administrators, aiding in decision-making
  and performance evaluation.

### **13. System Health Monitoring Functions**

#### **SystemHealthCheck**

- **Description**: Monitor and report on the health and performance of the backend system.
- **Purpose**: Ensures reliability and availability, allowing administrators to proactively address
  issues.

### **Additional Considerations**

- **Data Push Mechanism**:

    - **Backend-Driven Updates**:
        - The backend proactively pushes data to the frontend based on a **PageSet** command.
        - **PageSet Command**:
            - **Description**: Informs the backend of the current page or context the user is
              viewing.
            - **Purpose**: Allows the backend to determine what data the user needs and when to
              update it.
        - **Note**: Specific data fetching functions may be added in the future as requirements
          evolve.

- **Mock Object Handling**:

    - Managed via the `isMock` property within existing functions.
    - **Note**: Separate functions for mock object management can be added if stricter separation is
      required.

- **Price Reversal in Auctions**:

    - Managed within **AuctionChangeState** and auction parameters.
    - No separate functions needed, as it is integrated into the auction state logic.

- **Access Control Enforcement**:
    - All functions enforce role-based access control, ensuring users can only perform actions
      permitted by their roles.
    - The **AccessControlCheck** function centralizes these permission checks.

- **Auction Rule Validation**:
    - handled by the backend

- **Matching Algorithm logic**:
    - currently impemented by the backend, it may I supposed be neccessary to override calculated
      awards and/or edit bids and have the system reapply the matching algorithm and/or recalculate
      awards
