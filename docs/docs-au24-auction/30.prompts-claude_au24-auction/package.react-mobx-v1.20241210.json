{"name": "react-mobx-v1", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "coverage": "vitest run --coverage", "clean": "rimraf .tsbuild/*.tsbuildinfo dist", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "cosmos": "cosmos", "cosmos-export": "cosmos-export", "monitor-console": "tsx console-monitor.ts"}, "dependencies": {"@bugfender/sdk": "^2.3.0", "@hookform/resolvers": "^3.9.0", "@million/lint": "^1.0.12", "@previewjs/cli": "^1.28.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@tabler/icons-react": "^3.22.0", "ag-grid-react": "^32.3.3", "chroma-js": "^3.1.2", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "fflate": "^0.8.2", "input-otp": "^1.2.4", "lodash": "^4.17.21", "lucide-react": "^0.446.0", "mitt": "^3.0.1", "next-themes": "^0.3.0", "pako": "^2.1.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.28.0", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "threads": "^1.7.0", "uuid": "^11.0.3", "valtio": "^2.1.2", "vaul": "^1.0.0", "yaml": "^2.6.0"}, "devDependencies": {"@ag-grid-community/theming": "^32.3.3", "@chromatic-com/storybook": "^3.2.2", "@eslint/js": "^9.11.1", "@faker-js/faker": "^9.2.0", "@playwright/test": "^1.49.0", "@previewjs/config": "^6.0.1", "@react-buddy/ide-toolbox": "^2.4.0", "@redux-devtools/extension": "^3.3.0", "@storybook/addon-essentials": "^8.4.3", "@storybook/addon-interactions": "^8.4.3", "@storybook/addon-onboarding": "^8.4.3", "@storybook/blocks": "^8.4.3", "@storybook/react": "^8.4.3", "@storybook/react-vite": "^8.4.3", "@storybook/test": "^8.4.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/node": "^22.7.3", "@types/pako": "^2.0.3", "@types/react": "^18.3.9", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.3", "@vitest/coverage-v8": "^2.1.5", "@welldone-software/why-did-you-render": "^8.0.3", "autoprefixer": "^10.4.20", "eslint": "^9.11.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "eslint-plugin-storybook": "^0.11.0", "globals": "^15.9.0", "jsdom": "^25.0.1", "postcss": "^8.4.47", "react-cosmos": "^6.2.0", "react-cosmos-plugin-boolean-input": "^6.2.0", "react-cosmos-plugin-open-fixture": "^6.2.0", "react-cosmos-plugin-vite": "^6.2.0", "storybook": "^8.4.3", "styled-components": "^6.1.13", "tailwindcss": "^3.4.13", "tsx": "^4.19.2", "typescript": "^5.5.3", "typescript-eslint": "^8.7.0", "vite": "^5.4.8", "vite-tsconfig-paths": "^5.1.2", "vitest": "^2.1.5", "winston": "^3.17.0", "winston-papertrail": "^1.0.5"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}}