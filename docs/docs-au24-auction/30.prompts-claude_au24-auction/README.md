# 30.propmts-claude_au24-auction

Dec 10, 2024:
- these are links to the current files in the: 
  - Claude au24-auction project
- Those files can't be edited
- These must always be the originals
  - [A.Start-here-for-React-Shadcn-instructions.md](A.Start-here-for-React-Shadcn-instructions.md)
  - [A.prompt-requirements-and-design.md](../prompts/A.prompt-requirements-and-design.md)
  - [C.MockValtioStoreForPreview.md](../react/C.MockValtioStoreForPreview.md)
  - [D.Client-Architecture.md](../prompts/D.Client-Architecture.md)
  - [package.react-mobx-v1.20241210.json](package.react-mobx-v1.20241210.json)
  - [volume-meter.md](../ui-requirements/volume-meter.md)
  - [C.React-ui-next-steps.md](../react/C.React-ui-next-steps.md)
  - [B.React-AgGrid.md](../react/B.React-AgGrid.md)
  - [A.auction-rules.md](../auction-rules-slides/A.auction-rules.md)
  - [generated.ts](../../projects/node/packages/pinx-vue-full-version/src/au24/types/generated.ts)

NOTE: C.MockValtioStoreForPreview.md may need to be augmented.
See the file domain-store.valtio.ts
(and make sure to get the latest copy of that files from react-mobx-v1)



