# Float Button API

## FloatButton Props

| Name | Type | Default | Description | Version |
|------|------|---------|-------------|---------|
| bottom | number \| string | 40 | CSS bottom property of the button group | |
| height | number \| string | 40 | CSS height property of the button group | |
| left | number \| string | undefined | CSS left property of the button group | |
| menu-trigger | 'click' \| 'hover' | undefined | Trigger action to show submenu | |
| position | 'relative' \| 'absolute' \| 'fixed' | 'fixed' | CSS position property of the button group | |
| right | number \| string | undefined | CSS right property of the button group | |
| shape | 'circle' \| 'square' | 'circle' | Shape of the button | |
| show-menu | boolean | undefined | Whether submenu of the button is shown | |
| top | number \| string | undefined | CSS top property of the button group | |
| type | 'default' \| 'primary' | 'default' | Type of the button | |
| width | number \| string | undefined | | |
| on-update:show-menu | (value: boolean) => void | undefined | Callback when the menu is opened or closed | |

## FloatButtonGroup Props

| Name | Type | Default | Description | Version |
|------|------|---------|-------------|---------|
| bottom | number \| string | undefined | CSS bottom property of the button group | |
| left | number \| string | undefined | CSS left property of the button group | |
| position | 'relative' \| 'absolute' \| 'fixed' | 'fixed' | CSS position property of the button group | |
| right | number \| string | undefined | CSS right property of the button group | |
| shape | 'circle' \| 'square' | 'circle' | Shape of the button group | |
| top | number \| string | undefined | CSS top property of the button group | |

## FloatButton Slots

| Name | Parameters | Description | Version |
|------|------------|-------------|---------|
| description | () | Description for the button | |
| menu | () | Submenu of the float button | |


