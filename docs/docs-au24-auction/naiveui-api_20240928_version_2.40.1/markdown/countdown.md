# Countdown API

## Countdown Props

| Name | Type | Default | Description | Version |
|------|------|---------|-------------|---------|
| active | boolean | true | Whether countdown is active | |
| duration | number | 0 | The duration of the countdown (unit is millisecond). Not reactive | |
| precision | 0 \| 1 \| 2 \| 3 | 0 | The precision of the second | |
| render | (props: { hours: number, minutes: number, seconds: number, milliseconds: number }) => VNodeChild | undefined | Time's render function | |
| on-finish | () => void | undefined | The callback on countdown is finished | |

## Countdown Methods

| Name | Type | Description | Version |
|------|------|-------------|---------|
| reset | () => void | Reset countdown | |

