# Transfer API

## Transfer Props

| Name | Type | Default | Description | Version |
|------|------|---------|-------------|---------|
| clear-text | string | undefined | Text of clear button | |
| default-value | Array<string \| number> \| null | null | Default value | |
| disabled | boolean | true | Disabled state | |
| filter | (pattern: string, option: TransferOption, from: 'source' \| 'target') => boolean | A basic label string match function | Filter function | from 2.22.0 |
| options | TransferOption[] | [] | For configuration options see the TransferOption Type below | |
| render-source-label | (props: { option: TransferOption }) => VNodeChild | undefined | Customize source label rendering | |
| render-target-label | (props: { option: TransferOption }) => VNodeChild | undefined | Customize target label rendering | |
| render-source-list | (props: { onCheck: (checkedValueList: Array<string \| number>) => void, checkedOptions: TransferOption[], pattern: string }) => VNodeChild | undefined | Customize source list rendering | |
| render-target-list | (props: { onCheck: (checkedValueList: Array<string \| number>) => void, checkedOptions: TransferOption[], pattern: string }) => VNodeChild | undefined | Customize target list rendering | |
| select-all-text | string | undefined | Text of select all button | |
| show-selected | boolean | true | Whether to show selected options in the source list | |
| size | 'small' \| 'medium' \| 'large' | 'medium' | Size | |
| source-filterable | boolean | false | The source filterable state | |
| source-filter-placeholder | string | undefined | Placeholder for the source items search box | |
| source-title | string \| (() => VNodeChild) | undefined | Source items title | Render function since 2.22.0 |
| target-filterable | boolean | false | The target filterable state | |
| target-filter-placeholder | string | undefined | Placeholder for the target items search box | |
| target-title | string \| (() => VNodeChild) | undefined | Target items title | Render function since 2.22.0 |
| value | Array<string \| number> \| null | undefined | Value when being set manually | |
| on-update:value | (value: Array<string \| number>) => void | undefined | Callback when the value changes | |
| virtual-scroll | boolean | false | Enable virtual scrolling | |

## TransferOption Type

| Property | Type | Description |
|----------|------|-------------|
| label | string | The option's label to display |
| value | string \| number | The option's unique value |
| disabled | boolean | The option's disabled state |

