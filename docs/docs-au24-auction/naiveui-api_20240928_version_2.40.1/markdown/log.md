# Log API

## Log Props

| Name | Type | Default | Description |
|------|------|---------|-------------|
| font-size | number | 14 | Font size |
| hljs | Object | undefined | If you want to set hljs locally pass it using this prop |
| language | string | undefined | The language of the log in highlightjs |
| line-height | number | 1.25 | Line height |
| lines | Array<string> | undefined | Display the log content by line. When the log parameter exists at the same time the parameter is invalid |
| loading | boolean | false | Whether to show loading |
| log | string | undefined | The content of the log |
| rows | number | 15 | Log size |
| trim | boolean | false | Whether to display the log after trim |
| on-require-more | (from: 'top' \| 'bottom') => void | undefined | Callback function for scroll loading log |
| on-reach-top | () => void | undefined | Scroll to the top callback function |
| on-reach-bottom | () => void | undefined | Scroll to the bottom callback function |

## Log Methods

| Name | Parameters | Description |
|------|------------|-------------|
| scrollTo | (options: { top: number, position: 'top' \| 'bottom', silent: boolean }) | Callback function for scroll event |

