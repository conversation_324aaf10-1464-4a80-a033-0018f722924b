import { random_number, random_string } from "@/au24/utils"
import { type DeRoundResultVM, type DeScenarioMatchVM, type DeTraderFlowVM, OrderType } from "@/au24/types/generated.js"

export const createDemo__DeScenarioMatchVM = (buyer_id: string, seller_id: string): DeScenarioMatchVM => {
	const quantity = random_number()

	return {
		buyer_id,
		buyer_shortname: random_string(),
		round_number: random_number(),
		seller_id,
		seller_shortname: random_string(),
		actual_match: quantity,
		actual_match_str: quantity + ""
	}
}

export const createDemo__DeTraderFlowVM = (company_id: string): DeTraderFlowVM => {
	return {
		company_id,
		company_shortname: random_string(),
		order_type: OrderType.BUY,
		quantity: random_number() + ""
	}
}

export const createDemo__DeRoundResultVM = (round: number, companyIds: string[]): DeRoundResultVM => {
	const matches: DeScenarioMatchVM[] = []
	companyIds.forEach(buyerId => {
		companyIds.forEach(sellerId => {
			matches.push(createDemo__DeScenarioMatchVM(buyerId, sellerId))
		})
	})

	const trader_flows: DeTraderFlowVM[] = []
	companyIds.forEach(companyId => {
		trader_flows.push(createDemo__DeTraderFlowVM(companyId))
	})

	return {
		buy_total: random_number() + "",
		match_total: random_number() + "",
		matches,
		round_number: round,
		round_price: random_number() + "",
		sell_total: random_number() + "",
		trader_flows
	}
}
