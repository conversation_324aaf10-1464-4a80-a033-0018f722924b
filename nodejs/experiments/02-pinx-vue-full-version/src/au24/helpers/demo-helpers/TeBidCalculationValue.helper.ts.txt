import {TeBidCalculationValue,} from '../_generated/generated'
import {random_bool, random_number} from '@/au21-frontend/utils'

export function createDemo__TeBidCalculationValue(): TeBidCalculationValue {
  return {
    exceeds_credit_limit: random_bool(),
    npv: random_number({rand: 10000, mult: 1}) + '',
    npv_risk_adjusted: random_number({rand: 10000, mult: 1}) + '',
    npv_per_dth: random_number({rand: 1000, mult: 0.0001}) + '',
    npv_per_dth_risk_adjusted: random_number({rand: 1000, mult: 0.0001}) + '',
    total_exposure: random_number({rand: 10000, mult: 1}) + '',
  }
}
