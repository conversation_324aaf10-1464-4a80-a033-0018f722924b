<template>
	<ag-grid-vue
		:class="`${theme} AuBaseTable`"
		:style="computedStyle"
		:rowData="rowData"
		:columnDefs="columnDefs"
		:pinnedTopRowData="pinnedTopRowData"
		:pinnedBottomRowData="pinnedBottomRowData"
		@grid-size-changed="onGridSizeChanged"
		:gridOptions="gridOptionsLocal"
		:getRowHeight="getRowHeight"
	/>
</template>

<script setup lang="ts">
import { computed, onBeforeMount, onMounted, ref, watch } from "vue"
import { GridApi, GridOptions } from "ag-grid-community"
import { until } from "@vueuse/core"
//import "ag-grid-community/styles/ag-grid.css"
//import "ag-grid-community/styles/ag-theme-balham.css"
import "@/au24/au-styles/ag-grid-theme-builder-2024-07-09.css"
import { AgGridVue } from "ag-grid-vue3"

/*
a nice thing about emits is that they can be thrown to parents of parents, etc.
so, we don't need to emit @grid-ready, or @row-clicked, etc
 */

/*
See TraderSelectTable for how to handle checkboxes
- and also how to enable clicking on the entire row to toggle the checkbox
- basically you have to both:
  - a) 	suppressRowClickSelection: true on the gridOptions
     - this is because clicking on the row will select only that row
     - and clear all other selections
  - b) set that row's isSelected property:
   function onRowClicked(event: RowClickedEvent) {
	if (gridApi) {
		event.node.setSelected(!node.isSelected()); // Toggle the row selection
	}
}

 */
const props = defineProps({
	auto_refresh: {
		type: Boolean,
		default: true
	},
	columnDefs: Array,

	// todo: implement :rowHeight="'auto'" as the default

	getRowHeight: {
		type: Function,
		default: () => 27
	},
	gridOptions: Object,
	gridSizeChanged: Function,
	height: {
		type: Number | String,
		default: 400
	},
	width: {
		type: Number | String,
		default: "100%"
	},
	pinnedBottomRowData: Array,
	pinnedTopRowData: Array,
	rowData: Array,
	turnOffAutoColSize: Boolean
})

// adding this so that the parent can use @grid-ready,
// - ie: that's what you'd get from AI if it assumed that
//       this component has an AgGridVue interface

const theme = ref("ag-theme-balham-dark")
const gridApi = ref<GridApi | null>(null)
const columnApi = ref(null)
const gridOptionsLocal = ref<GridOptions>({}) as any
const gridReady = ref(false)

defineExpose({
	gridApi
})

const computedStyle = computed(() => {
	return {
		height: typeof props.height === "number" ? `${props.height}px` : props.height,
		width: typeof props.width === "number" ? `${props.width}px` : props.width,
		"user-select": "none",
		outline: "none"
	}
})

onBeforeMount(() => {
	const optionsDefault: GridOptions = {
		animateRows: false,
		alwaysShowVerticalScroll: true,
		getRowId: params => params.data.id,
		headerHeight: 35,
		suppressCellFocus: true,
		suppressContextMenu: true,
		suppressLoadingOverlay: true,
		suppressMenuHide: true,
		suppressMovableColumns: true,
		suppressNoRowsOverlay: true,
		suppressScrollOnNewData: true,
		defaultColDef: {
			enableCellChangeFlash: true
		}
	}
	gridOptionsLocal.value = { ...optionsDefault, ...props.gridOptions }
})

onMounted(() => {
	// Additional logic can be added here if needed
})

const onGridReady = (params: any) => {
	gridApi.value = params.api
	columnApi.value = params.columnApi
	gridReady.value = true
}

const onGridSizeChanged = () => {
	props.gridSizeChanged?.(gridApi.value)
	if (!props.turnOffAutoColSize && gridApi.value) {
		gridApi.value.sizeColumnsToFit()
	}
}

const scrollToBottom = async () => {
	const count = props.rowData?.length
	if (!count) return
	await until(gridReady).toBeTruthy()
	if (!gridApi.value) return
	gridApi.value.ensureIndexVisible(count - 1, "bottom")
}

const ensureColumnVisible = async (key: string) => {
	await until(gridReady).toBeTruthy()
	if (!gridApi.value) return
	gridApi.value.ensureColumnVisible(key)
}

const refresh = async () => {
	await until(gridReady).toBeTruthy()
	if (!gridApi.value) return
	gridApi.value.refreshHeader()
}

watch(
	() => props.rowData,
	() => {
		if (props.auto_refresh && gridApi.value) {
			gridApi.value.refreshCells({ suppressFlash: true })
		}
	},
	{ deep: true }
)
</script>

<style lang="less" scoped>
@import (reference) "../../../../au-styles/variables";

.AuBaseTable :deep(.ag-body-viewport) {
	overflow-y: scroll !important;
}

.AuBaseTable :deep(.ag-cell-reset) {
	border: 0;
	margin: 0;
	overflow: hidden;
}

.AuBaseTable :deep(.ag-cell) {
	border-right: 1px solid hsl(0, 0%, 23%) !important;
	overflow: hidden !important;
	margin: 0;
	padding: 3px 10px;
}

.AuBaseTable :deep(.ag-floating-bottom) {
	overflow: hidden !important;
}

.AuBaseTable :deep(.ag-header) {
	background-color: #262626;
}

.AuBaseTable :deep(.ag-header-cell) {
	border-right: 1px solid hsl(0, 0%, 23%) !important;
	color: #ffffffd1;
	padding: 5px 10px;
}

.AuBaseTable :deep(.ag-horizontal-left-spacer) {
	overflow: hidden !important;
}

.AuBaseTable :deep(.ag-pinned-left-cols-container) {
	border-right: 1px solid hsl(0, 0%, 23%) !important;
}

.AuBaseTable :deep(.ag-row-even) {
	border-bottom: 1px solid hsl(0, 0%, 23%) !important;
	background-color: hsl(203, 6%, 9%) !important;
}

.AuBaseTable :deep(.ag-row-odd) {
	border-bottom: 1px solid hsl(0, 0%, 23%) !important;
	background-color: hsl(183, 6%, 11%) !important;
}

.AuBaseTable :deep(.ag-root-wrapper) {
	border-radius: 3px;
	border-color: #2d2d30;
	border-top: 1px solid hsl(0, 0%, 23%) !important;
	border-bottom: 1px solid hsl(0, 0%, 23%) !important;
	color: #ffffffd1;
	font-size: 14px;
	font-weight: 500;
	line-height: 22.4px;
}
</style>
