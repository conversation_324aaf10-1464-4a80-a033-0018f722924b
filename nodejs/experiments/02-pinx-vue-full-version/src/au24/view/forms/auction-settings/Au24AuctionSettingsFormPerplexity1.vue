<template>
    <n-form
            ref="formRef"
            :model="formValue"
            :rules="rules"
            label-placement="left"
            label-width="220"
            require-mark-placement="right-hanging"
            :show-feedback="false"
            size="small"
    >
        <n-grid :cols="24" :x-gap="24">
            <n-grid-item :span="24">
                <n-form-item label="Auction Name" path="auction_name">
                    <n-input v-model:value="formValue.auction_name" placeholder="Enter auction name" />
                </n-form-item>
            </n-grid-item>

            <n-grid-item :span="12">
                <n-form-item label="Cost Multiplier" path="cost_multiplier">
                    <n-input-number
                            v-model:value="formValue.cost_multiplier"
                            placeholder="Enter cost multiplier"
                            :precision="2"
                    />
                </n-form-item>
            </n-grid-item>

            <n-grid-item :span="12">
                <n-form-item label="Price Decimal Places" path="price_decimal_places">
                    <n-input-number
                            v-model:value="formValue.price_decimal_places"
                            placeholder="Enter decimal places"
                            :min="0"
                            :max="10"
                    />
                </n-form-item>
            </n-grid-item>

            <!-- Excess Level Settings -->
            <n-grid-item :span="24">
                <n-divider>Excess Level Settings</n-divider>
            </n-grid-item>

            <template v-for="level in 5" :key="level">
                <n-grid-item :span="12">
                    <n-form-item :label="`Excess Level ${level-1} Label`" :path="`excess_level_${level-1}_label`">
                        <n-input v-model:value="formValue[`excess_level_${level-1}_label`]" />
                    </n-form-item>
                </n-grid-item>
                <n-grid-item :span="12" v-if="level > 0">
                    <n-form-item :label="`Excess Level ${level-1} Quantity`" :path="`excess_level_${level-1}_quantity`">
                        <n-input-number v-model:value="formValue[`excess_level_${level-1}_quantity`]" />
                    </n-form-item>
                </n-grid-item>
            </template>

            <!-- Price Settings -->
            <n-grid-item :span="24">
                <n-divider>Price Settings</n-divider>
            </n-grid-item>

            <n-grid-item :span="12">
                <n-form-item label="Price Change Initial" path="price_change_initial">
                    <n-input-number v-model:value="formValue.price_change_initial" />
                </n-form-item>
            </n-grid-item>

            <n-grid-item :span="12">
                <n-form-item label="Price Change Post Reversal" path="price_change_post_reversal">
                    <n-input-number v-model:value="formValue.price_change_post_reversal" />
                </n-form-item>
            </n-grid-item>

            <!-- Labels -->
            <n-grid-item :span="12">
                <n-form-item label="Price Label" path="price_label">
                    <n-input v-model:value="formValue.price_label" />
                </n-form-item>
            </n-grid-item>

            <n-grid-item :span="12">
                <n-form-item label="Quantity Label" path="quantity_label">
                    <n-input v-model:value="formValue.quantity_label" />
                </n-form-item>
            </n-grid-item>

            <!-- Quantity Settings -->
            <n-grid-item :span="12">
                <n-form-item label="Quantity Minimum" path="quantity_minimum">
                    <n-input-number v-model:value="formValue.quantity_minimum" />
                </n-form-item>
            </n-grid-item>

            <n-grid-item :span="12">
                <n-form-item label="Quantity Step" path="quantity_step">
                    <n-input-number v-model:value="formValue.quantity_step" />
                </n-form-item>
            </n-grid-item>

            <!-- Round Settings -->
            <n-grid-item :span="24">
                <n-divider>Round Settings</n-divider>
            </n-grid-item>

            <n-grid-item :span="12">
                <n-form-item label="Round Closed Min Seconds" path="round_closed_min_secs">
                    <n-input-number v-model:value="formValue.round_closed_min_secs" :min="0" />
                </n-form-item>
            </n-grid-item>

            <n-grid-item :span="12">
                <n-form-item label="Round Open Min Seconds" path="round_open_min_secs">
                    <n-input-number v-model:value="formValue.round_open_min_secs" :min="0" />
                </n-form-item>
            </n-grid-item>

            <n-grid-item :span="12">
                <n-form-item label="Round Orange Seconds" path="round_orange_secs">
                    <n-input-number v-model:value="formValue.round_orange_secs" :min="0" />
                </n-form-item>
            </n-grid-item>

            <n-grid-item :span="12">
                <n-form-item label="Round Red Seconds" path="round_red_secs">
                    <n-input-number v-model:value="formValue.round_red_secs" :min="0" />
                </n-form-item>
            </n-grid-item>

            <!-- Time Settings -->
            <n-grid-item :span="12">
                <n-form-item label="Starting Price Announcement (mins)" path="starting_price_announcement_mins">
                    <n-input-number v-model:value="formValue.starting_price_announcement_mins" :min="0" />
                </n-form-item>
            </n-grid-item>

            <n-grid-item :span="12">
                <n-form-item label="Starting Time" path="starting_time">
                    <n-date-picker
                            v-model:value="formValue.starting_time"
                            type="datetime"
                            clearable
                    />
                </n-form-item>
            </n-grid-item>

            <n-grid-item :span="24">
                <n-form-item label="Use Counterparty Credits" path="use_counterparty_credits">
                    <n-switch v-model:value="formValue.use_counterparty_credits" />
                </n-form-item>
            </n-grid-item>
        </n-grid>
    </n-form>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import type { FormInst, FormRules } from 'naive-ui'

interface DeSettingsValue {
    auction_name: string
    cost_multiplier: string
    excess_level_0_label: string
    excess_level_1_label: string
    excess_level_1_quantity: string
    excess_level_2_label: string
    excess_level_2_quantity: string
    excess_level_3_label: string
    excess_level_3_quantity: string
    excess_level_4_label: string
    excess_level_4_quantity: string
    price_change_initial: string
    price_change_post_reversal: string
    price_decimal_places: number
    price_label: string
    quantity_label: string
    quantity_minimum: string
    quantity_step: string
    round_closed_min_secs: number
    round_open_min_secs: number
    round_orange_secs: number
    round_red_secs: number
    starting_price_announcement_mins: number
    starting_time: number | null
    use_counterparty_credits: boolean
}

const formRef = ref<FormInst | null>(null)
const formValue = ref<DeSettingsValue>({
    auction_name: '',
    cost_multiplier: '',
    excess_level_0_label: '',
    excess_level_1_label: '',
    excess_level_1_quantity: '',
    excess_level_2_label: '',
    excess_level_2_quantity: '',
    excess_level_3_label: '',
    excess_level_3_quantity: '',
    excess_level_4_label: '',
    excess_level_4_quantity: '',
    price_change_initial: '',
    price_change_post_reversal: '',
    price_decimal_places: 2,
    price_label: '',
    quantity_label: '',
    quantity_minimum: '',
    quantity_step: '',
    round_closed_min_secs: 0,
    round_open_min_secs: 0,
    round_orange_secs: 0,
    round_red_secs: 0,
    starting_price_announcement_mins: 0,
    starting_time: null,
    use_counterparty_credits: false
})

const rules: FormRules = {
    auction_name: {
        required: true,
        message: 'Please enter auction name',
        trigger: ['blur', 'input']
    },
    price_decimal_places: {
        required: true,
        type: 'number',
        message: 'Please enter decimal places',
        trigger: ['blur', 'input']
    }
    // Add more validation rules as needed
}

const handleValidateClick = (e: MouseEvent) => {
    e.preventDefault()
    formRef.value?.validate((errors) => {
        if (!errors) {
            console.log('Valid form data:', formValue.value)
        } else {
            console.log('Form validation failed:', errors)
        }
    })
}
</script>
