<template>
	<div ref="chartContainer"></div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from "vue"
import * as d3 from "d3"
import { hsl } from "d3-color"

export type OrderSide = "BUY" | "SELL"

export type TradingHistory = {
	trader_name: string
	initial_buy_quantity: number
	initial_sell_quantity: number
	rounds: Array<{
		round_num: number
		price: number
		order: {
			quantity: number
			side: OrderSide
		}
		quantity_constraints: {
			max_buy: number
			min_buy: number
			max_sell: number
			min_sell: number
		}
	}>
}

const props = defineProps<{
	data: TradingHistory
	initial_buy_quantity: number
	initial_sell_quantity: number
	width?: number
	height?: number
	marginTop?: number
	marginRight?: number
	marginBottom?: number
	marginLeft?: number
}>()

const sell_hue = 0 // Red hue
const buy_hue = 120 // Green hue

const getColor = (side: OrderSide, level: "initial_constraint" | "round_constraint" | "order") => {
	const baseHue = side === "SELL" ? sell_hue : buy_hue

	switch (`${side}-${level}`) {
		case "SELL-initial_constraint":
			return hsl(baseHue, 0, 0).toString()
		// High lightness, high saturation
		case "SELL-round_constraint":
			return hsl(baseHue, 0.4, 0.3).toString()
		// Medium lightness, higher saturation
		case "SELL-order":
			return hsl(baseHue, 0.8, 0.5).toString()
		// Lower lightness, highest saturation

		case "BUY-initial_constraint":
			return hsl(baseHue, 0, 0).toString()
		// High lightness, medium saturation
		case "BUY-round_constraint":
			return hsl(baseHue, 0.4, 0.3).toString()
		// Medium lightness, higher saturation
		case "BUY-order":
			return hsl(baseHue, 0.8, 0.5).toString()
		// Lower lightness, highest saturation

		default:
			console.log(side, level)
			return "white"
	}
}

const chartContainer = ref<HTMLElement | null>(null)

const drawChart = () => {
	if (!chartContainer.value) return

	const width = props.width || 400
	const height = props.height || 400
	const marginTop = props.marginTop || 20
	const marginRight = props.marginRight || 20
	const marginBottom = props.marginBottom || 40
	const marginLeft = props.marginLeft || 60

	d3.select(chartContainer.value).selectAll("*").remove()

	const svg = d3.select(chartContainer.value).append("svg").attr("width", width).attr("height", height)

	// Define the hatched pattern
	const defs = svg.append("defs")

	const pattern = defs
		.append("pattern")
		.attr("id", "hatched-pattern")
		.attr("width", 10)
		.attr("height", 10)
		.attr("patternUnits", "userSpaceOnUse")
		.attr("patternTransform", "rotate(45)")

	pattern.append("rect").attr("width", 10).attr("height", 10).attr("fill", "#fff")

	pattern
		.append("line")
		.attr("x1", 0)
		.attr("y1", 0)
		.attr("x2", 10)
		.attr("y2", 10)
		.attr("stroke", "#000")
		.attr("stroke-width", 2)

	const rounds = props.data.rounds.map(d => d.round_num.toString())
	console.log("Categories (Rounds):", rounds)

	const x = d3
		.scaleBand()
		.domain(rounds)
		.range([marginLeft, width - marginRight])
		.padding(0.03)

	console.log("ScaleBand domain:", x.domain())
	console.log("ScaleBand range:", x.range())
	console.log("Band width:", x.bandwidth())
	console.log("Step size (including padding):", x.step())

	const y = d3
		.scaleLinear()
		.domain([-props.initial_buy_quantity, props.initial_sell_quantity])
		.range([height - marginBottom, marginTop])

	// (1) AXES:

	const xAxis = (g: d3.Selection<SVGGElement, unknown, null, undefined>) =>
		g
			.attr("transform", `translate(0,${height - marginBottom})`)
			.call(d3.axisBottom(x).tickFormat(d => `Round ${d}`))

	const yAxis = (g: d3.Selection<SVGGElement, unknown, null, undefined>) =>
		g
			.attr("transform", `translate(${marginLeft},0)`)
			.call(d3.axisLeft(y).tickFormat(d => Math.abs(Number(d)).toString()))
			.call(g => g.select(".domain").remove())
			.call(g =>
				g
					.selectAll(".tick line")
					.clone()
					.attr("x2", width - marginLeft - marginRight)
					.attr("stroke-opacity", 0.1)
			)

	svg.append("g").call(xAxis)
	svg.append("g").call(yAxis)

	// (2) Add center line
	svg.append("line")
		.attr("x1", marginLeft)
		.attr("x2", width - marginRight)
		.attr("y1", y(0))
		.attr("y2", y(0))
		.attr("stroke", "#555")
		.attr("stroke-width", 1)

	// QUANTITIES:

	// (3) Draw initial quantity bars

	const initialQuantityGroup = svg.append("g")

	// Initial buy quantity bars
	initialQuantityGroup
		.selectAll(".initial-buy-bar")
		.data(props.data.rounds)
		.join("rect")
		.attr("class", "initial-buy-bar")
		.attr("x", d => x(d.round_num.toString()) || 0)
		.attr("y", y(0))
		.attr("width", x.bandwidth())
		.attr("height", y(-50) - y(0))
		.attr("fill", () => getColor("BUY", "initial_constraint"))

	// Initial sell quantity bars
	initialQuantityGroup
		.selectAll(".initial-sell-bar")
		.data(props.data.rounds)
		.join("rect")
		.attr("class", "initial-sell-bar")
		.attr("x", d => x(d.round_num.toString()) || 0)
		.attr("y", y(50))
		.attr("width", x.bandwidth())
		.attr("height", y(0) - y(50))
		.attr("fill", () => getColor("SELL", "initial_constraint"))

	// (4) Draw bars
	const barGroup = svg.append("g")

	barGroup
		.selectAll(".sell-bar")
		.data(props.data.rounds)
		.join("rect")
		.attr("class", "sell-bar")
		.attr("x", d => x(d.round_num.toString()) || 0)
		.attr("y", d => y(d.quantity_constraints.max_sell))
		.attr("width", x.bandwidth())
		.attr("height", d => Math.abs(y(d.quantity_constraints.max_sell) - y(d.quantity_constraints.min_sell)))
		.attr("fill", "url(#hatched-pattern)")

	// Draw buy bars (corrected as per user's solution)
	barGroup
		.selectAll(".buy-bar")
		.data(props.data.rounds)
		.join("rect")
		.attr("class", "buy-bar")
		.attr("x", d => x(d.round_num.toString()) || 0)
		.attr("y", d => y(-d.quantity_constraints.min_buy))
		.attr("width", x.bandwidth())
		.attr("height", d => Math.abs(y(-d.quantity_constraints.min_buy) - y(-d.quantity_constraints.max_buy)))
		.attr("fill", () => getColor("BUY", "round_constraint"))

	const orderGroup = svg.append("g")

	// orderGroup.selectAll(".order-bar")
	// 	.data(props.data.rounds)
	// 	.join("rect")
	// 	.attr("class", "order-bar")
	// 	.attr("x", d => x(d.round_num.toString()) || 0)
	// 	.attr("y", d => d.order.side === "SELL" ? y(d.order.quantity) : y(0))
	// 	.attr("width", x.bandwidth())
	// 	.attr("height", d => Math.abs(y(d.order.side === "SELL" ? d.order.quantity : -d.order.quantity) - y(0)))
	// 	.attr("fill", d => getColor(d.order.side, "order"))
	// // Full opacity for the order

	// Adjust the opacity of constraint bars
	// 	barGroup.selectAll(".sell-bar, .buy-bar")
	// 		.attr("opacity", 0.30)  // Reduce opacity for constraints

	// Ensure the order bars are drawn on top of the constraint bars
	svg.node()?.appendChild(orderGroup.node()!)
}

onMounted(() => {
	console.log({ data: props.data })
	console.table(props.data.rounds.map(d => d.quantity_constraints))
	drawChart()
})

watch(
	() => props.data,
	() => {
		drawChart()
	},
	{ deep: true }
)
</script>
<!--<style scoped>-->
<!--:deep(g .sell-bar) {-->
<!--	fill: blue !important;-->
<!--}-->
<!--</style>-->
