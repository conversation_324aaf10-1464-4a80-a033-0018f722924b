import { glob } from 'bun';
import { readdir, readFile, writeFile } from 'bun:fs';

interface Args {
  patterns: string[];
  filePatterns: string[];
  outputFile: string;
  showHelp: boolean;
}

function parseArgs(argv: string[]): Args {
  const args: Args = { patterns: [], filePatterns: [], outputFile: '', showHelp: false };

  for (let i = 2; i < argv.length; i++) {
    const arg = argv[i];
    if (arg === '-f') {
      if (i + 1 < argv.length) {
        args.filePatterns.push(argv[i + 1]);
        i++;
      } else {
        throw new Error('Error: -f flag requires a filename. Use -h for usage.');
      }
    } else if (arg === '-o') {
      if (i + 1 < argv.length) {
        if (args.outputFile !== '') {
          throw new Error('Error: Multiple output files specified. Use -h for usage.');
        }
        args.outputFile = argv[i + 1];
        i++;
      } else {
        throw new Error('Error: -o flag requires a filename. Use -h for usage.');
      }
    } else if (arg === '-h' || arg === '--help') {
      args.showHelp = true;
    } else {
      args.patterns.push(arg);
    }
  }
  return args;
}

async function readPatternsFromFile(filePath: string): Promise<string[]> {
  try {
    const data = await readFile(filePath, 'utf8');
    return data.split(/\r?\n/).map(line => line.trim()).filter(line => line && !line.startsWith('#'));
  } catch (err) {
    throw new Error(`Error: Cannot read file "${filePath}". Use -h for usage.`);
  }
}

async function main() {
  try {
    const { patterns, filePatterns, outputFile, showHelp } = parseArgs(import.meta.argv);

    if (showHelp || import.meta.argv.length <= 2) {
      console.log(`
Usage:
  bun file-matcher.ts <patterns...> [-f <pattern file>] -o <output file>

Options:
  -f <pattern file>    Specify a file containing glob patterns (one per line).
  -o <output file>     Specify the output file to write results.
  -h, --help           Display this help message.

Examples:
  bun file-matcher.ts src/**/*.{vue,ts} -f globpatterns.txt -o output.md
  bun file-matcher.ts -h
`);
      import.meta.exit(0);
    }

    if (!outputFile) {
      console.error('Error: Output file not specified. Use the -o flag to specify an output file.');
      import.meta.exit(1);
    }

    let allPatterns = [...patterns];

    if (filePatterns.length > 0) {
      for (const filePath of filePatterns) {
        const fileContentPatterns = await readPatternsFromFile(filePath);
        allPatterns = allPatterns.concat(fileContentPatterns);
      }
    }

    if (allPatterns.length === 0) {
      console.error('Error: No glob patterns specified. Use -h for usage.');
      import.meta.exit(1);
    }

    const inclusionPatterns = [];
    const exclusionPatterns = [];

    for (const pattern of allPatterns) {
      if (pattern.startsWith('!')) {
        exclusionPatterns.push(pattern.slice(1));
      } else {
        inclusionPatterns.push(pattern);
      }
    }

    const matchedFilesSet = new Set();

    for (const inclusionPattern of inclusionPatterns) {
      for await (const file of glob(inclusionPattern)) {
        let included = true;
        for (const exclusionPattern of exclusionPatterns) {
          if (await glob(exclusionPattern).then(files => files.includes(file))) {
            included = false;
            break;
          }
        }
        if (included) {
          matchedFilesSet.add(file);
        }
      }
    }

    const matchedFiles = Array.from(matchedFilesSet);
    matchedFiles.sort();

    console.log(`found ${matchedFiles.length} files:`);
    for (const file of matchedFiles) {
      console.log(`- ${file}`);
    }

    let outputContent = `found ${matchedFiles.length} files:\n`;
    for (const file of matchedFiles) {
      outputContent += `- ${file}\n`;
    }

    for (const file of matchedFiles) {
      try {
        const content = await readFile(file, 'utf8');
        outputContent += `\n${file}\n\`\`\`\`\n${content}\n\`\`\`\`\n`;
      } catch (err) {
        console.error(`Error reading file "${file}": ${err.message}.`);
      }
    }

    try {
      await writeFile(outputFile, outputContent, 'utf8');
      console.log(`Output successfully written to "${outputFile}"`);
    } catch (err) {
      console.error(`Error writing to file "${outputFile}": ${err.message}`);
      import.meta.exit(1);
    }
  } catch (err) {
    console.error(err.message);
    import.meta.exit(1);
  }
}

main();
import { Glob } from "bun";
import { readFileSync, writeFileSync } from 'fs';
import { relative, dirname } from 'path';

const main = async () => {
    // Check if correct number of arguments are provided
    if (process.argv.length !== 3) {
        console.error('Usage: bun run script.js <glob-pattern>');
        process.exit(1);
    }

    const pattern = process.argv[2];

    try {
        // Create a new Glob instance
        const glob = new Glob(pattern);

        // Create the markdown table header
        let markdownTable = '| Path | Line Count |\n|------|------------|\n';

        // Array to store file information
        let fileInfos = [];

        // Scan for matching files
        for await (const file of glob.scan()) {
            const content = readFileSync(file, 'utf-8');
            const lineCount = content.split('\n').length;
            const basePath = dirname(pattern.split('*')[0]); // Extract base path from glob pattern
            const relativePath = relative(basePath, file);

            fileInfos.push({ path: relativePath, lineCount });
        }

        // Sort files alphabetically by path
        fileInfos.sort((a, b) => a.path.localeCompare(b.path));

        // Add sorted file info to the markdown table
        for (const { path, lineCount } of fileInfos) {
            markdownTable += `| ${path} | ${lineCount} |\n`;
        }

        // Write the markdown table to line-count.md
        writeFileSync('line-count.md', markdownTable);

        console.log('File statistics have been written to line-count.md');
    } catch (error) {
        console.error('An error occurred:', error.message);
        process.exit(1);
    }
};

main();