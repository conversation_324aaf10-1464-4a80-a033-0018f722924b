<template>
  <div>
    <!-- Auction Header -->
    <n-page-header @back="goBack">
      <template #title>
        <n-text strong class="text-xl">{{ auctionData?.name || `Auction ${id}` }}</n-text>
      </template>
      <template #subtitle>
        <n-ellipsis :line-clamp="1" :tooltip="false">{{ auctionData?.notice || 'No notice' }}</n-ellipsis>
      </template>
       <template #extra>
             <n-button size="small" @click="editNotice">Edit Notice</n-button>
        </template>
    </n-page-header>
    <n-divider />

    <!-- Main Grid -->
    <n-grid cols="1 m:3" responsive="screen" :x-gap="16" :y-gap="16">

      <!-- Status & Controls Column -->
      <n-gi span="1 m:3 l:1">
        <n-card title="Status & Controls" size="small">
          <n-descriptions label-placement="left" bordered :column="1" size="small" label-style="width: 150px">
            <n-descriptions-item label="Auctioneer State">
                <n-tag :type="getStateTagType(auctionData?.auctioneerState)" size="small">
                    {{ auctionData?.auctioneerStateText || auctionData?.auctioneerState || 'Loading...' }}
                </n-tag>
            </n-descriptions-item>
            <n-descriptions-item label="Common State">
                 <n-tag :type="getStateTagType(auctionData?.commonState)" size="small">
                    {{ auctionData?.commonStateText || auctionData?.commonState || 'Loading...' }}
                 </n-tag>
            </n-descriptions-item>
            <n-descriptions-item label="Current Round">{{ auctionData?.round || 'N/A' }}</n-descriptions-item>
            <n-descriptions-item label="Current Price">{{ auctionData?.price || 'N/A' }}</n-descriptions-item>
            <n-descriptions-item label="Time Remaining">{{ auctionData?.timeRemaining || 'N/A' }}</n-descriptions-item>
            <n-descriptions-item label="Excess (Qty / Level)">{{ auctionData?.excessQuantity }} / {{ auctionData?.excessLevel }} ({{ auctionData?.excessSide }})</n-descriptions-item>
            <n-descriptions-item label="Awardable?">{{ auctionData?.awardable ? 'Yes' : 'No' }}</n-descriptions-item>
            <n-descriptions-item label="Price Overshot?">{{ auctionData?.overshot ? 'Yes' : 'No' }}</n-descriptions-item>
            <n-descriptions-item label="Autopilot">{{ auctionData?.autopilot || 'Disengaged' }}</n-descriptions-item>
          </n-descriptions>

          <n-divider title-placement="left">Controls</n-divider>
          <n-space vertical>
             <n-input-group>
                <n-input-number
                    v-model:value="startingPrice"
                    placeholder="Starting Price"
                    :disabled="!controls.SET_STARTING_PRICE"
                    :show-button="false"
                    :step="0.001"
                    clearable
                    size="small"
                    style="min-width: 120px;"
                >
                     <template #prefix>$</template>
                </n-input-number>
                <n-button type="primary" size="small" :disabled="!controls.SET_STARTING_PRICE" @click="handleControl('SET_STARTING_PRICE')">Set Price</n-button>
            </n-input-group>
            <n-button block :disabled="!controls.ANNOUNCE_STARTING_PRICE" @click="handleControl('ANNOUNCE_STARTING_PRICE')">Announce Starting Price</n-button>
            <n-button block :disabled="!controls.START_AUCTION" @click="handleControl('START_AUCTION')">Start Auction</n-button>
            <n-button block :disabled="!controls.CLOSE_ROUND" @click="handleControl('CLOSE_ROUND')">Close Round</n-button>
            <n-button block :disabled="!controls.NEXT_ROUND" @click="handleControl('NEXT_ROUND')">Next Round</n-button>
            <n-button block :disabled="!controls.REOPEN_ROUND" @click="handleControl('REOPEN_ROUND')">Reopen Round</n-button>
            <n-button block type="error" :disabled="!controls.AWARD_AUCTION" @click="handleControl('AWARD_AUCTION')">Award Auction</n-button>
          </n-space>
        </n-card>

         <n-card title="Trader Status" size="small" class="mt-4">
             <n-list bordered clickable hoverable>
                <n-list-item v-for="t in traderStatusData" :key="t.id">
                    <template #prefix>
                        <n-avatar round size="small" :src="t.avatar" />
                    </template>
                    <n-thing :title="t.name">
                        <template #description>
                            <n-tag :type="t.online ? 'success' : 'error'" size="small" round class="mr-1">{{ t.online ? 'Online' : 'Offline' }}</n-tag>
                            <n-tag :type="getBidStatusTagType(t.bidStatus)" size="small" round>{{ t.bidStatus }}</n-tag>
                        </template>
                    </n-thing>
                     <template #suffix>
                        <!-- Add actions like force logout if needed -->
                    </template>
                </n-list-item>
                 <template #footer v-if="traderStatusData.length === 0">
                    No traders added yet.
                 </template>
             </n-list>
         </n-card>

         <n-card title="Messaging" size="small" class="mt-4">
             <n-list bordered class="mb-4 h-64 overflow-y-auto">
                <n-list-item v-for="(msg, index) in messagesData" :key="index">
                    <n-thing :title="msg.from" :description="msg.message" />
                    <template #suffix><n-text depth="3" class="text-xs">{{ msg.timestamp }}</n-text></template>
                </n-list-item>
                 <template #footer v-if="messagesData.length === 0">
                    No messages yet.
                 </template>
             </n-list>
             <n-input-group>
                <n-input v-model:value="newMessage" placeholder="Send broadcast message..." clearable />
                <n-button type="primary" @click="sendMessage">Send</n-button>
             </n-input-group>
         </n-card>

      </n-gi>

      <!-- Main Content Column -->
      <n-gi span="1 m:3 l:2">
         <n-card title="Blotter View" size="small">
             <p class="text-sm text-gray-500 mb-2">Round-by-round summary and individual bids.</p>
             <!-- Placeholder: Needs a more complex table or custom component -->
             <n-data-table
                :columns="blotterColumns"
                :data="blotterData"
                :pagination="false"
                size="small"
                max-height="400px"
             />
         </n-card>

         <n-card title="Matching Matrix (Current Round)" size="small" class="mt-4">
             <p class="text-sm text-gray-500 mb-2">Potential matches between buyers and sellers.</p>
             <!-- Placeholder: Needs a custom grid or complex table -->
             <n-text>Matching Matrix visualization placeholder.</n-text>
         </n-card>

         <n-card title="Award Summary" size="small" class="mt-4" v-if="auctionData?.isClosed">
             <p class="text-sm text-gray-500 mb-2">Final awarded quantities and prices.</p>
             <n-descriptions label-placement="left" bordered :column="3" size="small">
                <!-- Placeholder Award data -->
                <n-descriptions-item label="Awarded Round">{{ auctionData?.awardedRound || 'N/A' }}</n-descriptions-item>
                <n-descriptions-item label="Awarded Price">{{ auctionData?.awardedPrice || 'N/A' }}</n-descriptions-item>
                <n-descriptions-item label="Total Matched">{{ auctionData?.totalMatched || 'N/A' }}</n-descriptions-item>
                <!-- Add per-trader awards here, maybe using n-data-table -->
             </n-descriptions>
         </n-card>
      </n-gi>

    </n-grid>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { useRouter } from 'vue-router'
import {
    NPageHeader, NDivider, NGrid, NGi, NCard, NSpace, NButton, NInputGroup, NInputNumber, NInput,
    NList, NListItem, NDescriptions, NDescriptionsItem, NDataTable, NTag, NThing, NAvatar,
    useMessage, type DataTableColumns, type TagType
} from 'naive-ui'
import Icon from '@/components/common/Icon.vue'
// Import stores and types as needed

const props = defineProps<{ id: string }>()
const router = useRouter()
const message = useMessage()

const startingPrice = ref<number | null>(null)
const newMessage = ref<string>('');

// --- Placeholder Data (Replace with actual data from store/API reflecting DeAuctionValue) ---
const auctionData = ref<any>({
    name: `Auction ${props.id}`,
    notice: 'Round 1 is open for bidding.',
    auctioneerState: 'ROUND_OPEN_ALL_ORDERS_NOT_IN',
    auctioneerStateText: 'Round 1 open, waiting for bids...',
    commonState: 'ROUND_OPEN',
    commonStateText: 'Round 1 open for orders!',
    round: 1,
    price: '100.000',
    timeRemaining: '00:03:15',
    excessQuantity: 12,
    excessLevel: '++',
    excessSide: 'BUY',
    awardable: false,
    overshot: false,
    autopilot: 'Disengaged',
    isClosed: false,
    awardedRound: null,
    awardedPrice: null,
    totalMatched: null,
});

const controls = ref<Record<string, boolean>>({ // Simulate DeAuctioneerStatusValue.controls
    SET_STARTING_PRICE: false, // Can't set price after start
    ANNOUNCE_STARTING_PRICE: false,
    START_AUCTION: false,
    CLOSE_ROUND: true, // Can close the current open round
    NEXT_ROUND: false, // Can't go next until round is closed
    REOPEN_ROUND: false, // Can't reopen until round is closed
    AWARD_AUCTION: false, // Can't award until conditions met
});

const blotterColumns = ref<DataTableColumns<any>>([ // Define based on DeRoundElement/DeRoundTraderElement
    { title: 'Round', key: 'round', width: 60 },
    { title: 'Price', key: 'price', width: 100 },
    { title: 'Total Buy', key: 'buyQty', align: 'right' },
    { title: 'Total Sell', key: 'sellQty', align: 'right' },
    { title: 'Matched', key: 'matched', align: 'right' },
    { title: 'Excess', key: 'excess', render: (row) => `${row.excessQty} (${row.excessSide})` },
    // Add columns for individual bids if needed, or use a separate table/expandable rows
]);
const blotterData = ref([ // Populate with DeBlotter data
    { round: 1, price: '100.000', buyQty: 50, sellQty: 38, matched: 38, excessQty: 12, excessSide: 'BUY' },
]);

const traderStatusData = ref([ // Populate with DeTraderElement/UserElement data
    { id: 't1', name: 'Trader Alpha', avatar: `https://i.pravatar.cc/56?_=${Math.random()}`, online: true, bidStatus: 'Default' },
    { id: 't2', name: 'Trader Gamma', avatar: `https://i.pravatar.cc/56?_=${Math.random()}`, online: true, bidStatus: 'Manual Bid' },
    { id: 't3', name: 'Trader Delta', avatar: `https://i.pravatar.cc/56?_=${Math.random()}`, online: false, bidStatus: 'Offline' },
]);

const messagesData = ref([ // Populate with MessageElement data
    { from: 'Trader Gamma', message: 'What is the reserve price?', timestamp: '10:05 AM' },
    { from: 'Auctioneer', message: 'The reserve price will not be disclosed.', timestamp: '10:06 AM' },
]);
// --- End Placeholder Data ---

const getStateTagType = (state: string | undefined): TagType => {
    if (!state) return 'default';
    if (state.includes('Error') || state.includes('CLOSED')) return 'error';
    if (state.includes('Warning') || state.includes('WAITING')) return 'warning';
    if (state.includes('Success') || state.includes('AWARDABLE')) return 'success';
    if (state.includes('OPEN')) return 'info';
    return 'default';
};

const getBidStatusTagType = (status: string | undefined): TagType => {
     if (!status) return 'default';
     if (status === 'Manual Bid') return 'success';
     if (status === 'Default') return 'warning';
     if (status === 'Mandatory') return 'info';
     if (status === 'Offline') return 'error';
     return 'default';
}

onMounted(() => {
    console.log("Auction Control mounted for ID:", props.id);
    // Fetch auction data based on props.id using your store/API
    // auctionStore.fetchAuctionDetails(props.id).then(data => auctionData.value = data);
    // Setup WebSocket listener for real-time updates for this auction ID
});

const goBack = () => {
    router.push({ name: 'Dashboard' }); // Or previous route
}

const editNotice = () => {
    message.info("Edit Notice clicked (implement functionality)");
    // Implement modal or inline editing for auction notice
    // Call NoticeSaveCommand via store action
}

const handleControl = (controlType: string) => {
    message.loading(`Sending command: ${controlType}...`, { duration: 1000, key: 'control' });
    console.log(`Control action: ${controlType}`);
    if (controlType === 'SET_STARTING_PRICE') {
        if (startingPrice.value === null || startingPrice.value <= 0) {
             message.error('Please enter a valid starting price.', { key: 'control', duration: 3000 });
             return;
        }
        console.log(`Setting price to: ${startingPrice.value}`);
        // TODO: Call backend DeFlowControlCommand via store action with startingPrice.value
    } else {
        // TODO: Call backend DeFlowControlCommand via store action
    }
    // Simulate state update after a delay
    setTimeout(() => {
        message.success(`Command ${controlType} executed (simulated).`, { key: 'control', duration: 2000 });
        // TODO: Update auctionData and controls based on expected backend response/WebSocket update
    }, 1200);
}

const sendMessage = () => {
    if (!newMessage.value.trim()) return;
    message.success(`Sending message: ${newMessage.value}`);
    // TODO: Call backend MessageSendCommand via store action
    newMessage.value = '';
}

</script>

<style scoped>
.n-card {
    margin-bottom: 16px; /* Consistent spacing */
}
.n-descriptions {
    margin-bottom: 16px;
}
.n-list {
    background-color: var(--bg-secondary-color); /* Slightly different background for lists */
}
</style>