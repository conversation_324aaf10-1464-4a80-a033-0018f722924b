<template>
  <div>
    <n-empty :description="`Page not implemented: ${pageName}`">
      <template #extra>
        <n-button size="small" @click="goHome">Go to Home</n-button>
      </template>
    </n-empty>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { NEmpty, NButton } from 'naive-ui';
import { Connector, page_set_command, PageName } from 'au25-connector';
import {liveClientStore} from "../store/liveClientStore.ts";

const store = liveClientStore();
const pageName = computed(() => store.currentPage);

const goHome = () => {
  Connector.publish(page_set_command({ page: PageName.HOME_PAGE }));
};
</script>

<style scoped>
/* Styles for PlaceholderPage */
</style>
