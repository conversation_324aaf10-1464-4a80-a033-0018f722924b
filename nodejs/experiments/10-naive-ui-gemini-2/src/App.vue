<template>
  <n-config-provider :theme="naiveTheme">
    <n-message-provider>
      <n-dialog-provider>
        <n-loading-bar-provider>
          <div class="app-container">
            <component :is="currentView" />
          </div>
        </n-loading-bar-provider>
      </n-dialog-provider>
    </n-message-provider>
    <n-global-style />
  </n-config-provider>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, shallowRef, watch } from 'vue';
import {
  NConfigProvider,
  NGlobalStyle,
  NMessageProvider,
  NDialogProvider,
  NLoadingBarProvider,
  useMessage,
  useDialog,
  useLoadingBar,
  type GlobalTheme,
  darkTheme,
} from 'naive-ui';
import { liveClientStore } from './store/liveClientStore'; // Assuming store is in src/store
import { Connector, type ConnectorOptions, type ClientCommand, CommandType, PageName, type LiveClientStore, type StoreElement, type AddElements, type TerminateSession, type ShowMessage, BrowserMessageKind } from 'au25-connector';

import LoginPage from './pages/LoginPage.vue';
import MainLayout from './layouts/MainLayout.vue';
import { useThemeStore } from './store/themeStore'; // Assuming a theme store

// --- Store and Theme ---
const store = liveClientStore();
const themeStore = useThemeStore();
const naiveTheme = computed<GlobalTheme | null>(() => themeStore.isDark ? darkTheme : null);

// --- Naive UI Composables (for potential future use directly in App.vue if needed) ---
// const message = useMessage(); // Provided via NMessageProvider
// const dialog = useDialog();   // Provided via NDialogProvider
// const loadingBar = useLoadingBar(); // Provided via NLoadingBarProvider

// --- Connector Setup ---
const connectorInstance = ref<Connector | null>(null);
const connectionError = ref<string | null>(null);

const connectorOptions: ConnectorOptions = {
  // Use VITE env var or default; ensure it ends with /socket/
  // url: import.meta.env.VITE_WEBSOCKET_URL || `ws://${window.location.host}/socket/`,
  session_id: store.sessionId || crypto.randomUUID(), // Persist session ID if possible
  show_connector_log: import.meta.env.DEV, // Show logs in development
  clientCommandHandlers: {
    onSetLiveStore: (cmd) => {
      // console.log('Received SetLiveStore:', cmd);
      // store.apply_patch(cmd.store as LiveClientStore); // Type assertion might be needed
    },
    onAddElements: (cmd) => {
      console.warn('Received AddElements - Implement handler in store or component:', cmd);
      // Example: Find the target array in the store based on cmd.path and add/update elements
      // This often requires more complex logic, potentially in the store itself.
      // store.addOrUpdateElements(cmd.path, cmd.elements as StoreElement[]);
    },
    onCommandSucceeded: (cmd) => {
      // console.log('Command Succeeded');
      // Optionally provide feedback to the user
    },
    onShowMessage: (cmd) => {
      const msgCmd = cmd as ShowMessage; // Type assertion
      const naiveMessage = useMessage(); // Get instance here
      const content = msgCmd.message.join('\n');
      switch(msgCmd.browser_message_kind) {
        case BrowserMessageKind.ALERT:
          // Could use NDialog for alerts if more prominent display is needed
          naiveMessage.error(content, { duration: 5000, closable: true });
          break;
        case BrowserMessageKind.NOTIFICATION:
          naiveMessage.info(content, { duration: 3000 });
          break;
        default:
          naiveMessage.info(content);
      }
    },
    onTerminateSession: (cmd) => {
      const termCmd = cmd as TerminateSession; // Type assertion
      console.warn('Session Terminated by Server:', termCmd.message);
      const dialog = useDialog(); // Get instance here
      dialog.warning({
        title: 'Session Terminated',
        content: termCmd.message || 'Your session has been terminated by the server.',
        positiveText: 'OK',
        onPositiveClick: () => {
          // Reset store state related to logged-in user
          store.$reset(); // Or a more specific logout action
          // Optionally redirect to login or refresh
          window.location.reload();
        }
      });
      Connector.reset(); // Clean up the connector
    },
    onNetworkDown: (cmd) => {
      console.warn('Network Down reported by server');
      // Show UI indicator for network issues
    },
    onNetworkUp: (cmd) => {
      console.info('Network Up reported by server');
      // Hide UI indicator for network issues
    }
    // Add handlers for other CommandType values as needed
  },
  onError: (error) => {
    console.error('Connector Error:', error);
    connectionError.value = error.message || 'Connection failed';
    // Potentially use useMessage() here
  },
  onSecondsSinceLastMessage: (seconds) => {
    store.seconds_since_last_message_received = seconds;
    // Could show a warning if seconds get too high
  },
  onTerminate: (reason) => {
    console.warn('Connector Terminated:', reason);
    // Handle unexpected termination (e.g., browser unload)
  }
};

onMounted(() => {
  console.log('App.vue mounted, creating connector...');
  connectorInstance.value = Connector.create(connectorOptions);
});

onUnmounted(() => {
  console.log('App.vue unmounted, resetting connector...');
  Connector.reset(); // Clean up connector on component unmount
});

// --- View Logic ---
const currentView = computed(() => {
  // console.log(`isLoggedIn: ${store.isLoggedIn}, currentPage: ${store.currentPage}`);
  if (!store.isLoggedIn) {
    return LoginPage;
  }
  // If logged in, MainLayout handles rendering the correct page based on store.currentPage
  return MainLayout;
});

// Watch for session ID changes (e.g., after login) and update connector if needed
// This might be redundant if the session_id is correctly handled on initial create
watch(() => store.sessionId, (newSid, oldSid) => {
  if (newSid && newSid !== oldSid && newSid !== connectorOptions.session_id) {
    console.log(`Session ID changed from ${oldSid} to ${newSid}. Resetting connector.`);
    connectorOptions.session_id = newSid;
    Connector.reset(connectorOptions); // Recreate connector with new session ID
  }
});

</script>

<style>
/* Global styles if needed */
html, body, #app, .app-container {
  height: 100%;
  margin: 0;
  padding: 0;
  width: 100%;
}

.app-container {
  /* Add styles for the main container if necessary */
}
</style>
