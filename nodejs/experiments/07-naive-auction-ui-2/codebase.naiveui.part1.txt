=== src/stores/appStore.ts
import type {
	AuctionRowElement,
	CompanyElement,
	CounterpartyCreditElement,
	MessageElement,
	SessionUserValue,
	TimeValue,
	UserElement
} from "@/types/generated"; // Assuming generated types exist
import type { DeAuctionValue, DeMatrixRoundElement } from "@/types/generated"; // Assuming generated types exist
import { defineStore } from "pinia";

// Mirroring the backend's LiveClientStore structure
interface AppState {
	auction_rows: AuctionRowElement[];
	companies: CompanyElement[];
	counterparty_credits: CounterpartyCreditElement[];
	de_auction: DeAuctionValue | null;
	session_user: SessionUserValue | null;
	time: TimeValue | null;
	users: UserElement[];
	// Add a specific place for matrix rounds if needed frequently outside de_auction
	de_matrix_rounds: DeMatrixRoundElement[];
	messages: MessageElement[]; // Assuming messages might be added incrementally
	websocketConnected: boolean;
	lastMessageTimestamp: number | null;
}

export const useAppStore = defineStore("app", {
	state: (): AppState => ({
		auction_rows: [],
		companies: [],
		counterparty_credits: [],
		de_auction: null,
		session_user: null,
		time: null,
		users: [],
		de_matrix_rounds: [],
		messages: [],
		websocketConnected: false,
		lastMessageTimestamp: null,
	}),

	actions: {
		setLiveStore(storeData: Partial<AppState>) {
			// Deep merge might be better, but direct assignment matches backend push
			// Consider using lodash merge or similar if partial updates are expected
			// within SetLiveStore, otherwise, this assumes a full state push.
			if (storeData.auction_rows !== undefined) this.auction_rows = storeData.auction_rows;
			if (storeData.companies !== undefined) this.companies = storeData.companies;
			if (storeData.counterparty_credits !== undefined) this.counterparty_credits = storeData.counterparty_credits;
			if (storeData.de_auction !== undefined) this.de_auction = storeData.de_auction;
			if (storeData.session_user !== undefined) this.session_user = storeData.session_user;
			if (storeData.time !== undefined) this.time = storeData.time;
			if (storeData.users !== undefined) this.users = storeData.users;

			// If messages/matrix rounds are part of LiveClientStore, update them here too.
			// If they are only updated via AddElements, handle them there.
			if (storeData.de_auction?.messages) {
				this.messages = storeData.de_auction.messages;
			}
			if (storeData.de_auction?.matrix_last_round) {
				// Assuming only the last round is sent in the main store push
				this.de_matrix_rounds = [storeData.de_auction.matrix_last_round];
			}

			console.log("LiveStore updated"); // Reduced verbosity
		},

		addElements(path: string, elements: any[] | null) {
			console.log(`AddElements received for path: ${path}`, elements ? `${elements.length} elements` : 'null (clear)');
			if (elements === null) {
				// Handle clearing the array
				if (path === "AuctionRowElement") this.auction_rows = [];
				else if (path === "CompanyElement") this.companies = [];
				else if (path === "CounterpartyCreditElement") this.counterparty_credits = [];
				else if (path === "MessageElement") this.messages = [];
				else if (path === "UserElement") this.users = [];
				else if (path === "DeMatrixRoundElement") this.de_matrix_rounds = [];
				// Add other paths as needed
				else console.warn(`AddElements: Unknown path to clear: ${path}`);
				return;
			}

			if (!Array.isArray(elements)) {
				console.error("AddElements: received non-array elements", elements);
				return;
			}

			// Find the target array and update/add elements
			let targetArray: any[] | undefined;
			if (path === "AuctionRowElement") targetArray = this.auction_rows;
			else if (path === "CompanyElement") targetArray = this.companies;
			else if (path === "CounterpartyCreditElement") targetArray = this.counterparty_credits;
			else if (path === "MessageElement") targetArray = this.messages;
			else if (path === "UserElement") targetArray = this.users;
			else if (path === "DeMatrixRoundElement") targetArray = this.de_matrix_rounds;
			// Add other paths as needed

			if (targetArray) {
				elements.forEach(newElement => {
					if (!newElement || typeof newElement.id === 'undefined') {
						console.warn("AddElements: Skipping element without id", newElement);
						return;
					}
					const index = targetArray.findIndex(item => item.id === newElement.id);
					if (index !== -1) {
						// Update existing element (important for reactivity)
						// Use spread or Object.assign for better reactivity handling if needed
						targetArray[index] = { ...targetArray[index], ...newElement };
					} else {
						// Add new element
						targetArray.push(newElement);
					}
				});
				// Optional: Sort arrays if needed after updates
				if (path === 'MessageElement') this.messages.sort((a, b) => a.timestamp - b.timestamp);
				if (path === 'DeMatrixRoundElement') this.de_matrix_rounds.sort((a, b) => a.round_number - b.round_number);

			} else {
				console.warn(`AddElements: Unknown path: ${path}`);
			}
		},

		setWebsocketConnected(status: boolean) {
			this.websocketConnected = status;
		},

		setLastMessageTimestamp(ts: number) {
			this.lastMessageTimestamp = ts;
		},

		resetState() {
			this.$reset();
			console.log("AppStore state reset");
		}
	},
	getters: {
		isAuctioneer: (state) => state.session_user?.isAuctioneer ?? false,
		isTrader: (state) => !state.session_user?.isAuctioneer && !!state.session_user?.role, // Assuming non-auctioneer logged in is trader
		currentSessionId: (state) => state.session_user?.session_id,
		currentPage: (state) => state.session_user?.current_page,
		currentAuctionId: (state) => state.session_user?.current_auction_id,
		// Add more specific getters as needed e.g., getTraderInfo, getAuctioneerStatus
		getDeAuctioneerStatus: (state) => state.de_auction?.auctioneer_status,
		getDeCommonStatus: (state) => state.de_auction?.common_status,
		getDeTraderInfo: (state) => state.de_auction?.trader_info,
		getDeTraderHistory: (state) => state.de_auction?.trader_history_rows ?? [],
		getDeBlotter: (state) => state.de_auction?.blotter,
		getDeMatrixLastRound: (state) => state.de_auction?.matrix_last_round,
		getMessagesForCurrentAuction: (state) => {
			// Filter messages based on current auction context if needed,
			// or assume messages in the store are already filtered by backend push
			// Ensure sorting happens here if AddElements doesn't guarantee order
			return [...state.messages].sort((a, b) => a.timestamp - b.timestamp);
		}
	}
});

=== src/services/websocket.ts
import { useAppStore } from "@/stores/appStore";
import { useAuthStore } from "@/stores/auth";
import { useMainStore } from "@/stores/main";
import { useGlobalActions } from "@/composables/useGlobalActions";
import { ref, watch } from "vue";
import { i18nGlobal } from "@/lang"; // Assuming i18n setup for messages

// Assuming generated types exist
import type { ClientCommand, EngineCommand, EngineCommandEnvelope } from "@/types/generated";
import { CommandType, BrowserMessageKind } from "@/types/generated";

// Make WS URL configurable via environment variables
const WS_BASE_URL = import.meta.env.VITE_WS_URL || 'ws://localhost:8080/socket'; // Default fallback

let websocket: WebSocket | null = null;
let reconnectAttempts = 0;
const maxReconnectAttempts = 5;
const reconnectInterval = 5000; // 5 seconds
let reconnectTimeout: NodeJS.Timeout | null = null;
let currentSessionId = ref<string | null>(null); // Track the session ID we *should* be connected with

export function useWebSocket() {
	const appStore = useAppStore();
	const authStore = useAuthStore();
	const mainStore = useMainStore();
	const { message: showMessage, notification: showNotification } = useGlobalActions(); // Assuming global setup

	// Watch for session ID changes to connect/disconnect
	// This is the primary trigger for connection management
	watch(() => appStore.currentSessionId, (newSid, oldSid) => {
		console.log(`[WS] Session ID watcher: changed from ${oldSid} to ${newSid}`);
		if (newSid && newSid !== currentSessionId.value) {
			console.log(`[WS] New session ID detected (${newSid}), initiating connection.`);
			currentSessionId.value = newSid; // Update the target session ID
			disconnectWebSocket(false); // Disconnect old if exists, don't trigger reconnect logic
			connectWebSocket(newSid);
		} else if (!newSid && currentSessionId.value) {
			console.log("[WS] Session ID removed, disconnecting.");
			currentSessionId.value = null; // Clear target session ID
			disconnectWebSocket(false); // Disconnect cleanly
		}
	}, { immediate: true });

	// Watch for login status changes (secondary check, mainly for logout)
	watch(() => authStore.isLogged, (loggedIn) => {
		console.log(`[WS] Auth state watcher: loggedIn=${loggedIn}`);
		if (!loggedIn && currentSessionId.value) {
			console.log("[WS] User logged out, disconnecting WebSocket.");
			currentSessionId.value = null; // Clear target session ID
			disconnectWebSocket(false); // Clean disconnect on logout
			appStore.resetState(); // Clear app state on logout
		}
		// Connection on login is handled by the session_id watch
	});

	function getWebSocketURL(sessionId: string): string {
		// Add browser info as query params, matching backend expectations
		const browserInfo = {
			browser_name: navigator.userAgentData?.brands?.[0]?.brand || navigator.vendor || "Unknown",
			browser_version: navigator.userAgentData?.brands?.[0]?.version || navigator.appVersion || "Unknown",
			browser_os: navigator.platform || "Unknown",
		};
		const queryParams = new URLSearchParams(browserInfo).toString();
		const url = `${WS_BASE_URL}/${sessionId}`; // Use template literal
		return `${url}?${queryParams}`;
	}

	function connectWebSocket(sessionId: string) {
		if (!sessionId) {
			console.error("[WS] Cannot connect: No Session ID provided.");
			return;
		}
		// Prevent multiple connections for the same session ID
		if (websocket && websocket.readyState !== WebSocket.CLOSED && websocket.url.includes(sessionId)) {
			console.log(`[WS] Already connected or connecting to session ${sessionId}.`);
			return;
		}
		// If a WS exists for a *different* session, disconnect it first
		if (websocket && websocket.readyState !== WebSocket.CLOSED && !websocket.url.includes(sessionId)) {
			console.warn(`[WS] Disconnecting obsolete WebSocket for session ${websocket.url.split('/').pop()?.split('?')[0]}`);
			disconnectWebSocket(false);
		}


		const url = getWebSocketURL(sessionId);
		console.log(`[WS] Connecting to: ${url}`);
		appStore.setWebsocketConnected(false);
		mainStore.loadingBar?.start();

		try {
			websocket = new WebSocket(url);
		} catch (error) {
			console.error("[WS] Failed to create WebSocket:", error);
			mainStore.loadingBar?.error();
			scheduleReconnect(sessionId); // Attempt reconnect even if creation fails
			return;
		}


		websocket.onopen = () => {
			// Double check if this is still the desired session before proceeding
			if (currentSessionId.value !== sessionId) {
				console.warn(`[WS] Connection opened for ${sessionId}, but desired session is now ${currentSessionId.value}. Closing.`);
				disconnectWebSocket(false);
				return;
			}
			console.log(`[WS] Connection established for session ${sessionId}.`);
			appStore.setWebsocketConnected(true);
			reconnectAttempts = 0; // Reset attempts on successful connection
			clearTimeout(reconnectTimeout!);
			mainStore.loadingBar?.finish();
			// Backend should send ClientSocketCommand(OPENED) and then push initial state
		};

		websocket.onmessage = (event) => {
			// Ensure this message is for the *current* session
			if (currentSessionId.value !== sessionId) {
				console.warn(`[WS] Received message for obsolete session ${sessionId}. Ignoring.`);
				return;
			}
			appStore.setLastMessageTimestamp(Date.now());
			// Assume backend sends gzipped binary data - Placeholder for ungzip
			if (event.data instanceof Blob) {
				const reader = new FileReader();
				reader.onload = function () {
					try {
						const arrayBuffer = reader.result as ArrayBuffer;
						// *** Placeholder for ungzip ***
						// Replace this with actual ungzip logic using pako or similar
						console.warn("[WS] Binary message received, ungzip not implemented. Decoding as UTF-8 text.");
						const textDecoder = new TextDecoder("utf-8");
						handleMessage(textDecoder.decode(arrayBuffer));
						// *** End Placeholder ***

					} catch (error) {
						console.error("[WS] Error processing binary message:", error);
						mainStore.loadingBar?.error();
					}
				};
				reader.onerror = (error) => {
					console.error("[WS] Error reading Blob:", error);
					mainStore.loadingBar?.error();
				};
				reader.readAsArrayBuffer(event.data);
			} else if (typeof event.data === 'string') {
				// Handle plain text messages
				// console.log("[WS] Received Text Message (raw):", event.data.substring(0, 200) + "...");
				handleMessage(event.data);
			} else {
				console.warn("[WS] Received unexpected message type:", typeof event.data);
			}
		};

		websocket.onerror = (error) => {
			console.error(`[WS] Error for session ${sessionId}:`, error);
			// Only attempt reconnect if this is the currently active session
			if (currentSessionId.value === sessionId) {
				appStore.setWebsocketConnected(false);
				mainStore.loadingBar?.error();
				showMessage(i18nGlobal.t('errors.websocketConnection'), { type: 'error', duration: 0, closable: true });
				scheduleReconnect(sessionId);
			} else {
				console.log(`[WS] Ignoring error for obsolete session ${sessionId}.`);
			}
		};

		websocket.onclose = (event) => {
			console.log(`[WS] Connection closed for session ${sessionId}: Code=${event.code}, Reason=${event.reason}, Clean=${event.wasClean}`);
			// Only manage state and reconnect if this was the *intended* active connection
			if (currentSessionId.value === sessionId) {
				appStore.setWebsocketConnected(false);
				websocket = null; // Clear the reference only if it's the current one closing

				// Reconnect only if:
				// 1. It wasn't a clean close (code 1000 is normal) OR it was closed unexpectedly (e.g., server restart 1006)
				// 2. The user is still supposed to be logged in (authStore.isLogged)
				// 3. This session ID is still the one we want (currentSessionId.value === sessionId)
				if ((!event.wasClean || event.code === 1006) && authStore.isLogged) {
					console.log("[WS] Unclean close or server issue, attempting reconnect...");
					showMessage(i18nGlobal.t('errors.websocketDisconnected'), { type: 'warning' });
					mainStore.loadingBar?.error();
					scheduleReconnect(sessionId);
				} else {
					console.log("[WS] Clean close or user logged out/session changed, not reconnecting.");
					mainStore.loadingBar?.finish(); // Finish loading bar if closed cleanly
				}
			} else {
				console.log(`[WS] Ignoring close event for obsolete session ${sessionId}.`);
				// If the closed websocket is the one stored in the global `websocket` variable, clear it
				if (websocket === event.target) {
					websocket = null;
				}
			}
		};
	}

	function scheduleReconnect(sessionId: string) {
		// Only schedule if this is still the desired session
		if (currentSessionId.value !== sessionId) {
			console.log(`[WS] Reconnect cancelled for ${sessionId}, desired session is ${currentSessionId.value}.`);
			return;
		}

		if (reconnectAttempts >= maxReconnectAttempts) {
			console.error("[WS] Max reconnect attempts reached. Giving up.");
			showMessage(i18nGlobal.t('errors.websocketReconnectFailed'), { type: 'error', duration: 0, closable: true });
			// Optional: Force logout or redirect to login
			// authStore.setLogout(); // This might trigger disconnectWebSocket via watcher
			// router.push('/login');
			return;
		}

		clearTimeout(reconnectTimeout!); // Clear any existing timeout
		reconnectAttempts++;
		const delay = reconnectInterval * Math.pow(2, reconnectAttempts - 1); // Exponential backoff
		console.log(`[WS] Scheduling reconnect attempt ${reconnectAttempts}/${maxReconnectAttempts} for session ${sessionId} in ${delay / 1000}s`);

		reconnectTimeout = setTimeout(() => {
			// Double-check the session ID *again* before actually connecting
			if (currentSessionId.value === sessionId) {
				console.log(`[WS] Attempting reconnect ${reconnectAttempts} for session ${sessionId}...`);
				connectWebSocket(sessionId);
			} else {
				console.log(`[WS] Reconnect attempt ${reconnectAttempts} cancelled for ${sessionId}, desired session changed to ${currentSessionId.value}.`);
				reconnectAttempts = 0; // Reset attempts as we are no longer trying for this session
			}
		}, delay);
	}

	function disconnectWebSocket(clean = true) {
		clearTimeout(reconnectTimeout!); // Always clear reconnect timeout on disconnect
		reconnectAttempts = 0; // Reset attempts
		if (websocket) {
			const reason = clean ? "Client requested disconnect" : "Switching sessions";
			console.log(`[WS] Disconnecting WebSocket (${reason}). State: ${websocket.readyState}`);
			if (websocket.readyState === WebSocket.OPEN || websocket.readyState === WebSocket.CONNECTING) {
				websocket.close(1000, reason); // 1000 is normal closure
			}
			websocket = null; // Clear the reference
		}
		// Only update store if it reflects the current state intention
		if (appStore.websocketConnected) {
			appStore.setWebsocketConnected(false);
		}
	}


	function handleMessage(messageJson: string) {
		try {
			// console.log("[WS] Handling Message:", messageJson.substring(0, 200) + "...");
			const clientCommand = JSON.parse(messageJson) as ClientCommand; // Assuming ClientCommand is the base type

			switch (clientCommand.command) {
				case CommandType.SetLiveStore:
					const storeCommand = clientCommand as ClientCommand.StoreCommand.SetLiveStore;
					appStore.setLiveStore(storeCommand.store);
					break;
				case CommandType.AddElements:
					const addElementsCommand = clientCommand as ClientCommand.StoreCommand.AddElements;
					appStore.addElements(addElementsCommand.path, addElementsCommand.elements);
					break;
				case CommandType.ShowMessage:
					const showMessageCommand = clientCommand as ClientCommand.ShowMessage;
					const messageType = showMessageCommand.browser_message_kind === BrowserMessageKind.ALERT ? 'error' : 'info'; // Or map more granularly
					const messageText = showMessageCommand.message.join('\n');
					if (messageType === 'error') {
						showMessage(messageText, { type: 'error', duration: 5000, closable: true });
					} else {
						showNotification({
							content: messageText,
							type: messageType, // info, success, warning
							duration: 4000,
							keepAliveOnHover: true
						});
					}
					break;
				case CommandType.TerminateSession:
					const terminateCommand = clientCommand as ClientCommand.TerminateSession;
					console.log("[WS] Session termination requested by server:", terminateCommand.message);
					showMessage(terminateCommand.message || i18nGlobal.t('errors.sessionTerminated'), { type: 'error', duration: 0, closable: true });
					authStore.setLogout(); // This will trigger watchers to disconnect WS and clear state
					// Router guard should handle redirect
					break;
				case CommandType.NetworkDown:
					console.warn("[WS] Backend indicated network issues.");
					showMessage(i18nGlobal.t('errors.networkDown'), { type: 'warning' });
					break;
				case CommandType.NetworkUp:
					console.info("[WS] Backend indicated network is back up.");
					showMessage(i18nGlobal.t('info.networkUp'), { type: 'success' });
					break;
				case CommandType.CommandSucceeded:
					console.log("[WS] Command succeeded ack received.");
					break;
				default:
					console.warn("[WS] Received unknown command type:", clientCommand.command);
			}
		} catch (error) {
			console.error("[WS] Error parsing or handling WebSocket message:", error, "\nMessage was:", messageJson);
			showMessage(i18nGlobal.t('errors.websocketMessageError'), { type: 'error' });
		}
	}

	function sendCommand(command: EngineCommand) {
		if (!currentSessionId.value) {
			console.error("[WS] No active session ID. Cannot send command.");
			showMessage(i18nGlobal.t('errors.sendCommandNoSession'), { type: 'error' });
			return;
		}
		if (!websocket || websocket.readyState !== WebSocket.OPEN) {
			console.error("[WS] WebSocket is not connected. Cannot send command.");
			showMessage(i18nGlobal.t('errors.websocketNotConnected'), { type: 'error' });
			// Optional: Attempt reconnect or queue command? Maybe trigger connect if not connecting
			if (!websocket || websocket.readyState === WebSocket.CLOSED) {
				console.log("[WS] Attempting to reconnect before sending command...");
				connectWebSocket(currentSessionId.value);
				// Maybe queue the command to send after successful connection?
			}
			return;
		}


		// Construct the envelope - Backend needs to handle missing simplename/classname
		const envelope: Partial<EngineCommandEnvelope> = {
			session_id: currentSessionId.value,
			command: command,
		};

		try {
			const messageJson = JSON.stringify(envelope);
			console.log("[WS] Sending Command:", command.constructor.name, JSON.stringify(command).substring(0,100)+"...");
			// TODO: Gzip if required by backend
			websocket.send(messageJson);
		} catch (error) {
			console.error("[WS] Error sending command:", error);
			showMessage(i18nGlobal.t('errors.sendCommandFailed'), { type: 'error' });
		}
	}

	return {
		// connectWebSocket, // Connection is now managed internally by session ID changes
		disconnectWebSocket, // Expose for explicit disconnect (e.g., logout button)
		sendCommand,
		websocketConnected: computed(() => appStore.websocketConnected),
		lastMessageTimestamp: computed(() => appStore.lastMessageTimestamp),
	};
}

=== src/router/index.ts
import type { FormType } from "@/components/auth/types.d";
import { Layout, PageName } from "@/types/generated"; // Assuming PageName is generated
import { authCheck } from "@/utils/auth";
import { createRouter, createWebHistory } from "vue-router";

// Helper to map backend PageName to frontend route names/paths
// Adjust paths/names as needed to match your preferred frontend routing structure
const pageNameToRoute = {
	[PageName.LOGIN_PAGE]: { name: "Login", path: "/login" },
	[PageName.HOME_PAGE]: { name: "Home", path: "/" }, // Map to dashboard or appropriate home
	[PageName.USER_PAGE]: { name: "UserManagement", path: "/admin/users" },
	[PageName.COMPANY_PAGE]: { name: "CompanyManagement", path: "/admin/companies" }, // Assuming a dedicated page
	[PageName.SESSION_PAGE]: { name: "SessionManagement", path: "/admin/sessions" },
	[PageName.DE_SETUP_PAGE]: { name: "DeAuctionSetup", path: "/de/setup" }, // Can be combined with edit using :id?
	[PageName.DE_AUCTIONEER_PAGE]: { name: "DeAuctioneer", path: "/de/auction/:id/auctioneer" },
	[PageName.DE_TRADER_PAGE]: { name: "DeTrader", path: "/de/auction/:id/trader" },
	// Add mappings for other PageNames (CREDITOR, BH, MR, TE, TO) if they exist and have UIs
	[PageName.CREDITOR_AUCTIONEER_PAGE]: { name: "CreditorAuctioneer", path: "/creditor/auction/:id/auctioneer" },
	[PageName.CREDITOR_TRADER_PAGE]: { name: "CreditorTrader", path: "/creditor/auction/:id/trader" },
	// ... add others like BH_AUCTIONEER_PAGE etc. if needed
};

const router = createRouter({
	history: createWebHistory(import.meta.env.BASE_URL),
	routes: [
		// Redirect root to a default dashboard or home based on role later
		{
			path: "/",
			name: pageNameToRoute[PageName.HOME_PAGE].name, // Use mapping
			component: () => import("@/views/Dashboard/Analytics.vue"), // Example dashboard
			meta: { title: "Dashboard", auth: true, roles: "all" },
		},

		// --- Authentication ---
		{
			path: pageNameToRoute[PageName.LOGIN_PAGE].path,
			name: pageNameToRoute[PageName.LOGIN_PAGE].name,
			component: () => import("@/views/Auth/Login.vue"), // Use the adapted Pinx view
			meta: {
				title: "Login",
				theme: { layout: Layout.Blank, boxed: { enabled: false }, padded: { enabled: false } },
				checkAuth: true, // Redirect if already logged in
				skipPin: true,
			},
		},
		// Add Register and ForgotPassword if needed, mapping them appropriately
		{
			path: "/register", // No direct PageName mapping, assuming standard flow
			name: "Register",
			component: () => import("@/views/Auth/Login.vue"), // Reuse Login view
			props: { formType: "signup" as FormType },
			meta: {
				title: "Register",
				theme: { layout: Layout.Blank, boxed: { enabled: false }, padded: { enabled: false } },
				checkAuth: true,
				skipPin: true,
			},
		},
		{
			path: "/forgot-password", // No direct PageName mapping
			name: "ForgotPassword",
			component: () => import("@/views/Auth/Login.vue"), // Reuse Login view
			props: { formType: "forgotpassword" as FormType },
			meta: {
				title: "Forgot Password",
				theme: { layout: Layout.Blank, boxed: { enabled: false }, padded: { enabled: false } },
				checkAuth: true,
				skipPin: true,
			},
		},
		{
			path: "/logout",
			name: "Logout",
			redirect: pageNameToRoute[PageName.LOGIN_PAGE].path, // Redirect to login after logout logic
		},

		// --- Admin / Auctioneer Specific ---
		{
			path: "/admin", // Group admin routes
			redirect: "/admin/users",
			meta: { auth: true, roles: ["AUCTIONEER"] }, // Role guard
			children: [
				{
					path: "users",
					name: pageNameToRoute[PageName.USER_PAGE].name,
					component: () => import("@/views/Admin/UserManagement.vue"), // Use created view
					meta: { title: "User Management" },
				},
				{
					path: "companies",
					name: pageNameToRoute[PageName.COMPANY_PAGE].name,
					component: () => import("@/views/Admin/CompanyManagement.vue"), // Use created view
					meta: { title: "Company Management" },
				},
				{
					path: "sessions",
					name: pageNameToRoute[PageName.SESSION_PAGE].name,
					component: () => import("@/views/Admin/SessionManagement.vue"), // Use created view
					meta: { title: "Session Management" },
				},
			],
		},

		// --- DE Auction ---
		{
			path: "/de", // Group DE routes
			redirect: pageNameToRoute[PageName.HOME_PAGE].path, // Redirect to home/dashboard
			meta: { auth: true, roles: "all" }, // Allow both roles, specific views handle finer control
			children: [
				{
					path: "setup", // Route for creating a new auction
					name: pageNameToRoute[PageName.DE_SETUP_PAGE].name + "Create", // Differentiate create/edit
					component: () => import("@/views/Auction/DeAuctionSetup.vue"), // Use created view
					meta: { title: "Create DE Auction", roles: ["AUCTIONEER"] },
				},
				{
					path: "setup/:id", // Route for editing an existing auction
					name: pageNameToRoute[PageName.DE_SETUP_PAGE].name + "Edit",
					component: () => import("@/views/Auction/DeAuctionSetup.vue"), // Reuse the setup view
					props: true, // Pass route params as props
					meta: { title: "Edit DE Auction", roles: ["AUCTIONEER"] },
				},
				{
					path: "auction/:id/auctioneer",
					name: pageNameToRoute[PageName.DE_AUCTIONEER_PAGE].name,
					component: () => import("@/views/Auction/DeAuctioneer.vue"), // Use created view
					props: true,
					meta: { title: "DE Auctioneer View", roles: ["AUCTIONEER"] },
				},
				{
					path: "auction/:id/trader",
					name: pageNameToRoute[PageName.DE_TRADER_PAGE].name,
					component: () => import("@/views/Auction/DeTrader.vue"), // Use created view
					props: true,
					meta: { title: "DE Trader View", roles: ["TRADER"] },
				},
			],
		},

		// --- Add routes for other auction types (Creditor, BH, MR, TE, TO) similarly ---
		// Example for Creditor
		{
			path: "/creditor",
			redirect: pageNameToRoute[PageName.HOME_PAGE].path, // Or a specific creditor dashboard/list
			meta: { auth: true, roles: "all" },
			children: [
				// Add setup/edit routes if applicable
				{
					path: "auction/:id/auctioneer",
					name: pageNameToRoute[PageName.CREDITOR_AUCTIONEER_PAGE].name,
					component: () => import("@/views/Auction/CreditorAuctioneer.vue"), // Use created placeholder
					props: true,
					meta: { title: "Creditor Auctioneer View", roles: ["AUCTIONEER"] },
				},
				{
					path: "auction/:id/trader",
					name: pageNameToRoute[PageName.CREDITOR_TRADER_PAGE].name,
					component: () => import("@/views/Auction/CreditorTrader.vue"), // Use created placeholder
					props: true,
					meta: { title: "Creditor Trader View", roles: ["TRADER"] },
				},
			]
		},


		// --- Catchall ---
		{
			path: "/:pathMatch(.*)*",
			name: "NotFound",
			component: () => import("@/views/NotFound.vue"),
			meta: {
				theme: { layout: Layout.Blank, boxed: { enabled: false }, padded: { enabled: false } },
				skipPin: true,
			},
		},
	],
});

router.beforeEach(route => {
	return authCheck(route); // Use Pinx's auth check utility
});

export default router;

=== src/components/common/SegmentedPage.vue
<template>
	<n-split
		ref="splitPane"
		direction="horizontal"
		:default-size="sanitizedDefaultSplit"
		:min="0"
		:max="1"
		:resize-trigger-size="0"
		:disabled="!enableResize || splitDisabled"
		class="wrapper flex grow"
		:class="[{ 'sidebar-open': sidebarOpen }, `sidebar-position-${sidebarPosition}`]"
		:pane1-style="pane1Style"
	>
		<template #[tplNameSide]>
			<div v-if="sidebarAvailable" ref="sidebar" class="sidebar flex flex-col">
				<div v-if="$slots['sidebar-header']" class="sidebar-header flex items-center justify-between">
					<slot name="sidebar-header" />
					<n-button text class="close-btn" @click="sidebarOpen = false">
						<Icon :size="24" :name="CloseIcon" />
					</n-button>
				</div>
				<div v-if="$slots['sidebar-content']" class="sidebar-main grow">
					<n-scrollbar class="max-h-full">
						<div class="sidebar-main-content" :style="sidebarContentStyle" :class="sidebarContentClass">
							<slot name="sidebar-content" />
						</div>
					</n-scrollbar>
				</div>
				<div v-if="$slots['sidebar-footer']" class="sidebar-footer flex items-center">
					<slot name="sidebar-footer" />
				</div>
			</div>
		</template>

		<template #resize-trigger>
			<div class="split-trigger">
				<div class="split-trigger-icon">
					<Icon :name="SplitIcon" :size="12" />
				</div>
			</div>
		</template>

		<template #[tplNameMain]>
			<div class="main flex flex-grow flex-col">
				<div v-if="$slots['main-toolbar']" class="main-toolbar flex items-center">
					<div v-if="sidebarAvailable && !hideMenuBtn" class="menu-btn flex justify-center opacity-50">
						<n-button text @click="sidebarOpen = true">
							<Icon :size="24" :name="MenuIcon" />
						</n-button>
					</div>

					<div class="grow">
						<slot name="main-toolbar" />
					</div>
				</div>
				<div class="main-view grow" :class="{ 'no-container-query': disableContainerQuery }">
					<n-scrollbar v-if="useMainScroll" ref="mainScrollbar" class="max-h-full">
						<div class="main-content" :style="mainContentStyle" :class="mainContentClass">
							<slot name="main-content" />
						</div>
					</n-scrollbar>
					<div v-else class="main-content" :style="mainContentStyle" :class="mainContentClass">
						<slot name="main-content" />
					</div>
				</div>
				<div v-if="$slots['main-footer']" class="main-footer flex items-center">
					<div class="wrap">
						<slot name="main-footer" />
					</div>
				</div>

				<div v-if="sidebarOpen" class="main-overlay" @click="closeSidebar" />
			</div>
		</template>
	</n-split>
</template>

<script setup lang="ts">
import type { SetupContext } from "vue"
import Icon from "@/components/common/Icon.vue"
import { onClickOutside, useWindowSize } from "@vueuse/core"
import { NButton, NScrollbar, NSplit } from "naive-ui"
import { computed, onMounted, ref, useSlots, watch } from "vue"

type SidebarPosition = "left" | "right"

export interface CtxSegmentedPage {
	mainScrollbar: typeof NScrollbar | null
	closeSidebar: () => void
	openSidebar: () => void
}

const {
	hideMenuBtn,
	mainContentStyle,
	mainContentClass,
	sidebarContentStyle,
	sidebarContentClass,
	enableResize,
	disableContainerQuery,
	sidebarPosition = "left",
	useMainScroll = true,
	defaultSplit = 0.3,
	maxSidebarWidth = 450,
	minSidebarWidth = 250,
	padding = "30px",
	paddingMobile = "20px",
	toolbarHeight = "70px",
	toolbarHeightMobile = "62px"
} = defineProps<{
	sidebarPosition?: SidebarPosition
	hideMenuBtn?: boolean
	useMainScroll?: boolean
	mainContentStyle?: string
	mainContentClass?: string
	sidebarContentStyle?: string
	sidebarContentClass?: string
	enableResize?: boolean
	disableContainerQuery?: boolean
	defaultSplit?: number
	maxSidebarWidth?: number
	minSidebarWidth?: number
	padding?: string
	paddingMobile?: string
	toolbarHeight?: string
	toolbarHeightMobile?: string
}>()

const emit = defineEmits<{
	(e: "mounted", value: CtxSegmentedPage): void
	(e: "sidebar", value: boolean): void
}>()

const MenuIcon = "ph:list-light"
const CloseIcon = "carbon:close"
const SplitIcon = "carbon:draggable"

const splitPane = ref()
const sanitizedDefaultSplit = ref(defaultSplit)
const splitDisabled = ref(false)

const slots: SetupContext["slots"] = useSlots()
const sidebarOpen = ref(false)
const sidebar = ref(null)
const mainScrollbar = ref<typeof NScrollbar | null>(null)
const { width } = useWindowSize()
const sidebarAvailable = computed<boolean>(
	() => !!slots["sidebar-header"] || !!slots["sidebar-content"] || !!slots["sidebar-footer"]
)
const isSidebarLeft = computed<boolean>(() => sidebarPosition === "left")
const tplNameMain = computed<1 | 2>(() => (isSidebarLeft.value ? 2 : 1))
const tplNameSide = computed<1 | 2>(() => (isSidebarLeft.value ? 1 : 2))
const pane1Style = computed(() => ({
	maxWidth: isSidebarLeft.value ? `${maxSidebarWidth}px` : `calc(100% - ${minSidebarWidth}px)`,
	minWidth: isSidebarLeft.value ? `${minSidebarWidth}px` : `calc(100% - ${maxSidebarWidth}px)`
}))

function closeSidebar() {
	sidebarOpen.value = false
}

function openSidebar() {
	sidebarOpen.value = true
}

onClickOutside(sidebar, () => closeSidebar())

watch(
	sidebarOpen,
	val => {
		emit("sidebar", val)
	},
	{ immediate: true }
)

watch(
	width,
	val => {
		sanitizedDefaultSplit.value = val <= 700 ? 0 : isSidebarLeft.value ? defaultSplit : 1 - defaultSplit
		splitDisabled.value = val <= 700
	},
	{ immediate: true }
)

onMounted(() => {
	emit("mounted", {
		mainScrollbar: mainScrollbar.value,
		closeSidebar,
		openSidebar
	})
})
</script>

<style lang="scss" scoped>
.wrapper {
	--mb-toolbar-height: v-bind(toolbarHeight);
	--padding-x: v-bind(padding);
	position: relative;
	height: 100%;
	overflow: hidden;
	border-radius: var(--border-radius);
	border: 1px solid var(--border-color);
	background-color: var(--bg-default-color);
	direction: ltr;

	.split-trigger {
		height: 100%;
		width: 3px;
		display: flex;
		align-items: center;
		position: relative;
		z-index: 1;
		left: -2px;
		transition: background-color 0.3s var(--bezier-ease);

		.split-trigger-icon {
			background-color: var(--border-color);
			border-radius: var(--border-radius-small);
			height: 18px;
			display: flex;
			justify-content: center;
			align-items: center;
			position: relative;
			margin-left: -5px;
			z-index: 1;
			transition: background-color 0.3s var(--bezier-ease);
		}

		&:hover {
			background-color: rgba(var(--primary-color-rgb) / 0.1);

			.split-trigger-icon {
				background-color: rgba(var(--primary-color-rgb) / 0.1);
			}
		}
	}

	.sidebar {
		background-color: var(--bg-secondary-color);
		height: 100%;
		overflow: hidden;
		border-right: 1px solid var(--border-color);

		.sidebar-header {
			border-block-end: 1px solid var(--border-color);
			min-height: var(--mb-toolbar-height);
			height: var(--mb-toolbar-height);
			padding: 0 var(--padding-x);

			.close-btn {
				display: none;
			}
		}

		.sidebar-main {
			overflow: hidden;

			.sidebar-main-content {
				padding: var(--padding-x);
			}
		}

		.sidebar-footer {
			border-block-start: 1px solid var(--border-color);
			min-height: var(--mb-toolbar-height);
			padding: 0 var(--padding-x);
		}
	}

	.main {
		background-color: var(--bg-default-color);
		position: relative;
		height: 100%;

		.main-overlay {
			position: absolute;
			width: 100%;
			height: 100%;
			z-index: 2;
			top: 0;
			left: 0;
			background-color: rgba(0, 0, 0, 0.4); // Added overlay background
			cursor: pointer; // Indicate it's clickable
		}

		.main-toolbar {
			border-block-end: 1px solid var(--border-color);
			min-height: var(--mb-toolbar-height);
			height: var(--mb-toolbar-height);
			padding: 0 var(--padding-x);
			gap: 18px;
			line-height: 1.3;
			container-type: inline-size;

			.menu-btn {
				display: none;
			}
		}

		.main-view {
			overflow: hidden;
			&:not(.no-container-query) {
				container-type: inline-size;
			}

			.main-content {
				padding: var(--padding-x);
			}
		}

		.main-footer {
			container-type: inline-size;
			border-block-start: 1px solid var(--border-color);
			padding: 0 var(--padding-x);

			.wrap {
				min-height: calc(var(--mb-toolbar-height) - 1px);
				width: 100%;
				display: flex;
				align-items: center;
			}
		}
	}

	&.sidebar-position-right {
		.sidebar {
			border-right: none;
			border-left: 1px solid var(--border-color);
		}
	}

	@media (max-width: 700px) {
		--mb-toolbar-height: v-bind(toolbarHeightMobile);
		--padding-x: v-bind(paddingMobile);

		height: 100%;
		overflow: hidden;
		border-radius: 0;
		border: none;

		&::before { // Overlay for mobile when sidebar is open
			content: "";
			width: 100vw;
			display: block;
			background-color: rgba(0, 0, 0, 0.4); // Semi-transparent overlay
			position: absolute;
			top: 0;
			left: 0;
			bottom: 0;
			transform: translateX(-100%);
			opacity: 0;
			transition:
				opacity 0.25s ease-in-out,
				transform 0s linear 0.3s;
			z-index: 1; // Below sidebar but above main content
			pointer-events: none; // Allow clicks through initially
		}

		.sidebar {
			position: absolute;
			top: 0;
			left: 0;
			bottom: 0;
			transform: translateX(-100%);
			transition: transform 0.25s ease-in-out;
			z-index: 3; // Sidebar above overlay
			min-width: 300px;
			max-width: min(450px, 80vw);
			background-color: var(--bg-default-color); // Ensure background for mobile

			.sidebar-header,
			.sidebar-footer {
				padding: 0 var(--padding-x);

				.close-btn {
					display: flex;
				}
			}

			.sidebar-main {
				.sidebar-main-content {
					padding: var(--padding-x);
				}
			}
		}
		.main {
			.main-toolbar {
				padding: 0 var(--padding-x);
				gap: 14px;

				.menu-btn {
					display: flex;
				}
			}

			.main-view {
				.main-content {
					padding: var(--padding-x);
				}
			}
			.main-footer {
				padding: 0 var(--padding-x);
			}
		}

		&.sidebar-position-left {
			:deep() {
				.n-split-pane-1 {
					min-width: 0 !important;
					max-width: 0 !important;
				}
			}
		}

		&.sidebar-position-right {
			:deep() {
				.n-split-pane-1 {
					min-width: 100% !important;
					max-width: 100% !important;
				}
			}

			&::before,
			.sidebar {
				left: initial;
				right: 0;
				transform: translateX(100%);
			}

			.main {
				.main-toolbar {
					flex-direction: row-reverse;
					justify-content: space-between;
				}
			}
		}

		&.sidebar-open {
			&::before {
				transform: translateX(0);
				opacity: 1; // Make overlay visible
				transition:
					opacity 0.25s ease-in-out,
					transform 0s linear 0s;
				pointer-events: auto; // Allow clicks on overlay to close sidebar
			}

			.sidebar {
				transform: translateX(0);
				box-shadow: 0px 0px 80px 0px rgba(0, 0, 0, 0.1);
			}
		}
	}
}

.direction-rtl {
	.wrapper {
		.sidebar,
		.main {
			direction: rtl;
		}
		// Add specific RTL adjustments if needed, e.g., for split trigger position
		.split-trigger {
			left: initial;
			right: -2px; // Adjust for RTL
			.split-trigger-icon {
				margin-left: initial;
				margin-right: -5px; // Adjust for RTL
			}
		}
		&.sidebar-position-left { // When sidebar is on the left in RTL (visually right)
			.sidebar {
				border-right: none;
				border-left: 1px solid var(--border-color);
			}
		}
		&.sidebar-position-right { // When sidebar is on the right in RTL (visually left)
			.sidebar {
				border-left: none;
				border-right: 1px solid var(--border-color);
			}
		}
	}
}
</style>

=== src/views/Auction/CreditorAuctioneer.vue
<template>
	<div>Placeholder for CreditorAuctioneer</div>
</template>

<script setup lang="ts">
// Basic setup, implement later
</script>

=== src/views/Auction/CreditorTrader.vue
<template>
	<div>Placeholder for CreditorTrader</div>
</template>

<script setup lang="ts">
// Basic setup, implement later
</script>

=== src/views/Auth/Login.vue
<template>
	<div class="page-auth">
		<!-- Pinx Settings component might be removed if not needed -->
		<!-- <Settings v-if="!isLogged" v-model:align="align" v-model:active-color="activeColor" /> -->

		<div class="wrapper flex justify-center">
			<!-- Optional Image Box -->
			<div v-if="align === 'right'" class="image-box basis-2/3" />

			<div class="form-box flex basis-1/3 items-center justify-center" :class="{ centered: align === 'center' }">
				<!-- Use Pinx's AuthForm, passing our specific type -->
				<!-- Removed @change-view as we use router now -->
				<AuthForm :type="type" :use-only-router="true" />
			</div>

			<div v-if="align === 'left'" class="image-box basis-2/3" />
		</div>
	</div>
</template>

<script lang="ts" setup>
import type { Align } from "@/components/auth/Settings.vue"; // If using settings
import type { FormType } from "@/components/auth/types.d";
import AuthForm from "@/components/auth/AuthForm.vue";
// import Settings from "@/components/auth/Settings.vue"; // Optional
import { useAuthStore } from "@/stores/auth";
import { computed, onBeforeMount, ref } from "vue";
import { useRoute } from "vue-router";
import "./main.scss"; // Import Pinx's auth styles

const props = defineProps<{
	formType?: FormType;
}>();

const route = useRoute();
const align = ref<Align>("left"); // Default alignment from Pinx
const activeColor = ref(""); // Default color from Pinx
const type = ref<FormType>(props.formType || "signin"); // Default to signin
const authStore = useAuthStore();
const isLogged = computed(() => authStore.isLogged);

onBeforeMount(() => {
	// Check route query params on initial load (like Pinx might do)
	// Or determine type based on route name if using separate routes
	if (route.name === 'Register') {
		type.value = 'signup';
	} else if (route.name === 'ForgotPassword') {
		type.value = 'forgotpassword';
	} else {
		type.value = 'signin';
	}

	// Handle query params if still needed for specific flows
	if (route.query.step) {
		const step = route.query.step as FormType;
		if (["signin", "signup", "forgotpassword"].includes(step)) {
			type.value = step;
		}
	}
});
</script>

<!-- Pinx's auth styles are imported via main.scss -->


=== src/views/Dashboard/Analytics.vue
<template>
	<div class="page">
		<div class="page-header">
			<div class="title">Auction Dashboard</div>
		</div>

		<!-- Auctioneer View -->
		<div v-if="isAuctioneer" class="components-list">
			<CardCodeExample title="Manage Auctions">
				<AuctionList :auctions="appStore.auction_rows" :is-auctioneer="true" />
				<n-button type="primary" class="mt-4" @click="goToCreateAuction">
					Create New DE Auction
				</n-button>
			</CardCodeExample>
			<CardCodeExample title="Admin Links">
				<n-space>
					<n-button @click="goToUserManagement">User Management</n-button>
					<n-button @click="goToCompanyManagement">Company Management</n-button>
					<n-button @click="goToSessionManagement">Session Management</n-button>
				</n-space>
			</CardCodeExample>
		</div>

		<!-- Trader View -->
		<div v-else-if="isTrader" class="components-list">
			<CardCodeExample title="Available Auctions">
				<AuctionList :auctions="appStore.auction_rows" :is-auctioneer="false" />
			</CardCodeExample>
			<!-- Add Trader specific dashboard items if needed -->
		</div>

		<!-- Fallback or Loading -->
		<div v-else>
			<n-spin />
		</div>
	</div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { useAppStore } from '@/stores/appStore';
import { NButton, NSpace, NSpin } from 'naive-ui';
import AuctionList from '@/components/auction/AuctionList.vue'; // Use created component
import CardCodeExample from '@/components/cards/CardCodeExample.vue'; // Use Pinx's card

const router = useRouter();
const appStore = useAppStore();

const isAuctioneer = computed(() => appStore.isAuctioneer);
const isTrader = computed(() => appStore.isTrader);

function goToCreateAuction() {
	router.push({ name: 'DeAuctionSetupCreate' });
}

function goToUserManagement() {
	router.push({ name: 'UserManagement' });
}

function goToCompanyManagement() {
	router.push({ name: 'CompanyManagement' });
}

function goToSessionManagement() {
	router.push({ name: 'SessionManagement' });
}
</script>

<style scoped>
/* Add specific styles if needed */
.components-list {
	grid-template-columns: 1fr; /* Adjust layout if needed */
}
</style>

=== src/views/Admin/CompanyManagement.vue
<template>
	<div>Placeholder for CompanyManagement</div>
</template>

<script setup lang="ts">
// Basic setup, implement later
</script>

=== src/views/Admin/SessionManagement.vue
<template>
	<div>Placeholder for SessionManagement</div>
</template>

<script setup lang="ts">
// Basic setup, implement later
</script>

=== src/views/Admin/UserManagement.vue
<template>
	<div class="page">
		<div class="page-header">
			<div class="title">User Management</div>
			<n-button type="primary" @click="openCreateModal">Create User</n-button>
		</div>

		<n-card>
			<n-data-table
				:columns="columns"
				:data="appStore.users"
				:pagination="pagination"
				:bordered="false"
			/>
		</n-card>

		<!-- Create/Edit Modal -->
		<n-modal v-model:show="showModal" preset="card" :style="{ width: '600px' }" :title="modalTitle">
			<UserForm :user-data="currentUser" :companies="companyOptions" @save="handleSave" @cancel="closeModal" />
		</n-modal>
	</div>
</template>

<script setup lang="ts">
import type { DataTableColumns } from 'naive-ui';
import type { UserElement, CompanyElement, AuUserRole } from '@/types/generated'; // Assuming generated types
import { h, ref, computed } from 'vue';
import { NDataTable, NButton, NSpace, NTag, NPopconfirm, useMessage, NModal, NCard } from 'naive-ui';
import { useAppStore } from '@/stores/appStore';
import { useWebSocket } from '@/services/websocket';
import UserForm from '@/components/admin/UserForm.vue'; // Use created component

const appStore = useAppStore();
const ws = useWebSocket();
const message = useMessage();

const showModal = ref(false);
const isEditing = ref(false);
const currentUser = ref<UserElement | null>(null);

const modalTitle = computed(() => (isEditing.value ? 'Edit User' : 'Create User'));
const pagination = { pageSize: 15 };

const companyOptions = computed(() => appStore.companies.map(c => ({ label: `${c.company_shortname} (${c.company_longname})`, value: c.company_id })));

function openCreateModal() {
	isEditing.value = false;
	currentUser.value = null; // Or a default new user structure if needed by form
	showModal.value = true;
}

function openEditModal(user: UserElement) {
	isEditing.value = true;
	currentUser.value = { ...user }; // Clone to avoid modifying store directly before save
	showModal.value = true;
}

function closeModal() {
	showModal.value = false;
	currentUser.value = null;
}

function handleDelete(userId: string) {
	ws.sendCommand({
		'@type': 'au21.engine.domain.common.commands.UserDeleteCommand',
		user_id: userId,
	});
	message.success(`Delete command sent for user ${userId}`);
}

function handleSave(userData: Partial<UserElement> & { password?: string; role: AuUserRole; company_id?: string }) {
	// Construct the UserSaveCommand payload
	const commandPayload = {
		'@type': 'au21.engine.domain.common.commands.UserSaveCommand',
		user_id: userData.user_id || "", // Empty for create
		username: userData.username || "",
		password: userData.password || (isEditing.value ? "" : "DEFAULT_PASSWORD"), // Handle password - empty means no change on edit, provide default for create?
		role: userData.role,
		company_id: userData.company_id || "", // Handle optional company
		email: userData.email || "",
		phone: userData.phone || "",
		// isObserver and isTester might need separate handling or be part of UserSaveCommand
	};

	// Basic validation example
	if (!commandPayload.username) {
		message.error("Username is required.");
		return;
	}
	if (!isEditing.value && !commandPayload.password) {
		message.error("Password is required for new users.");
		return;
	}
	if (commandPayload.role === 'TRADER' && !commandPayload.company_id) {
		message.error("Trader role requires a company.");
		return;
	}


	ws.sendCommand(commandPayload);
	message.success(`User ${isEditing.value ? 'update' : 'create'} command sent.`);
	closeModal();
}


const columns = computed<DataTableColumns<UserElement>>(() => [
	{ title: 'Username', key: 'username', sorter: 'default' },
	{ title: 'Role', key: 'role', width: 100 },
	{
		title: 'Company', key: 'company_shortname', width: 150,
		render: (row) => row.company_shortname || '---'
	},
	{ title: 'Email', key: 'email', width: 250, ellipsis: { tooltip: true } },
	{ title: 'Phone', key: 'phone', width: 150 },
	{
		title: 'Status', key: 'isOnline', width: 100,
		render(row) {
			return h(NTag, { type: row.isOnline ? 'success' : 'error', size: 'small' }, { default: () => row.isOnline ? 'Online' : 'Offline' });
		}
	},
	{
		title: 'Connection', key: 'has_connection_problem', width: 120,
		render(row) {
			return row.has_connection_problem ? h(NTag, { type: 'warning', size: 'small' }, { default: () => 'Problem' }) : 'OK';
		}
	},
	{
		title: 'Actions',
		key: 'actions',
		width: 150,
		align: 'right',
		render(row) {
			const editButton = h(NButton, { size: 'small', onClick: () => openEditModal(row), secondary: true, type: 'info' }, { default: () => 'Edit' });
			const deleteButton = h(
				NPopconfirm,
				{ onPositiveClick: () => handleDelete(row.user_id) },
				{
					trigger: () => h(NButton, { size: 'small', secondary: true, type: 'error' }, { default: () => 'Delete' }),
					default: () => `Are you sure you want to delete user ${row.username}?`
				}
			);
			return h(NSpace, { justify: 'end' }, () => [editButton, deleteButton]);
		}
	}
]);

</script>

=== src/views/Auction/DeAuctionSetup.vue
<template>
	<div>Placeholder for DeAuctionSetup</div>
</template>

<script setup lang="ts">
// Basic setup, implement later
</script>

=== src/views/Auction/DeAuctioneer.vue
<template>
	<div class="page page-wrapped page-without-footer flex flex-col">
		<SegmentedPage enable-resize sidebar-position="right" :default-split="0.75">
			<!-- Main Content: Control Panel, Blotter, Matrix -->
			<template #main-content>
				<div v-if="auctionData" class="flex h-full flex-col gap-4">
					<DeAuctioneerStatus :status="auctionData.auctioneer_status" :common-status="auctionData.common_status" />
					<DeAuctioneerControls :controls="auctionData.auctioneer_status?.controls" :auction-id="auctionId" :current-state="auctionData.auctioneer_status?.auctioneer_state" />
					<n-tabs type="line" animated class="flex grow flex-col overflow-hidden" pane-class="pt-4 flex grow flex-col">
						<n-tab-pane name="blotter" tab="Blotter">
							<DeBlotterView v-if="auctionData.blotter" :blotter-data="auctionData.blotter" class="grow" />
						</n-tab-pane>
						<n-tab-pane name="matrix" tab="Matrix (Last Round)">
							<DeMatrixView v-if="auctionData.matrix_last_round" :matrix-data="auctionData.matrix_last_round" class="grow" />
						</n-tab-pane>
						<n-tab-pane name="award" tab="Award Summary">
							 <DeAwardSummary v-if="auctionData.award_value" :award-data="auctionData.award_value" />
						</n-tab-pane>
						 <n-tab-pane name="settings" tab="Settings">
							 <DeSettingsView :settings="auctionData.settings" :is-auctioneer="true" />
						</n-tab-pane>
					</n-tabs>
				</div>
				<n-spin v-else class="h-full w-full" />
			</template>

			<!-- Sidebar: Info, Participants, Messages -->
			<template #sidebar-content>
				 <div v-if="auctionData" class="flex h-full flex-col gap-4">
					 <DeAuctioneerInfo v-if="auctionData.auctioneer_info" :info="auctionData.auctioneer_info" />
					 <DeParticipantList :traders="auctionData.blotter?.traders" :users-online="onlineUsersInAuction" />
					 <DeMessaging :messages="messages" :is-auctioneer="true" :auction-id="auctionId" />
					 <!-- Add Notice Edit Here -->
					 <n-card title="Notice" size="small">
						 <n-input type="textarea" :value="auctionData.notice" placeholder="Enter auction notice..." @update:value="updateNoticeDebounced" />
					 </n-card>
				 </div>
				  <n-spin v-else class="h-full w-full" />
			 </template>
		</SegmentedPage>
	</div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useAppStore } from '@/stores/appStore';
import { useWebSocket } from '@/services/websocket';
import { NTabs, NTabPane, NSpin, NCard, NInput } from 'naive-ui';
import SegmentedPage from '@/components/common/SegmentedPage.vue'; // Use created placeholder
import DeAuctioneerStatus from '@/components/auction/de/DeAuctioneerStatus.vue'; // Create this
import DeAuctioneerControls from '@/components/auction/de/DeAuctioneerControls.vue'; // Create this
import DeAuctioneerInfo from '@/components/auction/de/DeAuctioneerInfo.vue'; // Create this
import DeBlotterView from '@/components/auction/de/DeBlotterView.vue'; // Create this
import DeMatrixView from '@/components/auction/de/DeMatrixView.vue'; // Create this
import DeAwardSummary from '@/components/auction/de/DeAwardSummary.vue'; // Create this
import DeParticipantList from '@/components/auction/de/DeParticipantList.vue'; // Create this
import DeMessaging from '@/components/auction/de/DeMessaging.vue'; // Create this
import DeSettingsView from '@/components/auction/de/DeSettingsView.vue'; // Create this
import { useHideLayoutFooter } from "@/composables/useHideLayoutFooter";
import _debounce from 'lodash/debounce';

const route = useRoute();
const appStore = useAppStore();
const ws = useWebSocket();

const auctionId = computed(() => route.params.id as string);
const auctionData = computed(() => appStore.de_auction); // Assumes de_auction is populated when on this page
const messages = computed(() => appStore.getMessagesForCurrentAuction);

// Find users from the main user list who are online and in this auction
const onlineUsersInAuction = computed(() => {
	const auctionUserIds = new Set(auctionData.value?.users_that_have_seen_auction || []);
	return appStore.users.filter(u => u.isOnline && auctionUserIds.has(u.user_id));
});

const updateNoticeDebounced = _debounce((newNotice: string) => {
	if (auctionId.value) {
		ws.sendCommand({
			'@type': 'au21.engine.domain.common.commands.NoticeSaveCommand',
			auction_id: auctionId.value,
			notice: newNotice,
		});
	}
}, 500); // Debounce notice updates

// Watch for auction data changes if needed, e.g., if navigating between auctions without full page reload
watch(auctionId, (newId) => {
	if (newId && appStore.currentAuctionId !== newId) {
		// This might indicate a navigation that didn't trigger AuctionSelectCommand correctly
		// Or could be handled by ensuring AuctionSelectCommand is always sent on route enter
		console.warn(`Auctioneer View: Auction ID mismatch: route=${newId}, store=${appStore.currentAuctionId}`);
		// Optionally force select if needed: ws.sendCommand(...)
	}
}, { immediate: true });

useHideLayoutFooter();

</script>

<style scoped>
/* Add specific styles for the auctioneer view */
.page {
	container-type: inline-size;
}
/* Ensure tabs content can grow */
:deep(.n-tab-pane) {
	display: flex;
	flex-direction: column;
	flex-grow: 1;
	overflow: auto; /* Or hidden if internal components handle scroll */
}
</style>

=== src/views/Auction/DeTrader.vue
<template>
	<div class="page page-wrapped page-without-footer flex flex-col">
		 <SegmentedPage sidebar-position="right" :default-split="0.7" :enable-resize="false">
			<!-- Main Content: Status, Bidding, History -->
			<template #main-content>
				<div v-if="auctionData" class="flex h-full flex-col gap-4">
					<DeTraderStatus :status="auctionData.common_status" :trader-info="auctionData.trader_info" />
					<DeTraderBiddingPanel v-if="auctionData.trader_info && auctionData.common_status?.common_state === 'ROUND_OPEN'" :trader-info="auctionData.trader_info" :settings="auctionData.settings" :auction-id="auctionId" :round-number="auctionData.common_status?.round_number" />
					 <DeTraderHistory :history="traderHistory" :settings="auctionData.settings" />
				</div>
				 <n-spin v-else class="h-full w-full" />
			</template>
			<!-- Sidebar: Messages, Notice -->
			 <template #sidebar-content>
				 <div v-if="auctionData" class="flex h-full flex-col gap-4">
					 <n-card title="Notice" size="small">
						 <p v-if="auctionData.notice">{{ auctionData.notice }}</p>
						 <n-empty v-else description="No notice provided." />
					 </n-card>
					 <DeMessaging :messages="messages" :is-auctioneer="false" :auction-id="auctionId" />
					 <!-- Maybe show trader limits here -->
					 <DeTraderInfoDisplay v-if="auctionData.trader_info" :info="auctionData.trader_info" />
				 </div>
				  <n-spin v-else class="h-full w-full" />
			 </template>
		 </SegmentedPage>
	</div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useAppStore } from '@/stores/appStore';
import { NSpin, NCard, NEmpty } from 'naive-ui';
import SegmentedPage from '@/components/common/SegmentedPage.vue'; // Use created placeholder
import DeTraderStatus from '@/components/auction/de/DeTraderStatus.vue'; // Create this
import DeTraderBiddingPanel from '@/components/auction/de/DeTraderBiddingPanel.vue'; // Create this
import DeTraderHistory from '@/components/auction/de/DeTraderHistory.vue'; // Create this
import DeMessaging from '@/components/auction/de/DeMessaging.vue'; // Reuse or adapt
import DeTraderInfoDisplay from '@/components/auction/de/DeTraderInfoDisplay.vue'; // Create this
import { useHideLayoutFooter } from "@/composables/useHideLayoutFooter";

const route = useRoute();
const appStore = useAppStore();

const auctionId = computed(() => route.params.id as string);
const auctionData = computed(() => appStore.de_auction);
const messages = computed(() => appStore.getMessagesForCurrentAuction);
const traderHistory = computed(() => appStore.getDeTraderHistory);

// Watch for auction data changes if needed
watch(auctionId, (newId) => {
	if (newId && appStore.currentAuctionId !== newId) {
		console.warn(`Trader View: Auction ID mismatch: route=${newId}, store=${appStore.currentAuctionId}`);
	}
}, { immediate: true });

useHideLayoutFooter();
</script>

<style scoped>
/* Add specific styles for the trader view */
.page {
	container-type: inline-size;
}
</style>

=== src/components/common/SegmentedPage.vue
<template>
	<n-split
		ref="splitPane"
		direction="horizontal"
		:default-size="sanitizedDefaultSplit"
		:min="0"
		:max="1"
		:resize-trigger-size="0"
		:disabled="!enableResize || splitDisabled"
		class="wrapper flex grow"
		:class="[{ 'sidebar-open': sidebarOpen }, `sidebar-position-${sidebarPosition}`]"
		:pane1-style="pane1Style"
	>
		<template #[tplNameSide]>
			<div v-if="sidebarAvailable" ref="sidebar" class="sidebar flex flex-col">
				<div v-if="$slots['sidebar-header']" class="sidebar-header flex items-center justify-between">
					<slot name="sidebar-header" />
					<n-button text class="close-btn" @click="sidebarOpen = false">
						<Icon :size="24" :name="CloseIcon" />
					</n-button>
				</div>
				<div v-if="$slots['sidebar-content']" class="sidebar-main grow">
					<n-scrollbar class="max-h-full">
						<div class="sidebar-main-content" :style="sidebarContentStyle" :class="sidebarContentClass">
							<slot name="sidebar-content" />
						</div>
					</n-scrollbar>
				</div>
				<div v-if="$slots['sidebar-footer']" class="sidebar-footer flex items-center">
					<slot name="sidebar-footer" />
				</div>
			</div>
		</template>

		<template #resize-trigger>
			<div class="split-trigger">
				<div class="split-trigger-icon">
					<Icon :name="SplitIcon" :size="12" />
				</div>
			</div>
		</template>

		<template #[tplNameMain]>
			<div class="main flex flex-grow flex-col">
				<div v-if="$slots['main-toolbar']" class="main-toolbar flex items-center">
					<div v-if="sidebarAvailable && !hideMenuBtn" class="menu-btn flex justify-center opacity-50">
						<n-button text @click="sidebarOpen = true">
							<Icon :size="24" :name="MenuIcon" />
						</n-button>
					</div>

					<div class="grow">
						<slot name="main-toolbar" />
					</div>
				</div>
				<div class="main-view grow" :class="{ 'no-container-query': disableContainerQuery }">
					<n-scrollbar v-if="useMainScroll" ref="mainScrollbar" class="max-h-full">
						<div class="main-content" :style="mainContentStyle" :class="mainContentClass">
							<slot name="main-content" />
						</div>
					</n-scrollbar>
					<div v-else class="main-content" :style="mainContentStyle" :class="mainContentClass">
						<slot name="main-content" />
					</div>
				</div>
				<div v-if="$slots['main-footer']" class="main-footer flex items-center">
					<div class="wrap">
						<slot name="main-footer" />
					</div>
				</div>

				<div v-if="sidebarOpen" class="main-overlay" @click="closeSidebar" />
			</div>
		</template>
	</n-split>
</template>

<script setup lang="ts">
import type { SetupContext } from "vue"
import Icon from "@/components/common/Icon.vue"
import { onClickOutside, useWindowSize } from "@vueuse/core"
import { NButton, NScrollbar, NSplit } from "naive-ui"
import { computed, onMounted, ref, useSlots, watch } from "vue"

type SidebarPosition = "left" | "right"

export interface CtxSegmentedPage {
	mainScrollbar: typeof NScrollbar | null
	closeSidebar: () => void
	openSidebar: () => void
}

const {
	hideMenuBtn,
	mainContentStyle,
	mainContentClass,
	sidebarContentStyle,
	sidebarContentClass,
	enableResize,
	disableContainerQuery,
	sidebarPosition = "left",
	useMainScroll = true,
	defaultSplit = 0.3,
	maxSidebarWidth = 450,
	minSidebarWidth = 250,
	padding = "30px",
	paddingMobile = "20px",
	toolbarHeight = "70px",
	toolbarHeightMobile = "62px"
} = defineProps<{
	sidebarPosition?: SidebarPosition
	hideMenuBtn?: boolean
	useMainScroll?: boolean
	mainContentStyle?: string
	mainContentClass?: string
	sidebarContentStyle?: string
	sidebarContentClass?: string
	enableResize?: boolean
	disableContainerQuery?: boolean
	defaultSplit?: number
	maxSidebarWidth?: number
	minSidebarWidth?: number
	padding?: string
	paddingMobile?: string
	toolbarHeight?: string
	toolbarHeightMobile?: string
}>()

const emit = defineEmits<{
	(e: "mounted", value: CtxSegmentedPage): void
	(e: "sidebar", value: boolean): void
}>()

const MenuIcon = "ph:list-light"
const CloseIcon = "carbon:close"
const SplitIcon = "carbon:draggable"

const splitPane = ref()
const sanitizedDefaultSplit = ref(defaultSplit)
const splitDisabled = ref(false)

const slots: SetupContext["slots"] = useSlots()
const sidebarOpen = ref(false)
const sidebar = ref(null)
const mainScrollbar = ref<typeof NScrollbar | null>(null)
const { width } = useWindowSize()
const sidebarAvailable = computed<boolean>(
	() => !!slots["sidebar-header"] || !!slots["sidebar-content"] || !!slots["sidebar-footer"]
)
const isSidebarLeft = computed<boolean>(() => sidebarPosition === "left")
const tplNameMain = computed<1 | 2>(() => (isSidebarLeft.value ? 2 : 1))
const tplNameSide = computed<1 | 2>(() => (isSidebarLeft.value ? 1 : 2))
const pane1Style = computed(() => ({
	maxWidth: isSidebarLeft.value ? `${maxSidebarWidth}px` : `calc(100% - ${minSidebarWidth}px)`,
	minWidth: isSidebarLeft.value ? `${minSidebarWidth}px` : `calc(100% - ${maxSidebarWidth}px)`
}))

function closeSidebar() {
	sidebarOpen.value = false
}

function openSidebar() {
	sidebarOpen.value = true
}

onClickOutside(sidebar, () => closeSidebar())

watch(
	sidebarOpen,
	val => {
		emit("sidebar", val)
	},
	{ immediate: true }
)

watch(
	width,
	val => {
		sanitizedDefaultSplit.value = val <= 700 ? 0 : isSidebarLeft.value ? defaultSplit : 1 - defaultSplit
		splitDisabled.value = val <= 700
	},
	{ immediate: true }
)

onMounted(() => {
	emit("mounted", {
		mainScrollbar: mainScrollbar.value,
		closeSidebar,
		openSidebar
	})
})
</script>

<style lang="scss" scoped>
.wrapper {
	--mb-toolbar-height: v-bind(toolbarHeight);
	--padding-x: v-bind(padding);
	position: relative;
	height: 100%;
	overflow: hidden;
	border-radius: var(--border-radius);
	border: 1px solid var(--border-color);
	background-color: var(--bg-default-color);
	direction: ltr;

	.split-trigger {
		height: 100%;
		width: 3px;
		display: flex;
		align-items: center;
		position: relative;
		z-index: 1;
		left: -2px;
		transition: background-color 0.3s var(--bezier-ease);

		.split-trigger-icon {
			background-color: var(--border-color);
			border-radius: var(--border-radius-small);
			height: 18px;
			display: flex;
			justify-content: center;
			align-items: center;
			position: relative;
			margin-left: -5px;
			z-index: 1;
			transition: background-color 0.3s var(--bezier-ease);
		}

		&:hover {
			background-color: rgba(var(--primary-color-rgb) / 0.1);

			.split-trigger-icon {
				background-color: rgba(var(--primary-color-rgb) / 0.1);
			}
		}
	}

	.sidebar {
		background-color: var(--bg-secondary-color);
		height: 100%;
		overflow: hidden;
		border-right: 1px solid var(--border-color);

		.sidebar-header {
			border-block-end: 1px solid var(--border-color);
			min-height: var(--mb-toolbar-height);
			height: var(--mb-toolbar-height);
			padding: 0 var(--padding-x);

			.close-btn {
				display: none;
			}
		}

		.sidebar-main {
			overflow: hidden;

			.sidebar-main-content {
				padding: var(--padding-x);
			}
		}

		.sidebar-footer {
			border-block-start: 1px solid var(--border-color);
			min-height: var(--mb-toolbar-height);
			padding: 0 var(--padding-x);
		}
	}

	.main {
		background-color: var(--bg-default-color);
		position: relative;
		height: 100%;

		.main-overlay {
			position: absolute;
			width: 100%;
			height: 100%;
			z-index: 2;
			top: 0;
			left: 0;
			background-color: rgba(0, 0, 0, 0.4); // Added overlay background
			cursor: pointer; // Indicate it's clickable
		}

		.main-toolbar {
			border-block-end: 1px solid var(--border-color);
			min-height: var(--mb-toolbar-height);
			height: var(--mb-toolbar-height);
			padding: 0 var(--padding-x);
			gap: 18px;
			line-height: 1.3;
			container-type: inline-size;

			.menu-btn {
				display: none;
			}
		}

		.main-view {
			overflow: hidden;
			&:not(.no-container-query) {
				container-type: inline-size;
			}

			.main-content {
				padding: var(--padding-x);
			}
		}

		.main-footer {
			container-type: inline-size;
			border-block-start: 1px solid var(--border-color);
			padding: 0 var(--padding-x);

			.wrap {
				min-height: calc(var(--mb-toolbar-height) - 1px);
				width: 100%;
				display: flex;
				align-items: center;
			}
		}
	}

	&.sidebar-position-right {
		.sidebar {
			border-right: none;
			border-left: 1px solid var(--border-color);
		}
	}

	@media (max-width: 700px) {
		--mb-toolbar-height: v-bind(toolbarHeightMobile);
		--padding-x: v-bind(paddingMobile);

		height: 100%;
		overflow: hidden;
		border-radius: 0;
		border: none;

		&::before { // Overlay for mobile when sidebar is open
			content: "";
			width: 100vw;
			display: block;
			background-color: rgba(0, 0, 0, 0.4); // Semi-transparent overlay
			position: absolute;
			top: 0;
			left: 0;
			bottom: 0;
			transform: translateX(-100%);
			opacity: 0;
			transition:
				opacity 0.25s ease-in-out,
				transform 0s linear 0.3s;
			z-index: 1; // Below sidebar but above main content
			pointer-events: none; // Allow clicks through initially
		}

		.sidebar {
			position: absolute;
			top: 0;
			left: 0;
			bottom: 0;
			transform: translateX(-100%);
			transition: transform 0.25s ease-in-out;
			z-index: 3; // Sidebar above overlay
			min-width: 300px;
			max-width: min(450px, 80vw);
			background-color: var(--bg-default-color); // Ensure background for mobile

			.sidebar-header,
			.sidebar-footer {
				padding: 0 var(--padding-x);

				.close-btn {
					display: flex;
				}
			}

			.sidebar-main {
				.sidebar-main-content {
					padding: var(--padding-x);
				}
			}
		}
		.main {
			.main-toolbar {
				padding: 0 var(--padding-x);
				gap: 14px;

				.menu-btn {
					display: flex;
				}
			}

			.main-view {
				.main-content {
					padding: var(--padding-x);
				}
			}
			.main-footer {
				padding: 0 var(--padding-x);
			}
		}

		&.sidebar-position-left {
			:deep() {
				.n-split-pane-1 {
					min-width: 0 !important;
					max-width: 0 !important;
				}
			}
		}

		&.sidebar-position-right {
			:deep() {
				.n-split-pane-1 {
					min-width: 100% !important;
					max-width: 100% !important;
				}
			}

			&::before,
			.sidebar {
				left: initial;
				right: 0;
				transform: translateX(100%);
			}

			.main {
				.main-toolbar {
					flex-direction: row-reverse;
					justify-content: space-between;
				}
			}
		}

		&.sidebar-open {
			&::before {
				transform: translateX(0);
				opacity: 1; // Make overlay visible
				transition:
					opacity 0.25s ease-in-out,
					transform 0s linear 0s;
				pointer-events: auto; // Allow clicks on overlay to close sidebar
			}

			.sidebar {
				transform: translateX(0);
				box-shadow: 0px 0px 80px 0px rgba(0, 0, 0, 0.1);
			}
		}
	}
}

.direction-rtl {
	.wrapper {
		.sidebar,
		.main {
			direction: rtl;
		}
		// Add specific RTL adjustments if needed, e.g., for split trigger position
		.split-trigger {
			left: initial;
			right: -2px; // Adjust for RTL
			.split-trigger-icon {
				margin-left: initial;
				margin-right: -5px; // Adjust for RTL
			}
		}
		&.sidebar-position-left { // When sidebar is on the left in RTL (visually right)
			.sidebar {
				border-right: none;
				border-left: 1px solid var(--border-color);
			}
		}
		&.sidebar-position-right { // When sidebar is on the right in RTL (visually left)
			.sidebar {
				border-left: none;
				border-right: 1px solid var(--border-color);
			}
		}
	}
}
</style>

=== src/components/auction/de/DeAuctioneerStatus.vue
<template>
	<n-card size="small" title="Auction Status">
		<n-descriptions label-placement="left" :column="1" size="small">
			<n-descriptions-item label="Auctioneer State">
				<n-tag :type="statusType(status?.auctioneer_state)" size="small">
					{{ status?.auctioneer_state_text || 'N/A' }}
				</n-tag>
			</n-descriptions-item>
			<n-descriptions-item label="Common State">
				<n-tag :type="statusType(commonStatus?.common_state)" size="small">
					{{ commonStatus?.common_state_text || 'N/A' }}
				</n-tag>
			</n-descriptions-item>
			 <n-descriptions-item label="Round">
				{{ commonStatus?.round_number ?? 'N/A' }}
			 </n-descriptions-item>
			 <n-descriptions-item label="Price">
				 {{ status?.starting_price ?? commonStatus?.round_price ?? 'N/A' }}
			 </n-descriptions-item>
			 <n-descriptions-item label="Price Direction">
				 {{ commonStatus?.price_direction || 'N/A' }}
			 </n-descriptions-item>
			 <n-descriptions-item label="Reversed">
				 {{ commonStatus?.price_has_reversed ? 'Yes' : 'No' }}
			 </n-descriptions-item>
			 <n-descriptions-item label="Overshot">
				 {{ status?.price_has_overshot ? 'Yes' : 'No' }}
			 </n-descriptions-item>
			 <n-descriptions-item label="Excess Side">
				 {{ status?.excess_side || 'N/A' }}
			 </n-descriptions-item>
			 <n-descriptions-item label="Excess Level">
				 {{ status?.excess_level || 'N/A' }}
			 </n-descriptions-item>
			 <n-descriptions-item label="Awardable">
				 {{ status?.awardable ? 'Yes' : 'No' }}
			 </n-descriptions-item>
			 <n-descriptions-item label="Autopilot">
				 {{ status?.autopilot || 'N/A' }}
			 </n-descriptions-item>
			 <!-- Add more relevant fields -->
		</n-descriptions>
	</n-card>
</template>

<script setup lang="ts">
import type { DeAuctioneerStatusValue, DeCommonStatusValue, DeAuctioneerState, DeCommonState } from '@/types/generated';
import { NCard, NDescriptions, NDescriptionsItem, NTag } from 'naive-ui';
import { computed } from 'vue';

const props = defineProps<{
	status: DeAuctioneerStatusValue | null;
	commonStatus: DeCommonStatusValue | null;
}>();

function statusType(state: DeAuctioneerState | DeCommonState | undefined): 'success' | 'warning' | 'error' | 'info' | 'default' {
	switch (state) {
		case DeAuctioneerState.AUCTION_CLOSED:
		case DeCommonState.AUCTION_CLOSED:
			return 'success';
		case DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_NOT_IN:
		case DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN:
		case DeCommonState.ROUND_OPEN:
			return 'info';
		case DeAuctioneerState.ROUND_CLOSED_AWARDABLE:
			return 'success';
		case DeAuctioneerState.ROUND_CLOSED_NOT_AWARDABLE:
			return 'warning';
		case DeAuctioneerState.STARTING_PRICE_NOT_SET:
			return 'error';
		default:
			return 'default';
	}
}
</script>

=== src/components/auction/de/DeAuctioneerControls.vue
<template>
	<n-card size="small" title="Controls">
		<n-space align="center">
			<n-input-group v-if="showPriceInput">
				<n-input-number
					v-model:value="startingPrice"
					:show-button="false"
					placeholder="Starting Price"
					:precision="3"
					step="0.1"
					size="small"
					style="width: 120px"
				/>
				<n-button
					type="primary"
					size="small"
					:disabled="!controls?.[DeFlowControlType.SET_STARTING_PRICE] || startingPrice === null"
					@click="sendFlowControl(DeFlowControlType.SET_STARTING_PRICE)"
				>
					Set Price
				</n-button>
			</n-input-group>

			<n-button
				v-if="!showPriceInput"
				size="small"
				:disabled="!controls?.[DeFlowControlType.ANNOUNCE_STARTING_PRICE]"
				@click="sendFlowControl(DeFlowControlType.ANNOUNCE_STARTING_PRICE)"
			>
				Announce Price
			</n-button>
			<n-button
				size="small"
				:disabled="!controls?.[DeFlowControlType.START_AUCTION]"
				@click="sendFlowControl(DeFlowControlType.START_AUCTION)"
			>
				Start Auction
			</n-button>
			<n-button
				size="small"
				:disabled="!controls?.[DeFlowControlType.CLOSE_ROUND]"
				@click="sendFlowControl(DeFlowControlType.CLOSE_ROUND)"
			>
				Close Round
			</n-button>
			<n-button
				size="small"
				:disabled="!controls?.[DeFlowControlType.NEXT_ROUND]"
				@click="sendFlowControl(DeFlowControlType.NEXT_ROUND)"
			>
				Next Round
			</n-button>
			<n-button
				size="small"
				:disabled="!controls?.[DeFlowControlType.REOPEN_ROUND]"
				@click="sendFlowControl(DeFlowControlType.REOPEN_ROUND)"
			>
				Reopen Round
			</n-button>
			<n-button
				type="primary"
				size="small"
				:disabled="!controls?.[DeFlowControlType.AWARD_AUCTION]"
				@click="sendFlowControl(DeFlowControlType.AWARD_AUCTION)"
			>
				Award Auction
			</n-button>
			<!-- Add Autopilot Toggle if needed -->
		</n-space>
	</n-card>
</template>

<script setup lang="ts">
import type { DeAuctioneerState, DeFlowControlType as DeFlowControlTypeEnum } from '@/types/generated';
import { ref, computed } from 'vue';
import { NCard, NSpace, NButton, NInputGroup, NInputNumber, useMessage } from 'naive-ui';
import { useWebSocket } from '@/services/websocket';
import { DeFlowControlType, DeAuctioneerState as DeAuctioneerStateEnum } from '@/types/generated';

const props = defineProps<{
	controls: Record<string, boolean> | null | undefined;
	auctionId: string;
	currentState: DeAuctioneerState | undefined;
}>();

const ws = useWebSocket();
const message = useMessage();
const startingPrice = ref<number | null>(null);

const showPriceInput = computed(() => {
	return props.currentState === DeAuctioneerStateEnum.STARTING_PRICE_NOT_SET ||
		   props.currentState === DeAuctioneerStateEnum.STARTING_PRICE_SET;
});

function sendFlowControl(controlType: DeFlowControlTypeEnum) {
	if (!props.auctionId) return;

	let priceToSend: string | null = null;
	if (controlType === DeFlowControlType.SET_STARTING_PRICE) {
		if (startingPrice.value === null || isNaN(startingPrice.value)) {
			message.error("Please enter a valid starting price.");
			return;
		}
		priceToSend = startingPrice.value.toFixed(3); // Ensure correct format if needed
	}

	ws.sendCommand({
		'@type': 'au21.engine.domain.de.commands.DeFlowControlCommand',
		auction_id: props.auctionId,
		control: controlType,
		starting_price: priceToSend,
	});
	message.info(`Control command '${controlType}' sent.`);
}
</script>

=== src/components/auction/de/DeAuctioneerInfo.vue
<template>
	<n-card size="small" title="Auction Info">
		 <n-descriptions label-placement="left" :column="1" size="small">
			 <n-descriptions-item label="Last Round">{{ info?.last_round ?? 'N/A' }}</n-descriptions-item>
			 <n-descriptions-item label="Last Buyers">{{ info?.last_buyers ?? 'N/A' }}</n-descriptions-item>
			 <n-descriptions-item label="Last Sellers">{{ info?.last_sellers ?? 'N/A' }}</n-descriptions-item>
			 <n-descriptions-item label="Last Total Buy">{{ info?.last_total_buy ?? 'N/A' }}</n-descriptions-item>
			 <n-descriptions-item label="Last Total Sell">{{ info?.last_total_sell ?? 'N/A' }}</n-descriptions-item>
			 <n-descriptions-item label="Last Match">{{ info?.last_match ?? 'N/A' }}</n-descriptions-item>
			 <n-descriptions-item label="Last Excess">{{ info?.last_excess ?? 'N/A' }}</n-descriptions-item>
			 <n-descriptions-item label="Potential Flow">{{ info?.potential ?? 'N/A' }}</n-descriptions-item>
			 <!-- Add Penultimate round info if needed -->
		 </n-descriptions>
	</n-card>
</template>

<script setup lang="ts">
import type { DeAuctioneerInfoValue } from '@/types/generated';
import { NCard, NDescriptions, NDescriptionsItem } from 'naive-ui';

const props = defineProps<{
	info: DeAuctioneerInfoValue | null;
}>();
</script>

=== src/components/auction/de/DeBlotterView.vue
<template>
	<div>Placeholder for DeBlotterView</div>
</template>
// Implementation will involve NDataTable with complex column rendering based on DeBlotter structure

=== src/components/auction/de/DeMatrixView.vue
<template>
	<div>Placeholder for DeMatrixView</div>
</template>
// Implementation might use NDataTable or a custom grid/SVG for visualization

=== src/components/auction/de/DeAwardSummary.vue
<template>
	<div>Placeholder for DeAwardSummary</div>
</template>
// Display DeAwardValue using NCard, NList, NDataTable

=== src/components/auction/de/DeParticipantList.vue
<template>
	<div>Placeholder for DeParticipantList</div>
</template>
// Display traders and online status using NList or NDataTable

=== src/components/auction/de/DeMessaging.vue
<template>
	<div>Placeholder for DeMessaging</div>
</template>
// Display messages and provide input to send messages

=== src/components/auction/de/DeSettingsView.vue
<template>
	<div>Placeholder for DeSettingsView</div>
</template>
// Display DeSettingsValue, potentially read-only

=== src/components/auction/de/DeTraderStatus.vue
<template>
	<div>Placeholder for DeTraderStatus</div>
</template>
// Display common status and relevant trader info

=== src/components/auction/de/DeTraderBiddingPanel.vue
<template>
	<div>Placeholder for DeTraderBiddingPanel</div>
</template>
// Form for submitting bids

=== src/components/auction/de/DeTraderHistory.vue
<template>
	<div>Placeholder for DeTraderHistory</div>
</template>
// Table showing trader's bid history

=== src/components/auction/de/DeTraderInfoDisplay.vue
<template>
	<div>Placeholder for DeTraderInfoDisplay</div>
</template>
// Display trader-specific limits and award info


=== src/stores/appStore.ts
import type {
	AuctionRowElement,
	CompanyElement,
	CounterpartyCreditElement,
	MessageElement,
	SessionUserValue,
	TimeValue,
	UserElement
} from "@/types/generated"; // Assuming generated types exist
import type { DeAuctionValue, DeMatrixRoundElement } from "@/types/generated"; // Assuming generated types exist
import { defineStore } from "pinia";

// Mirroring the backend's LiveClientStore structure
interface AppState {
	auction_rows: AuctionRowElement[];
	companies: CompanyElement[];
	counterparty_credits: CounterpartyCreditElement[];
	de_auction: DeAuctionValue | null;
	session_user: SessionUserValue | null;
	time: TimeValue | null;
	users: UserElement[];
	// Add a specific place for matrix rounds if needed frequently outside de_auction
	de_matrix_rounds: DeMatrixRoundElement[];
	messages: MessageElement[]; // Assuming messages might be added incrementally
	websocketConnected: boolean;
	lastMessageTimestamp: number | null;
}

export const useAppStore = defineStore("app", {
	state: (): AppState => ({
		auction_rows: [],
		companies: [],
		counterparty_credits: [],
		de_auction: null,
		session_user: null,
		time: null,
		users: [],
		de_matrix_rounds: [],
		messages: [],
		websocketConnected: false,
		lastMessageTimestamp: null,
	}),

	actions: {
		setLiveStore(storeData: Partial<AppState>) {
			// Deep merge might be better, but direct assignment matches backend push
			// Consider using lodash merge or similar if partial updates are expected
			// within SetLiveStore, otherwise, this assumes a full state push.
			if (storeData.auction_rows !== undefined) this.auction_rows = storeData.auction_rows;
			if (storeData.companies !== undefined) this.companies = storeData.companies;
			if (storeData.counterparty_credits !== undefined) this.counterparty_credits = storeData.counterparty_credits;
			if (storeData.de_auction !== undefined) this.de_auction = storeData.de_auction;
			if (storeData.session_user !== undefined) this.session_user = storeData.session_user;
			if (storeData.time !== undefined) this.time = storeData.time;
			if (storeData.users !== undefined) this.users = storeData.users;

			// If messages/matrix rounds are part of LiveClientStore, update them here too.
			// If they are only updated via AddElements, handle them there.
			if (storeData.de_auction?.messages) {
				this.messages = storeData.de_auction.messages;
			}
			if (storeData.de_auction?.matrix_last_round) {
				// Assuming only the last round is sent in the main store push
				this.de_matrix_rounds = [storeData.de_auction.matrix_last_round];
			}

			console.log("LiveStore updated"); // Reduced verbosity
		},

		addElements(path: string, elements: any[] | null) {
			console.log(`AddElements received for path: ${path}`, elements ? `${elements.length} elements` : 'null (clear)');
			if (elements === null) {
				// Handle clearing the array
				if (path === "AuctionRowElement") this.auction_rows = [];
				else if (path === "CompanyElement") this.companies = [];
				else if (path === "CounterpartyCreditElement") this.counterparty_credits = [];
				else if (path === "MessageElement") this.messages = [];
				else if (path === "UserElement") this.users = [];
				else if (path === "DeMatrixRoundElement") this.de_matrix_rounds = [];
				// Add other paths as needed
				else console.warn(`AddElements: Unknown path to clear: ${path}`);
				return;
			}

			if (!Array.isArray(elements)) {
				console.error("AddElements: received non-array elements", elements);
				return;
			}

			// Find the target array and update/add elements
			let targetArray: any[] | undefined;
			if (path === "AuctionRowElement") targetArray = this.auction_rows;
			else if (path === "CompanyElement") targetArray = this.companies;
			else if (path === "CounterpartyCreditElement") targetArray = this.counterparty_credits;
			else if (path === "MessageElement") targetArray = this.messages;
			else if (path === "UserElement") targetArray = this.users;
			else if (path === "DeMatrixRoundElement") targetArray = this.de_matrix_rounds;
			// Add other paths as needed

			if (targetArray) {
				elements.forEach(newElement => {
					if (!newElement || typeof newElement.id === 'undefined') {
						console.warn("AddElements: Skipping element without id", newElement);
						return;
					}
					const index = targetArray.findIndex(item => item.id === newElement.id);
					if (index !== -1) {
						// Update existing element (important for reactivity)
						// Use spread or Object.assign for better reactivity handling if needed
						targetArray[index] = { ...targetArray[index], ...newElement };
					} else {
						// Add new element
						targetArray.push(newElement);
					}
				});
				// Optional: Sort arrays if needed after updates
				if (path === 'MessageElement') this.messages.sort((a, b) => a.timestamp - b.timestamp);
				if (path === 'DeMatrixRoundElement') this.de_matrix_rounds.sort((a, b) => a.round_number - b.round_number);

			} else {
				console.warn(`AddElements: Unknown path: ${path}`);
			}
		},

		setWebsocketConnected(status: boolean) {
			this.websocketConnected = status;
		},

		setLastMessageTimestamp(ts: number) {
			this.lastMessageTimestamp = ts;
		},

		resetState() {
			this.$reset();
			console.log("AppStore state reset");
		}
	},
	getters: {
		isAuctioneer: (state) => state.session_user?.isAuctioneer ?? false,
		isTrader: (state) => !state.session_user?.isAuctioneer && !!state.session_user?.role, // Assuming non-auctioneer logged in is trader
		currentSessionId: (state) => state.session_user?.session_id,
		currentPage: (state) => state.session_user?.current_page,
		currentAuctionId: (state) => state.session_user?.current_auction_id,
		// Add more specific getters as needed e.g., getTraderInfo, getAuctioneerStatus
		getDeAuctioneerStatus: (state) => state.de_auction?.auctioneer_status,
		getDeCommonStatus: (state) => state.de_auction?.common_status,
		getDeTraderInfo: (state) => state.de_auction?.trader_info,
		getDeTraderHistory: (state) => state.de_auction?.trader_history_rows ?? [],
		getDeBlotter: (state) => state.de_auction?.blotter,
		getDeMatrixLastRound: (state) => state.de_auction?.matrix_last_round,
		getMessagesForCurrentAuction: (state) => {
			// Filter messages based on current auction context if needed,
			// or assume messages in the store are already filtered by backend push
			// Ensure sorting happens here if AddElements doesn't guarantee order
			return [...state.messages].sort((a, b) => a.timestamp - b.timestamp);
		}
	}
});

=== src/services/websocket.ts
import { useAppStore } from "@/stores/appStore";
import { useAuthStore } from "@/stores/auth";
import { useMainStore } from "@/stores/main";
import { useGlobalActions } from "@/composables/useGlobalActions";
import { ref, watch } from "vue";
import { i18nGlobal } from "@/lang"; // Assuming i18n setup for messages

// Assuming generated types exist
import type { ClientCommand, EngineCommand, EngineCommandEnvelope } from "@/types/generated";
import { CommandType, BrowserMessageKind } from "@/types/generated";

// Make WS URL configurable via environment variables
const WS_BASE_URL = import.meta.env.VITE_WS_URL || 'ws://localhost:8080/socket'; // Default fallback

let websocket: WebSocket | null = null;
let reconnectAttempts = 0;
const maxReconnectAttempts = 5;
const reconnectInterval = 5000; // 5 seconds
let reconnectTimeout: NodeJS.Timeout | null = null;
let currentSessionId = ref<string | null>(null); // Track the session ID we *should* be connected with

export function useWebSocket() {
	const appStore = useAppStore();
	const authStore = useAuthStore();
	const mainStore = useMainStore();
	const { message: showMessage, notification: showNotification } = useGlobalActions(); // Assuming global setup

	// Watch for session ID changes to connect/disconnect
	// This is the primary trigger for connection management
	watch(() => appStore.currentSessionId, (newSid, oldSid) => {
		console.log(`[WS] Session ID watcher: changed from ${oldSid} to ${newSid}`);
		if (newSid && newSid !== currentSessionId.value) {
			console.log(`[WS] New session ID detected (${newSid}), initiating connection.`);
			currentSessionId.value = newSid; // Update the target session ID
			disconnectWebSocket(false); // Disconnect old if exists, don't trigger reconnect logic
			connectWebSocket(newSid);
		} else if (!newSid && currentSessionId.value) {
			console.log("[WS] Session ID removed, disconnecting.");
			currentSessionId.value = null; // Clear target session ID
			disconnectWebSocket(false); // Disconnect cleanly
		}
	}, { immediate: true });

	// Watch for login status changes (secondary check, mainly for logout)
	watch(() => authStore.isLogged, (loggedIn) => {
		console.log(`[WS] Auth state watcher: loggedIn=${loggedIn}`);
		if (!loggedIn && currentSessionId.value) {
			console.log("[WS] User logged out, disconnecting WebSocket.");
			currentSessionId.value = null; // Clear target session ID
			disconnectWebSocket(false); // Clean disconnect on logout
			appStore.resetState(); // Clear app state on logout
		}
		// Connection on login is handled by the session_id watch
	});

	function getWebSocketURL(sessionId: string): string {
		// Add browser info as query params, matching backend expectations
		const browserInfo = {
			browser_name: navigator.userAgentData?.brands?.[0]?.brand || navigator.vendor || "Unknown",
			browser_version: navigator.userAgentData?.brands?.[0]?.version || navigator.appVersion || "Unknown",
			browser_os: navigator.platform || "Unknown",
		};
		const queryParams = new URLSearchParams(browserInfo).toString();
		const url = `${WS_BASE_URL}/${sessionId}`; // Use template literal
		return `${url}?${queryParams}`;
	}

	function connectWebSocket(sessionId: string) {
		if (!sessionId) {
			console.error("[WS] Cannot connect: No Session ID provided.");
			return;
		}
		// Prevent multiple connections for the same session ID
		if (websocket && websocket.readyState !== WebSocket.CLOSED && websocket.url.includes(sessionId)) {
			console.log(`[WS] Already connected or connecting to session ${sessionId}.`);
			return;
		}
		// If a WS exists for a *different* session, disconnect it first
		if (websocket && websocket.readyState !== WebSocket.CLOSED && !websocket.url.includes(sessionId)) {
			console.warn(`[WS] Disconnecting obsolete WebSocket for session ${websocket.url.split('/').pop()?.split('?')[0]}`);
			disconnectWebSocket(false);
		}


		const url = getWebSocketURL(sessionId);
		console.log(`[WS] Connecting to: ${url}`);
		appStore.setWebsocketConnected(false);
		mainStore.loadingBar?.start();

		try {
			websocket = new WebSocket(url);
		} catch (error) {
			console.error("[WS] Failed to create WebSocket:", error);
			mainStore.loadingBar?.error();
			scheduleReconnect(sessionId); // Attempt reconnect even if creation fails
			return;
		}


		websocket.onopen = () => {
			// Double check if this is still the desired session before proceeding
			if (currentSessionId.value !== sessionId) {
				console.warn(`[WS] Connection opened for ${sessionId}, but desired session is now ${currentSessionId.value}. Closing.`);
				disconnectWebSocket(false);
				return;
			}
			console.log(`[WS] Connection established for session ${sessionId}.`);
			appStore.setWebsocketConnected(true);
			reconnectAttempts = 0; // Reset attempts on successful connection
			clearTimeout(reconnectTimeout!);
			mainStore.loadingBar?.finish();
			// Backend should send ClientSocketCommand(OPENED) and then push initial state
		};

		websocket.onmessage = (event) => {
			// Ensure this message is for the *current* session
			if (currentSessionId.value !== sessionId) {
				console.warn(`[WS] Received message for obsolete session ${sessionId}. Ignoring.`);
				return;
			}
			appStore.setLastMessageTimestamp(Date.now());
			// Assume backend sends gzipped binary data - Placeholder for ungzip
			if (event.data instanceof Blob) {
				const reader = new FileReader();
				reader.onload = function () {
					try {
						const arrayBuffer = reader.result as ArrayBuffer;
						// *** Placeholder for ungzip ***
						// Replace this with actual ungzip logic using pako or similar
						console.warn("[WS] Binary message received, ungzip not implemented. Decoding as UTF-8 text.");
						const textDecoder = new TextDecoder("utf-8");
						handleMessage(textDecoder.decode(arrayBuffer));
						// *** End Placeholder ***

					} catch (error) {
						console.error("[WS] Error processing binary message:", error);
						mainStore.loadingBar?.error();
					}
				};
				reader.onerror = (error) => {
					console.error("[WS] Error reading Blob:", error);
					mainStore.loadingBar?.error();
				};
				reader.readAsArrayBuffer(event.data);
			} else if (typeof event.data === 'string') {
				// Handle plain text messages
				// console.log("[WS] Received Text Message (raw):", event.data.substring(0, 200) + "...");
				handleMessage(event.data);
			} else {
				console.warn("[WS] Received unexpected message type:", typeof event.data);
			}
		};

		websocket.onerror = (error) => {
			console.error(`[WS] Error for session ${sessionId}:`, error);
			// Only attempt reconnect if this is the currently active session
			if (currentSessionId.value === sessionId) {
				appStore.setWebsocketConnected(false);
				mainStore.loadingBar?.error();
				showMessage(i18nGlobal.t('errors.websocketConnection'), { type: 'error', duration: 0, closable: true });
				scheduleReconnect(sessionId);
			} else {
				console.log(`[WS] Ignoring error for obsolete session ${sessionId}.`);
			}
		};

		websocket.onclose = (event) => {
			console.log(`[WS] Connection closed for session ${sessionId}: Code=${event.code}, Reason=${event.reason}, Clean=${event.wasClean}`);
			// Only manage state and reconnect if this was the *intended* active connection
			if (currentSessionId.value === sessionId) {
				appStore.setWebsocketConnected(false);
				websocket = null; // Clear the reference only if it's the current one closing

				// Reconnect only if:
				// 1. It wasn't a clean close (code 1000 is normal) OR it was closed unexpectedly (e.g., server restart 1006)
				// 2. The user is still supposed to be logged in (authStore.isLogged)
				// 3. This session ID is still the one we want (currentSessionId.value === sessionId)
				if ((!event.wasClean || event.code === 1006) && authStore.isLogged) {
					console.log("[WS] Unclean close or server issue, attempting reconnect...");
					showMessage(i18nGlobal.t('errors.websocketDisconnected'), { type: 'warning' });
					mainStore.loadingBar?.error();
					scheduleReconnect(sessionId);
				} else {
					console.log("[WS] Clean close or user logged out/session changed, not reconnecting.");
					mainStore.loadingBar?.finish(); // Finish loading bar if closed cleanly
				}
			} else {
				console.log(`[WS] Ignoring close event for obsolete session ${sessionId}.`);
				// If the closed websocket is the one stored in the global `websocket` variable, clear it
				if (websocket === event.target) {
					websocket = null;
				}
			}
		};
	}

	function scheduleReconnect(sessionId: string) {
		// Only schedule if this is still the desired session
		if (currentSessionId.value !== sessionId) {
			console.log(`[WS] Reconnect cancelled for ${sessionId}, desired session is ${currentSessionId.value}.`);
			return;
		}

		if (reconnectAttempts >= maxReconnectAttempts) {
			console.error("[WS] Max reconnect attempts reached. Giving up.");
			showMessage(i18nGlobal.t('errors.websocketReconnectFailed'), { type: 'error', duration: 0, closable: true });
			// Optional: Force logout or redirect to login
			// authStore.setLogout(); // This might trigger disconnectWebSocket via watcher
			// router.push('/login');
			return;
		}

		clearTimeout(reconnectTimeout!); // Clear any existing timeout
		reconnectAttempts++;
		const delay = reconnectInterval * Math.pow(2, reconnectAttempts - 1); // Exponential backoff
		console.log(`[WS] Scheduling reconnect attempt ${reconnectAttempts}/${maxReconnectAttempts} for session ${sessionId} in ${delay / 1000}s`);

		reconnectTimeout = setTimeout(() => {
			// Double-check the session ID *again* before actually connecting
			if (currentSessionId.value === sessionId) {
				console.log(`[WS] Attempting reconnect ${reconnectAttempts} for session ${sessionId}...`);
				connectWebSocket(sessionId);
			} else {
				console.log(`[WS] Reconnect attempt ${reconnectAttempts} cancelled for ${sessionId}, desired session changed to ${currentSessionId.value}.`);
				reconnectAttempts = 0; // Reset attempts as we are no longer trying for this session
			}
		}, delay);
	}

	function disconnectWebSocket(clean = true) {
		clearTimeout(reconnectTimeout!); // Always clear reconnect timeout on disconnect
		reconnectAttempts = 0; // Reset attempts
		if (websocket) {
			const reason = clean ? "Client requested disconnect" : "Switching sessions";
			console.log(`[WS] Disconnecting WebSocket (${reason}). State: ${websocket.readyState}`);
			if (websocket.readyState === WebSocket.OPEN || websocket.readyState === WebSocket.CONNECTING) {
				websocket.close(1000, reason); // 1000 is normal closure
			}
			websocket = null; // Clear the reference
		}
		// Only update store if it reflects the current state intention
		if (appStore.websocketConnected) {
			appStore.setWebsocketConnected(false);
		}
	}


	function handleMessage(messageJson: string) {
		try {
			// console.log("[WS] Handling Message:", messageJson.substring(0, 200) + "...");
			const clientCommand = JSON.parse(messageJson) as ClientCommand; // Assuming ClientCommand is the base type

			switch (clientCommand.command) {
				case CommandType.SetLiveStore:
					const storeCommand = clientCommand as ClientCommand.StoreCommand.SetLiveStore;
					appStore.setLiveStore(storeCommand.store);
					break;
				case CommandType.AddElements:
					const addElementsCommand = clientCommand as ClientCommand.StoreCommand.AddElements;
					appStore.addElements(addElementsCommand.path, addElementsCommand.elements);
					break;
				case CommandType.ShowMessage:
					const showMessageCommand = clientCommand as ClientCommand.ShowMessage;
					const messageType = showMessageCommand.browser_message_kind === BrowserMessageKind.ALERT ? 'error' : 'info'; // Or map more granularly
					const messageText = showMessageCommand.message.join('\n');
					if (messageType === 'error') {
						showMessage(messageText, { type: 'error', duration: 5000, closable: true });
					} else {
						showNotification({
							content: messageText,
							type: messageType, // info, success, warning
							duration: 4000,
							keepAliveOnHover: true
						});
					}
					break;
				case CommandType.TerminateSession:
					const terminateCommand = clientCommand as ClientCommand.TerminateSession;
					console.log("[WS] Session termination requested by server:", terminateCommand.message);
					showMessage(terminateCommand.message || i18nGlobal.t('errors.sessionTerminated'), { type: 'error', duration: 0, closable: true });
					authStore.setLogout(); // This will trigger watchers to disconnect WS and clear state
					// Router guard should handle redirect
					break;
				case CommandType.NetworkDown:
					console.warn("[WS] Backend indicated network issues.");
					showMessage(i18nGlobal.t('errors.networkDown'), { type: 'warning' });
					break;
				case CommandType.NetworkUp:
					console.info("[WS] Backend indicated network is back up.");
					showMessage(i18nGlobal.t('info.networkUp'), { type: 'success' });
					break;
				case CommandType.CommandSucceeded:
					console.log("[WS] Command succeeded ack received.");
					break;
				default:
					console.warn("[WS] Received unknown command type:", clientCommand.command);
			}
		} catch (error) {
			console.error("[WS] Error parsing or handling WebSocket message:", error, "\nMessage was:", messageJson);
			showMessage(i18nGlobal.t('errors.websocketMessageError'), { type: 'error' });
		}
	}

	function sendCommand(command: EngineCommand) {
		if (!currentSessionId.value) {
			console.error("[WS] No active session ID. Cannot send command.");
			showMessage(i18nGlobal.t('errors.sendCommandNoSession'), { type: 'error' });
			return;
		}
		if (!websocket || websocket.readyState !== WebSocket.OPEN) {
			console.error("[WS] WebSocket is not connected. Cannot send command.");
			showMessage(i18nGlobal.t('errors.websocketNotConnected'), { type: 'error' });
			// Optional: Attempt reconnect or queue command? Maybe trigger connect if not connecting
			if (!websocket || websocket.readyState === WebSocket.CLOSED) {
				console.log("[WS] Attempting to reconnect before sending command...");
				connectWebSocket(currentSessionId.value);
				// Maybe queue the command to send after successful connection?
			}
			return;
		}


		// Construct the envelope - Backend needs to handle missing simplename/classname
		const envelope: Partial<EngineCommandEnvelope> = {
			session_id: currentSessionId.value,
			command: command,
		};

		try {
			const messageJson = JSON.stringify(envelope);
			console.log("[WS] Sending Command:", command.constructor.name, JSON.stringify(command).substring(0,100)+"...");
			// TODO: Gzip if required by backend
			websocket.send(messageJson);
		} catch (error) {
			console.error("[WS] Error sending command:", error);
			showMessage(i18nGlobal.t('errors.sendCommandFailed'), { type: 'error' });
		}
	}

	return {
		// connectWebSocket, // Connection is now managed internally by session ID changes
		disconnectWebSocket, // Expose for explicit disconnect (e.g., logout button)
		sendCommand,
		websocketConnected: computed(() => appStore.websocketConnected),
		lastMessageTimestamp: computed(() => appStore.lastMessageTimestamp),
	};
}

=== src/router/index.ts
import type { FormType } from "@/components/auth/types.d";
import { Layout, PageName } from "@/types/generated"; // Assuming PageName is generated
import { authCheck } from "@/utils/auth"; // Corrected import path
import { createRouter, createWebHistory } from "vue-router";

// Helper to map backend PageName to frontend route names/paths
// Adjust paths/names as needed to match your preferred frontend routing structure
const pageNameToRoute = {
	[PageName.LOGIN_PAGE]: { name: "Login", path: "/login" },
	[PageName.HOME_PAGE]: { name: "Home", path: "/" }, // Map to dashboard or appropriate home
	[PageName.USER_PAGE]: { name: "UserManagement", path: "/admin/users" },
	[PageName.COMPANY_PAGE]: { name: "CompanyManagement", path: "/admin/companies" }, // Assuming a dedicated page
	[PageName.SESSION_PAGE]: { name: "SessionManagement", path: "/admin/sessions" },
	[PageName.DE_SETUP_PAGE]: { name: "DeAuctionSetup", path: "/de/setup" }, // Can be combined with edit using :id?
	[PageName.DE_AUCTIONEER_PAGE]: { name: "DeAuctioneer", path: "/de/auction/:id/auctioneer" },
	[PageName.DE_TRADER_PAGE]: { name: "DeTrader", path: "/de/auction/:id/trader" },
	// Add mappings for other PageNames (CREDITOR, BH, MR, TE, TO) if they exist and have UIs
	[PageName.CREDITOR_AUCTIONEER_PAGE]: { name: "CreditorAuctioneer", path: "/creditor/auction/:id/auctioneer" },
	[PageName.CREDITOR_TRADER_PAGE]: { name: "CreditorTrader", path: "/creditor/auction/:id/trader" },
	// ... add others like BH_AUCTIONEER_PAGE etc. if needed
};

const router = createRouter({
	history: createWebHistory(import.meta.env.BASE_URL),
	routes: [
		// Redirect root to a default dashboard or home based on role later
		{
			path: "/",
			name: pageNameToRoute[PageName.HOME_PAGE].name, // Use mapping
			component: () => import("@/views/Dashboard/Analytics.vue"), // Use created placeholder
			meta: { title: "Dashboard", auth: true, roles: "all" },
		},

		// --- Authentication ---
		{
			path: pageNameToRoute[PageName.LOGIN_PAGE].path,
			name: pageNameToRoute[PageName.LOGIN_PAGE].name,
			component: () => import("@/views/Auth/Login.vue"), // Use created placeholder
			meta: {
				title: "Login",
				theme: { layout: Layout.Blank, boxed: { enabled: false }, padded: { enabled: false } },
				checkAuth: true, // Redirect if already logged in
				skipPin: true,
			},
		},
		// Add Register and ForgotPassword if needed, mapping them appropriately
		{
			path: "/register", // No direct PageName mapping, assuming standard flow
			name: "Register",
			component: () => import("@/views/Auth/Login.vue"), // Reuse Login view
			props: { formType: "signup" as FormType },
			meta: {
				title: "Register",
				theme: { layout: Layout.Blank, boxed: { enabled: false }, padded: { enabled: false } },
				checkAuth: true,
				skipPin: true,
			},
		},
		{
			path: "/forgot-password", // No direct PageName mapping
			name: "ForgotPassword",
			component: () => import("@/views/Auth/Login.vue"), // Reuse Login view
			props: { formType: "forgotpassword" as FormType },
			meta: {
				title: "Forgot Password",
				theme: { layout: Layout.Blank, boxed: { enabled: false }, padded: { enabled: false } },
				checkAuth: true,
				skipPin: true,
			},
		},
		{
			path: "/logout",
			name: "Logout",
			redirect: pageNameToRoute[PageName.LOGIN_PAGE].path, // Redirect to login after logout logic
		},

		// --- Admin / Auctioneer Specific ---
		{
			path: "/admin", // Group admin routes
			redirect: "/admin/users",
			meta: { auth: true, roles: ["AUCTIONEER"] }, // Role guard
			children: [
				{
					path: "users",
					name: pageNameToRoute[PageName.USER_PAGE].name,
					component: () => import("@/views/Admin/UserManagement.vue"), // Use created view
					meta: { title: "User Management" },
				},
				{
					path: "companies",
					name: pageNameToRoute[PageName.COMPANY_PAGE].name,
					component: () => import("@/views/Admin/CompanyManagement.vue"), // Use created view
					meta: { title: "Company Management" },
				},
				{
					path: "sessions",
					name: pageNameToRoute[PageName.SESSION_PAGE].name,
					component: () => import("@/views/Admin/SessionManagement.vue"), // Use created view
					meta: { title: "Session Management" },
				},
			],
		},

		// --- DE Auction ---
		{
			path: "/de", // Group DE routes
			redirect: pageNameToRoute[PageName.HOME_PAGE].path, // Redirect to home/dashboard
			meta: { auth: true, roles: "all" }, // Allow both roles, specific views handle finer control
			children: [
				{
					path: "setup", // Route for creating a new auction
					name: pageNameToRoute[PageName.DE_SETUP_PAGE].name + "Create", // Differentiate create/edit
					component: () => import("@/views/Auction/DeAuctionSetup.vue"), // Use created view
					meta: { title: "Create DE Auction", roles: ["AUCTIONEER"] },
				},
				{
					path: "setup/:id", // Route for editing an existing auction
					name: pageNameToRoute[PageName.DE_SETUP_PAGE].name + "Edit",
					component: () => import("@/views/Auction/DeAuctionSetup.vue"), // Reuse the setup view
					props: true, // Pass route params as props
					meta: { title: "Edit DE Auction", roles: ["AUCTIONEER"] },
				},
				{
					path: "auction/:id/auctioneer",
					name: pageNameToRoute[PageName.DE_AUCTIONEER_PAGE].name,
					component: () => import("@/views/Auction/DeAuctioneer.vue"), // Use created view
					props: true,
					meta: { title: "DE Auctioneer View", roles: ["AUCTIONEER"] },
				},
				{
					path: "auction/:id/trader",
					name: pageNameToRoute[PageName.DE_TRADER_PAGE].name,
					component: () => import("@/views/Auction/DeTrader.vue"), // Use created view
					props: true,
					meta: { title: "DE Trader View", roles: ["TRADER"] },
				},
			],
		},

		// --- Add routes for other auction types (Creditor, BH, MR, TE, TO) similarly ---
		// Example for Creditor
		{
			path: "/creditor",
			redirect: pageNameToRoute[PageName.HOME_PAGE].path, // Or a specific creditor dashboard/list
			meta: { auth: true, roles: "all" },
			children: [
				// Add setup/edit routes if applicable
				{
					path: "auction/:id/auctioneer",
					name: pageNameToRoute[PageName.CREDITOR_AUCTIONEER_PAGE].name,
					component: () => import("@/views/Auction/CreditorAuctioneer.vue"), // Use created placeholder
					props: true,
					meta: { title: "Creditor Auctioneer View", roles: ["AUCTIONEER"] },
				},
				{
					path: "auction/:id/trader",
					name: pageNameToRoute[PageName.CREDITOR_TRADER_PAGE].name,
					component: () => import("@/views/Auction/CreditorTrader.vue"), // Use created placeholder
					props: true,
					meta: { title: "Creditor Trader View", roles: ["TRADER"] },
				},
			]
		},


		// --- Catchall ---
		{
			path: "/:pathMatch(.*)*",
			name: "NotFound",
			component: () => import("@/views/NotFound.vue"),
			meta: {
				theme: { layout: Layout.Blank, boxed: { enabled: false }, padded: { enabled: false } },
				skipPin: true,
			},
		},
	],
});

router.beforeEach(route => {
	return authCheck(route); // Use Pinx's auth check utility
});

export default router;

=== src/utils/auth.ts
import type { Role, Roles, RouteMetaAuth } from "@/types/auth.d"; // Ensure Roles is defined correctly here
import type { RouteLocationNormalized, NavigationGuardNext } from "vue-router";
import { useAuthStore } from "@/stores/auth";
import _castArray from "lodash/castArray"; // Make sure lodash is installed

// Define the expected return type for a navigation guard
type NavigationGuardReturn = void | Error | string | boolean | RouteLocationNormalized;

export function authCheck(route: RouteLocationNormalized): NavigationGuardReturn {
	// Destructure directly from route.meta, relying on extended RouteMeta type
	// Make properties optional as they might not exist on all meta objects
	const { checkAuth, authRedirect, auth, roles }: Partial<RouteMetaAuth> = route.meta;
	const authStore = useAuthStore();

	// Logout handling - Check if redirectedFrom exists and its name is 'Logout'
	if (route.redirectedFrom && route.redirectedFrom.name === "Logout") {
		console.log("[AuthCheck] Detected redirection from Logout. Logging out.");
		authStore.setLogout();
		// No need to return anything here, the redirect to /login will happen naturally
	}

	// --- Auth Required Check ---
	// If the route requires authentication (`auth: true`)
	if (auth) {
		// Check if user is logged in
		if (!authStore.isLogged) {
			console.warn(`[AuthCheck] Auth required for "${String(route.name)}", but user not logged in. Redirecting to login.`);
			const loginPath = `/login?redirect=${encodeURIComponent(route.fullPath)}`; // Redirect back after login
			return loginPath; // Return path to redirect
		}
		// Check if user has the required role(s)
		// Ensure isRoleGranted handles undefined roles gracefully
		if (roles && !authStore.isRoleGranted(roles)) {
			console.warn(`[AuthCheck] Role check failed for "${String(route.name)}". User role: ${authStore.userRole}, Required: ${JSON.stringify(roles)}. Redirecting to home (or an unauthorized page).`);
			// Redirect to home or a dedicated 'Unauthorized' page
			return "/"; // Or return { name: 'Unauthorized' } if you have such a route
		}
	}

	// --- Logged-in User Accessing Public Auth Pages Check ---
	// If the route is marked with `checkAuth: true` (e.g., /login, /register)
	// and the user *is* already logged in
	if (checkAuth && authStore.isLogged) {
		console.log(`[AuthCheck] User is logged in, attempting to access auth page "${String(route.name)}". Redirecting away.`);
		// Redirect to the intended page after login, or home as a fallback
		// Simplified: Just redirect home. Checking target route roles here adds complexity.
		return authRedirect || "/";
	}

	// If none of the above conditions caused a redirect, allow navigation
	return true; // Explicitly allow navigation
}

=== src/views/Auth/main.scss
.page-auth {
	min-height: 100vh;

	.wrapper {
		min-height: 100vh;

		.image-box {
			position: relative;
            background-color: var(--primary-color); // Use CSS var

			&::after {
				content: "";
				width: 100%;
				height: 100%;
				position: absolute;
				top: 0;
				left: 0;
				background-image: url(@/assets/images/pattern-onboard.png); // Ensure this path is correct relative to your project structure
				background-size: 500px;
				background-position: center center;
                opacity: 0.1; // Make pattern subtle
			}
		}

		.form-box {
			padding: 50px;

			&.centered {
				flex-basis: 100%;
				.form-wrap { // This class might be inside AuthForm.vue in Pinx
					padding: 60px;
					width: 100%;
					max-width: 500px;
					background-color: var(--bg-default-color);
					border-radius: 20px;
					@apply shadow-xl;
				}

				@media (max-width: 600px) {
					padding: 4%;
					.form-wrap {
						padding: 8%;
					}
				}
			}
		}
	}
	@media (max-width: 800px) {
		.wrapper {
			.image-box {
				display: none;
			}

			.form-box {
				flex-basis: 100%;
                padding: 20px; // Adjust padding for smaller screens
			}
		}
	}
}

=== src/types/auth.d.ts
// Use string literals for roles based on backend AuUserRole enum
export type Role = "AUCTIONEER" | "TRADER";
// Allow 'all' for meta checks, and handle single or multiple roles
export type Roles = Role | Role[] | "all";

// Extend RouteMeta from vue-router
export interface RouteMetaAuth {
	checkAuth?: boolean; // Should redirect away if logged in (e.g., login page)
	authRedirect?: string; // Where to redirect if checkAuth fails (user is logged in)
	auth?: boolean; // Does this route require authentication?
	roles?: Roles; // Which roles can access this route?
}

// Ensure vue-router augmentation includes these fields
// This might be better placed in src/router-env.d.ts

=== src/router-env.d.ts
import type { RouteMetaAuth } from "@/types/auth.d";
import type { Layout } from "@/types/theme.d";
import "vue-router";

// To ensure it is treated as a module, add at least one `export` statement
export {};

declare module "vue-router" {
	interface RouteMeta extends RouteMetaAuth { // Include auth properties
		title?: string;
		theme?: {
			layout?: Layout;
			boxed?: { enabled?: boolean };
			padded?: { enabled?: boolean };
		};
		skipPin?: boolean;
	}
}

// === Add the following files ===

=== src/components/auth/AuthForm.vue
<template>
	<div class="w-full max-w-96 min-w-64">
		<n-collapse-transition :show="type === 'forgotpassword'">
			<n-button text @click="gotoSignIn">
				<Icon name="carbon:arrow-left" :size="30" />
			</n-button>
		</n-collapse-transition>

		<div class="my-10">
			<Logo mini :dark="isDark" class="mb-4" max-height="40px" />
			<div class="font-display mb-4 text-4xl font-bold">{{ title }}</div>
			<div class="text-secondary mb-12 text-lg">
				Today is a new day. It's your day. You shape it. Sign in to start managing your projects.
			</div>
		</div>

		<transition name="form-fade" mode="out-in" appear class="min-h-137">
			<SignIn v-if="type === 'signin'" key="signin" @change-view="emit('change-view', $event)">
				<template #extra-actions>
					<n-button text type="primary" @click="gotoForgotPassword()">Forgot Password?</n-button>
				</template>
				<template #bottom-area>
					<div class="text-center">
						Don't you have an account?
						<n-button text type="primary" size="large" @click="gotoSignUp()">Sign up</n-button>
					</div>
				</template>
			</SignIn>
			<SignUp v-else-if="type === 'signup'" key="signup" @change-view="emit('change-view', $event)">
				<template #bottom-area>
					<div class="text-center">
						Do you have an account?
						<n-button text type="primary" size="large" @click="gotoSignIn()">Sign in</n-button>
					</div>
				</template>
			</SignUp>
			<ForgotPassword v-else-if="type === 'forgotpassword'" key="forgotpassword" @change-view="emit('change-view', $event)" />
		</transition>
	</div>
</template>

<script lang="ts" setup>
import type { FormType } from "./types.d";
import Logo from "@/app-layouts/common/Logo.vue";
import Icon from "@/components/common/Icon.vue";
import { useThemeStore } from "@/stores/theme";
import { NButton, NCollapseTransition } from "naive-ui";
import { computed, onBeforeMount, ref } from "vue";
import { useRouter } from "vue-router";
import ForgotPassword from "./ForgotPassword.vue";
import SignIn from "./SignIn.vue";
import SignUp from "./SignUp.vue";

const props = defineProps<{
	type?: FormType;
	useOnlyRouter?: boolean; // If true, navigation uses router.replace, otherwise emits change-view
}>();

const emit = defineEmits<{
	(e: "change-view", value: FormType): void;
}>();

const type = ref<FormType>("signin");
const router = useRouter();
const themeStore = useThemeStore();
const isDark = computed<boolean>(() => themeStore.isThemeDark);
const title = computed<string>(() =>
	type.value === "signin" ? "Welcome Back" : type.value === "signup" ? "Hello" : "Forgot Password"
);

function gotoSignIn() {
	if (!props.useOnlyRouter) {
		type.value = "signin";
		emit('change-view', 'signin');
	} else {
		router.replace({ name: "Login" }); // Assuming 'Login' is the route name for signin
	}
}

function gotoSignUp() {
	if (!props.useOnlyRouter) {
		type.value = "signup";
		emit('change-view', 'signup');
	} else {
		router.replace({ name: "Register" }); // Assuming 'Register' is the route name for signup
	}
}

function gotoForgotPassword() {
	if (!props.useOnlyRouter) {
		type.value = "forgotpassword";
		emit('change-view', 'forgotpassword');
	} else {
		router.replace({ name: "ForgotPassword" }); // Assuming 'ForgotPassword' is the route name
	}
}

onBeforeMount(() => {
	if (props.type) {
		type.value = props.type;
	}
});
</script>

<style lang="scss" scoped>
.form-fade-enter-active,
.form-fade-leave-active {
	transition:
		opacity 0.2s ease-in-out,
		transform 0.3s ease-in-out;
}
.form-fade-enter-from {
	opacity: 0;
	transform: translateX(10px);
}
.form-fade-leave-to {
	opacity: 0;
	transform: translateX(-10px);
}
</style>

=== src/components/auth/SignIn.vue
<template>
	<div>
		<n-form ref="formRef" :model="model" :rules="rules">
			<n-form-item path="email" label="Email" first>
				<n-input
					v-model:value="model.email"
					placeholder="Insert your email"
					size="large"
					autocomplete="on"
					@keydown.enter="signIn"
				/>
			</n-form-item>
			<n-form-item path="password" label="Password">
				<n-input
					v-model:value="model.password"
					type="password"
					show-password-on="click"
					placeholder="Insert your password"
					autocomplete="on"
					size="large"
					@keydown.enter="signIn"
				/>
			</n-form-item>
			<div class="flex flex-col items-end gap-6">
				<div class="flex w-full justify-between">
					<n-checkbox size="large">Remember me</n-checkbox>
					<slot name="extra-actions" />
				</div>
				<div class="w-full">
					<n-button type="primary" class="w-full!" size="large" :disabled="!isValid" @click="signIn">
						Sign in
					</n-button>
				</div>
			</div>
		</n-form>

		<n-divider title-placement="center">Or</n-divider>

		<div class="social-btns mb-12 flex flex-col gap-4">
			<n-button strong secondary size="large">
				<template #icon>
					<img alt="google-icon" class="block h-5" src="@/assets/images/google-icon.svg?url" />
				</template>
				<span class="px-2">Sign in with Google</span>
			</n-button>
			<n-button strong secondary size="large">
				<template #icon>
					<img alt="facebook-icon" class="block h-5" src="@/assets/images/facebook-icon.svg?url" />
				</template>
				<span class="px-2">Sign in with Facebook</span>
			</n-button>
		</div>

		<slot name="bottom-area" />
	</div>
</template>

<script lang="ts" setup>
import type { FormInst, FormItemRule, FormRules, FormValidationError } from "naive-ui";
import { useAuthStore } from "@/stores/auth";
import { NButton, NCheckbox, NDivider, NForm, NFormItem, NInput, useMessage } from "naive-ui";
import isEmail from "validator/es/lib/isEmail";
import { computed, ref, watch } from "vue";
import { useRouter } from "vue-router";
import { useWebSocket } from '@/services/websocket'; // Import WebSocket service

interface ModelType {
	email: string | null;
	password: string | null;
}

const router = useRouter();
const formRef = ref<FormInst | null>(null);
const message = useMessage();
const ws = useWebSocket(); // Get WebSocket instance
const model = ref<ModelType>({
	email: "a1", // Default to auctioneer for testing
	password: "1"
});

const rules: FormRules = {
	email: [
		{
			required: true,
			trigger: ["blur", "input"], // Trigger on input as well
			message: "Username/Email is required"
		},
		// Optional: Add email validation back if needed, but allow username too
		// {
		// 	validator: (rule: FormItemRule, value: string): boolean => {
		// 		return isEmail(value || "");
		// 	},
		// 	message: "The email is not formatted correctly",
		// 	trigger: ["blur"]
		// }
	],
	password: [
		{
			required: true,
			trigger: ["blur", "input"],
			message: "Password is required"
		}
	]
};

const isValid = computed(() => {
	// Check if both fields have values
	return model.value.email && model.value.password;
});

const authStore = useAuthStore();

function signIn(e?: Event) { // Make event optional
	e?.preventDefault();
	formRef.value?.validate((errors: Array<FormValidationError> | undefined) => {
		if (!errors) {
			// Send LoginCommand via WebSocket
			ws.sendCommand({
				'@type': 'au21.engine.domain.common.commands.LoginCommand', // Ensure this matches backend
				username: model.value.email!, // Use the email field as username
				password: model.value.password!
			});
			// Backend will handle success/failure and push state updates
			// Optionally show a loading message here
			// message.loading("Attempting login...");
		} else {
			message.error("Please fill in both username/email and password.");
		}
	});
}

// Optional: Trigger validation on changes if needed
// watch(model, () => {
// 	formRef.value?.validate().catch(() => {}); // Validate silently on change
// }, { deep: true });

</script>

=== src/components/auth/SignUp.vue
<template>
	<div>
		<n-form ref="formRef" :model="model" :rules="rules">
			<n-form-item path="email" label="Email" first>
				<n-input v-model:value="model.email" size="large" placeholder="Insert the email" />
			</n-form-item>
			<n-form-item path="password" label="Password">
				<div class="flex w-full flex-col gap-3">
					<n-input
						v-model:value="model.password"
						type="password"
						size="large"
						show-password-on="click"
						placeholder="Insert the password"
					/>
					<PasswordStrengthMeter
						v-if="model.password"
						:password="model.password"
						@strength="passwordStrength = $event"
					/>
				</div>
			</n-form-item>
			<n-form-item path="confirmPassword" label="Confirm Password" first>
				<n-input
					v-model:value="model.confirmPassword"
					type="password"
					:disabled="!model.password"
					size="large"
					show-password-on="click"
					placeholder="Confirm the password"
				/>
			</n-form-item>
			<div class="flex flex-col items-end">
				<div class="w-full">
					<n-button type="primary" class="w-full!" size="large" :disabled="!isValid" @click="signUp">
						Create an account
					</n-button>
				</div>
			</div>
		</n-form>

		<n-divider title-placement="center">Or</n-divider>

		<div class="social-btns mb-12 flex flex-col gap-4">
			<n-button strong secondary size="large">
				<template #icon>
					<img alt="google-icon" class="block h-5" src="@/assets/images/google-icon.svg?url" />
				</template>
				<span class="px-2">Sign in with Google</span>
			</n-button>
			<n-button strong secondary size="large">
				<template #icon>
					<img alt="facebook-icon" class="block h-5" src="@/assets/images/facebook-icon.svg?url" />
				</template>
				<span class="px-2">Sign in with Facebook</span>
			</n-button>
		</div>

		<slot name="bottom-area" />
	</div>
</template>

<script lang="ts" setup>
import type { FormInst, FormItemRule, FormRules, FormValidationError } from "naive-ui";
import PasswordStrengthMeter from "@/components/common/PasswordStrengthMeter.vue";
import { NButton, NDivider, NForm, NFormItem, NInput, useMessage } from "naive-ui";
import isEmail from "validator/es/lib/isEmail";
import { computed, ref, watch } from "vue";

interface ModelType {
	email: string | null;
	password: string | null;
	confirmPassword: string | null;
}

const formRef = ref<FormInst | null>(null);
const message = useMessage();
const passwordStrength = ref<number>(0);
const model = ref<ModelType>({
	email: null,
	password: null,
	confirmPassword: null
});

const rules: FormRules = {
	email: [
		{
			required: true,
			trigger: ["blur", "input"],
			message: "Email is required"
		},
		{
			validator: (rule: FormItemRule, value: string): boolean => {
				return isEmail(value || "");
			},
			message: "The email is not formatted correctly",
			trigger: ["blur", "input"]
		}
	],
	password: [
		{
			required: true,
			trigger: ["blur", "input"],
			message: "Password is required"
		}
	],
	confirmPassword: [
		{
			required: true,
			trigger: ["blur", "input"],
			message: "Confirm Password is required"
		},
		{
			validator: (rule: FormItemRule, value: string): boolean => {
				return value === model.value.password;
			},
			message: "Password is not same as re-entered password!",
			trigger: ["blur", "password-input"] // Trigger validation when password changes too
		}
	]
};

const isValid = computed(() => {
	return (
		isEmail(model.value.email || "") &&
		model.value.password &&
		model.value.confirmPassword &&
		model.value.password === model.value.confirmPassword &&
		passwordStrength.value === 100 // Ensure strong password if meter is used
	);
});

function signUp(e: Event) {
	e.preventDefault();
	formRef.value?.validate((errors: Array<FormValidationError> | undefined) => {
		if (!errors) {
			// TODO: Implement actual sign-up logic (e.g., API call)
			message.success("Sign up successful (placeholder)");
		} else {
			message.error("Please correct the errors in the form.");
		}
	});
}

// Optional: Trigger validation on changes if needed
// watch(model, () => {
// 	formRef.value?.validate().catch(() => {}); // Validate silently on change
// }, { deep: true });
</script>

=== src/components/auth/ForgotPassword.vue
<template>
	<div>
		<n-form ref="formRef" :model="model" :rules="rules">
			<n-form-item path="email" label="Email" first>
				<n-input
					v-model:value="model.email"
					placeholder="Input your email"
					size="large"
					@keydown.enter="forgotPassword"
				/>
			</n-form-item>
			<div class="flex flex-col items-end gap-6">
				<div class="w-full">
					<n-button type="primary" class="w-full!" size="large" :disabled="!isValid" @click="forgotPassword">
						Send Reset Link
					</n-button>
				</div>
			</div>
		</n-form>
	</div>
</template>

<script lang="ts" setup>
import type { FormInst, FormItemRule, FormRules, FormValidationError } from "naive-ui";
import { NButton, NForm, NFormItem, NInput, useMessage } from "naive-ui";
import isEmail from "validator/es/lib/isEmail";
import { computed, ref, watch } from "vue";

interface ModelType {
	email: string | null;
}

const formRef = ref<FormInst | null>(null);
const message = useMessage();
const model = ref<ModelType>({
	email: null
});

const rules: FormRules = {
	email: [
		{
			required: true,
			trigger: ["blur", "input"],
			message: "The email is mandatory"
		},
		{
			validator: (rule: FormItemRule, value: string): boolean => {
				return isEmail(value || "");
			},
			message: "The email is not formatted correctly",
			trigger: ["blur", "input"]
		}
	]
};

const isValid = computed(() => {
	return isEmail(model.value.email || "");
});

function forgotPassword(e: Event) {
	e.preventDefault();
	formRef.value?.validate((errors: Array<FormValidationError> | undefined) => {
		if (!errors) {
			// TODO: Implement actual forgot password logic (e.g., API call)
			message.success("Reset Link sent (placeholder)");
		} else {
			message.error("Please enter a valid email address.");
		}
	});
}

// Optional: Trigger validation on changes if needed
// watch(model, () => {
// 	formRef.value?.validate().catch(() => {}); // Validate silently on change
// }, { deep: true });
</script>

=== src/app-layouts/common/Logo.vue
<template>
	<div v-if="isDark && !mini" class="logo">
		<img alt="logo" src="@/assets/images/brand-logo_dark.svg?url" />
	</div>
	<div v-else-if="isLight && !mini" class="logo">
		<img alt="logo" src="@/assets/images/brand-logo_light.svg?url" />
	</div>
	<div v-else-if="isDark && mini" class="logo">
		<img alt="logo" src="@/assets/images/brand-icon_dark.svg?url" />
	</div>
	<div v-else-if="isLight && mini" class="logo">
		<img alt="logo" src="@/assets/images/brand-icon_light.svg?url" />
	</div>
</template>

<script lang="ts" setup>
import { useThemeStore } from "@/stores/theme";
import { computed } from "vue";

const {
	mini,
	dark,
	maxHeight = "32px"
} = defineProps<{
	mini?: boolean;
	dark?: boolean;
	maxHeight?: string;
}>();
const themeStore = useThemeStore();
const isDark = computed<boolean>(() => dark ?? themeStore.isThemeDark);
const isLight = computed<boolean>(() => !dark || themeStore.isThemeLight);
</script>

<style lang="scss" scoped>
.logo {
	height: 100%;
	display: flex;
	align-items: center;

	img {
		max-height: v-bind(maxHeight);
		display: block;
		height: 100%;
	}
}
</style>

// --- Include all other previously generated files here ---
// (appStore.ts, websocket.ts, router/index.ts, auth.ts, Login.vue, AuthForm.vue, SignIn.vue, etc...)
// ... (rest of the codebase.naiveui.txt content from the *previous* response)

=== src/stores/appStore.ts
import type {
	AuctionRowElement,
	CompanyElement,
	CounterpartyCreditElement,
	MessageElement,
	SessionUserValue,
	TimeValue,
	UserElement
} from "@/types/generated"; // Assuming generated types exist
import type { DeAuctionValue, DeMatrixRoundElement } from "@/types/generated"; // Assuming generated types exist
import { defineStore } from "pinia";

// Mirroring the backend's LiveClientStore structure
interface AppState {
	auction_rows: AuctionRowElement[];
	companies: CompanyElement[];
	counterparty_credits: CounterpartyCreditElement[];
	de_auction: DeAuctionValue | null;
	session_user: SessionUserValue | null;
	time: TimeValue | null;
	users: UserElement[];
	// Add a specific place for matrix rounds if needed frequently outside de_auction
	de_matrix_rounds: DeMatrixRoundElement[];
	messages: MessageElement[]; // Assuming messages might be added incrementally
	websocketConnected: boolean;
	lastMessageTimestamp: number | null;
}

export const useAppStore = defineStore("app", {
	state: (): AppState => ({
		auction_rows: [],
		companies: [],
		counterparty_credits: [],
		de_auction: null,
		session_user: null,
		time: null,
		users: [],
		de_matrix_rounds: [],
		messages: [],
		websocketConnected: false,
		lastMessageTimestamp: null,
	}),

	actions: {
		setLiveStore(storeData: Partial<AppState>) {
			// Deep merge might be better, but direct assignment matches backend push
			// Consider using lodash merge or similar if partial updates are expected
			// within SetLiveStore, otherwise, this assumes a full state push.
			if (storeData.auction_rows !== undefined) this.auction_rows = storeData.auction_rows;
			if (storeData.companies !== undefined) this.companies = storeData.companies;
			if (storeData.counterparty_credits !== undefined) this.counterparty_credits = storeData.counterparty_credits;
			if (storeData.de_auction !== undefined) this.de_auction = storeData.de_auction;
			if (storeData.session_user !== undefined) this.session_user = storeData.session_user;
			if (storeData.time !== undefined) this.time = storeData.time;
			if (storeData.users !== undefined) this.users = storeData.users;

			// If messages/matrix rounds are part of LiveClientStore, update them here too.
			// If they are only updated via AddElements, handle them there.
			if (storeData.de_auction?.messages) {
				this.messages = storeData.de_auction.messages;
			}
			if (storeData.de_auction?.matrix_last_round) {
				// Assuming only the last round is sent in the main store push
				this.de_matrix_rounds = [storeData.de_auction.matrix_last_round];
			}

			console.log("LiveStore updated"); // Reduced verbosity
		},

		addElements(path: string, elements: any[] | null) {
			console.log(`AddElements received for path: ${path}`, elements ? `${elements.length} elements` : 'null (clear)');
			if (elements === null) {
				// Handle clearing the array
				if (path === "AuctionRowElement") this.auction_rows = [];
				else if (path === "CompanyElement") this.companies = [];
				else if (path === "CounterpartyCreditElement") this.counterparty_credits = [];
				else if (path === "MessageElement") this.messages = [];
				else if (path === "UserElement") this.users = [];
				else if (path === "DeMatrixRoundElement") this.de_matrix_rounds = [];
				// Add other paths as needed
				else console.warn(`AddElements: Unknown path to clear: ${path}`);
				return;
			}

			if (!Array.isArray(elements)) {
				console.error("AddElements: received non-array elements", elements);
				return;
			}

			// Find the target array and update/add elements
			let targetArray: any[] | undefined;
			if (path === "AuctionRowElement") targetArray = this.auction_rows;
			else if (path === "CompanyElement") targetArray = this.companies;
			else if (path === "CounterpartyCreditElement") targetArray = this.counterparty_credits;
			else if (path === "MessageElement") targetArray = this.messages;
			else if (path === "UserElement") targetArray = this.users;
			else if (path === "DeMatrixRoundElement") targetArray = this.de_matrix_rounds;
			// Add other paths as needed

			if (targetArray) {
				elements.forEach(newElement => {
					if (!newElement || typeof newElement.id === 'undefined') {
						console.warn("AddElements: Skipping element without id", newElement);
						return;
					}
					const index = targetArray.findIndex(item => item.id === newElement.id);
					if (index !== -1) {
						// Update existing element (important for reactivity)
						// Use spread or Object.assign for better reactivity handling if needed
						targetArray[index] = { ...targetArray[index], ...newElement };
					} else {
						// Add new element
						targetArray.push(newElement);
					}
				});
				// Optional: Sort arrays if needed after updates
				if (path === 'MessageElement') this.messages.sort((a, b) => a.timestamp - b.timestamp);
				if (path === 'DeMatrixRoundElement') this.de_matrix_rounds.sort((a, b) => a.round_number - b.round_number);

			} else {
				console.warn(`AddElements: Unknown path: ${path}`);
			}
		},

		setWebsocketConnected(status: boolean) {
			this.websocketConnected = status;
		},

		setLastMessageTimestamp(ts: number) {
			this.lastMessageTimestamp = ts;
		},

		resetState() {
			this.$reset();
			console.log("AppStore state reset");
		}
	},
	getters: {
		isAuctioneer: (state) => state.session_user?.isAuctioneer ?? false,
		isTrader: (state) => !state.session_user?.isAuctioneer && !!state.session_user?.role, // Assuming non-auctioneer logged in is trader
		currentSessionId: (state) => state.session_user?.session_id,
		currentPage: (state) => state.session_user?.current_page,
		currentAuctionId: (state) => state.session_user?.current_auction_id,
		// Add more specific getters as needed e.g., getTraderInfo, getAuctioneerStatus
		getDeAuctioneerStatus: (state) => state.de_auction?.auctioneer_status,
		getDeCommonStatus: (state) => state.de_auction?.common_status,
		getDeTraderInfo: (state) => state.de_auction?.trader_info,
		getDeTraderHistory: (state) => state.de_auction?.trader_history_rows ?? [],
		getDeBlotter: (state) => state.de_auction?.blotter,
		getDeMatrixLastRound: (state) => state.de_auction?.matrix_last_round,
		getMessagesForCurrentAuction: (state) => {
			// Filter messages based on current auction context if needed,
			// or assume messages in the store are already filtered by backend push
			// Ensure sorting happens here if AddElements doesn't guarantee order
			return [...state.messages].sort((a, b) => a.timestamp - b.timestamp);
		}
	}
});

=== src/services/websocket.ts
import { useAppStore } from "@/stores/appStore";
import { useAuthStore } from "@/stores/auth";
import { useMainStore } from "@/stores/main";
import { useGlobalActions } from "@/composables/useGlobalActions";
import { ref, watch } from "vue";
import { i18nGlobal } from "@/lang"; // Assuming i18n setup for messages

// Assuming generated types exist
import type { ClientCommand, EngineCommand, EngineCommandEnvelope } from "@/types/generated";
import { CommandType, BrowserMessageKind } from "@/types/generated";

// Make WS URL configurable via environment variables
const WS_BASE_URL = import.meta.env.VITE_WS_URL || 'ws://localhost:8080/socket'; // Default fallback

let websocket: WebSocket | null = null;
let reconnectAttempts = 0;
const maxReconnectAttempts = 5;
const reconnectInterval = 5000; // 5 seconds
let reconnectTimeout: NodeJS.Timeout | null = null;
let currentSessionId = ref<string | null>(null); // Track the session ID we *should* be connected with

export function useWebSocket() {
	const appStore = useAppStore();
	const authStore = useAuthStore();
	const mainStore = useMainStore();
	const { message: showMessage, notification: showNotification } = useGlobalActions(); // Assuming global setup

	// Watch for session ID changes to connect/disconnect
	// This is the primary trigger for connection management
	watch(() => appStore.currentSessionId, (newSid, oldSid) => {
		console.log(`[WS] Session ID watcher: changed from ${oldSid} to ${newSid}`);
		if (newSid && newSid !== currentSessionId.value) {
			console.log(`[WS] New session ID detected (${newSid}), initiating connection.`);
			currentSessionId.value = newSid; // Update the target session ID
			disconnectWebSocket(false); // Disconnect old if exists, don't trigger reconnect logic
			connectWebSocket(newSid);
		} else if (!newSid && currentSessionId.value) {
			console.log("[WS] Session ID removed, disconnecting.");
			currentSessionId.value = null; // Clear target session ID
			disconnectWebSocket(false); // Disconnect cleanly
		}
	}, { immediate: true });

	// Watch for login status changes (secondary check, mainly for logout)
	watch(() => authStore.isLogged, (loggedIn) => {
		console.log(`[WS] Auth state watcher: loggedIn=${loggedIn}`);
		if (!loggedIn && currentSessionId.value) {
			console.log("[WS] User logged out, disconnecting WebSocket.");
			currentSessionId.value = null; // Clear target session ID
			disconnectWebSocket(false); // Clean disconnect on logout
			appStore.resetState(); // Clear app state on logout
		}
		// Connection on login is handled by the session_id watch
	});

	function getWebSocketURL(sessionId: string): string {
		// Add browser info as query params, matching backend expectations
		const browserInfo = {
			browser_name: navigator.userAgentData?.brands?.[0]?.brand || navigator.vendor || "Unknown",
			browser_version: navigator.userAgentData?.brands?.[0]?.version || navigator.appVersion || "Unknown",
			browser_os: navigator.platform || "Unknown",
		};
		const queryParams = new URLSearchParams(browserInfo).toString();
		const url = `${WS_BASE_URL}/${sessionId}`; // Use template literal
		return `${url}?${queryParams}`;
	}

	function connectWebSocket(sessionId: string) {
		if (!sessionId) {
			console.error("[WS] Cannot connect: No Session ID provided.");
			return;
		}
		// Prevent multiple connections for the same session ID
		if (websocket && websocket.readyState !== WebSocket.CLOSED && websocket.url.includes(sessionId)) {
			console.log(`[WS] Already connected or connecting to session ${sessionId}.`);
			return;
		}
		// If a WS exists for a *different* session, disconnect it first
		if (websocket && websocket.readyState !== WebSocket.CLOSED && !websocket.url.includes(sessionId)) {
			console.warn(`[WS] Disconnecting obsolete WebSocket for session ${websocket.url.split('/').pop()?.split('?')[0]}`);
			disconnectWebSocket(false);
		}


		const url = getWebSocketURL(sessionId);
		console.log(`[WS] Connecting to: ${url}`);
		appStore.setWebsocketConnected(false);
		mainStore.loadingBar?.start();

		try {
			websocket = new WebSocket(url);
		} catch (error) {
			console.error("[WS] Failed to create WebSocket:", error);
			mainStore.loadingBar?.error();
			scheduleReconnect(sessionId); // Attempt reconnect even if creation fails
			return;
		}


		websocket.onopen = () => {
			// Double check if this is still the desired session before proceeding
			if (currentSessionId.value !== sessionId) {
				console.warn(`[WS] Connection opened for ${sessionId}, but desired session is now ${currentSessionId.value}. Closing.`);
				disconnectWebSocket(false);
				return;
			}
			console.log(`[WS] Connection established for session ${sessionId}.`);
			appStore.setWebsocketConnected(true);
			reconnectAttempts = 0; // Reset attempts on successful connection
			clearTimeout(reconnectTimeout!);
			mainStore.loadingBar?.finish();
			// Backend should send ClientSocketCommand(OPENED) and then push initial state
		};

		websocket.onmessage = (event) => {
			// Ensure this message is for the *current* session
			if (currentSessionId.value !== sessionId) {
				console.warn(`[WS] Received message for obsolete session ${sessionId}. Ignoring.`);
				return;
			}
			appStore.setLastMessageTimestamp(Date.now());
			// Assume backend sends gzipped binary data - Placeholder for ungzip
			if (event.data instanceof Blob) {
				const reader = new FileReader();
				reader.onload = function () {
					try {
						const arrayBuffer = reader.result as ArrayBuffer;
						// *** Placeholder for ungzip ***
						// Replace this with actual ungzip logic using pako or similar
						console.warn("[WS] Binary message received, ungzip not implemented. Decoding as UTF-8 text.");
						const textDecoder = new TextDecoder("utf-8");
						handleMessage(textDecoder.decode(arrayBuffer));
						// *** End Placeholder ***

					} catch (error) {
						console.error("[WS] Error processing binary message:", error);
						mainStore.loadingBar?.error();
					}
				};
				reader.onerror = (error) => {
					console.error("[WS] Error reading Blob:", error);
					mainStore.loadingBar?.error();
				};
				reader.readAsArrayBuffer(event.data);
			} else if (typeof event.data === 'string') {
				// Handle plain text messages
				// console.log("[WS] Received Text Message (raw):", event.data.substring(0, 200) + "...");
				handleMessage(event.data);
			} else {
				console.warn("[WS] Received unexpected message type:", typeof event.data);
			}
		};

		websocket.onerror = (error) => {
			console.error(`[WS] Error for session ${sessionId}:`, error);
			// Only attempt reconnect if this is the currently active session
			if (currentSessionId.value === sessionId) {
				appStore.setWebsocketConnected(false);
				mainStore.loadingBar?.error();
				showMessage(i18nGlobal.t('errors.websocketConnection'), { type: 'error', duration: 0, closable: true });
				scheduleReconnect(sessionId);
			} else {
				console.log(`[WS] Ignoring error for obsolete session ${sessionId}.`);
			}
		};

		websocket.onclose = (event) => {
			console.log(`[WS] Connection closed for session ${sessionId}: Code=${event.code}, Reason=${event.reason}, Clean=${event.wasClean}`);
			// Only manage state and reconnect if this was the *intended* active connection
			if (currentSessionId.value === sessionId) {
				appStore.setWebsocketConnected(false);
				websocket = null; // Clear the reference only if it's the current one closing

				// Reconnect only if:
				// 1. It wasn't a clean close (code 1000 is normal) OR it was closed unexpectedly (e.g., server restart 1006)
				// 2. The user is still supposed to be logged in (authStore.isLogged)
				// 3. This session ID is still the one we want (currentSessionId.value === sessionId)
				if ((!event.wasClean || event.code === 1006) && authStore.isLogged) {
					console.log("[WS] Unclean close or server issue, attempting reconnect...");
					showMessage(i18nGlobal.t('errors.websocketDisconnected'), { type: 'warning' });
					mainStore.loadingBar?.error();
					scheduleReconnect(sessionId);
				} else {
					console.log("[WS] Clean close or user logged out/session changed, not reconnecting.");
					mainStore.loadingBar?.finish(); // Finish loading bar if closed cleanly
				}
			} else {
				console.log(`[WS] Ignoring close event for obsolete session ${sessionId}.`);
				// If the closed websocket is the one stored in the global `websocket` variable, clear it
				if (websocket === event.target) {
					websocket = null;
				}
			}
		};
	}

	function scheduleReconnect(sessionId: string) {
		// Only schedule if this is still the desired session
		if (currentSessionId.value !== sessionId) {
			console.log(`[WS] Reconnect cancelled for ${sessionId}, desired session is ${currentSessionId.value}.`);
			return;
		}

		if (reconnectAttempts >= maxReconnectAttempts) {
			console.error("[WS] Max reconnect attempts reached. Giving up.");
			showMessage(i18nGlobal.t('errors.websocketReconnectFailed'), { type: 'error', duration: 0, closable: true });
			// Optional: Force logout or redirect to login
			// authStore.setLogout(); // This might trigger disconnectWebSocket via watcher
			// router.push('/login');
			return;
		}

		clearTimeout(reconnectTimeout!); // Clear any existing timeout
		reconnectAttempts++;
		const delay = reconnectInterval * Math.pow(2, reconnectAttempts - 1); // Exponential backoff
		console.log(`[WS] Scheduling reconnect attempt ${reconnectAttempts}/${maxReconnectAttempts} for session ${sessionId} in ${delay / 1000}s`);

		reconnectTimeout = setTimeout(() => {
			// Double-check the session ID *again* before actually connecting
			if (currentSessionId.value === sessionId) {
				console.log(`[WS] Attempting reconnect ${reconnectAttempts} for session ${sessionId}...`);
				connectWebSocket(sessionId);
			} else {
				console.log(`[WS] Reconnect attempt ${reconnectAttempts} cancelled for ${sessionId}, desired session changed to ${currentSessionId.value}.`);
				reconnectAttempts = 0; // Reset attempts as we are no longer trying for this session
			}
		}, delay);
	}

	function disconnectWebSocket(clean = true) {
		clearTimeout(reconnectTimeout!); // Always clear reconnect timeout on disconnect
		reconnectAttempts = 0; // Reset attempts
		if (websocket) {
			const reason = clean ? "Client requested disconnect" : "Switching sessions";
			console.log(`[WS] Disconnecting WebSocket (${reason}). State: ${websocket.readyState}`);
			if (websocket.readyState === WebSocket.OPEN || websocket.readyState === WebSocket.CONNECTING) {
				websocket.close(1000, reason); // 1000 is normal closure
			}
			websocket = null; // Clear the reference
		}
		// Only update store if it reflects the current state intention
		if (appStore.websocketConnected) {
			appStore.setWebsocketConnected(false);
		}
	}


	function handleMessage(messageJson: string) {
		try {
			// console.log("[WS] Handling Message:", messageJson.substring(0, 200) + "...");
			const clientCommand = JSON.parse(messageJson) as ClientCommand; // Assuming ClientCommand is the base type

			switch (clientCommand.command) {
				case CommandType.SetLiveStore:
					const storeCommand = clientCommand as ClientCommand.StoreCommand.SetLiveStore;
					appStore.setLiveStore(storeCommand.store);
					break;
				case CommandType.AddElements:
					const addElementsCommand = clientCommand as ClientCommand.StoreCommand.AddElements;
					appStore.addElements(addElementsCommand.path, addElementsCommand.elements);
					break;
				case CommandType.ShowMessage:
					const showMessageCommand = clientCommand as ClientCommand.ShowMessage;
					const messageType = showMessageCommand.browser_message_kind === BrowserMessageKind.ALERT ? 'error' : 'info'; // Or map more granularly
					const messageText = showMessageCommand.message.join('\n');
					if (messageType === 'error') {
						showMessage(messageText, { type: 'error', duration: 5000, closable: true });
					} else {
						showNotification({
							content: messageText,
							type: messageType, // info, success, warning
							duration: 4000,
							keepAliveOnHover: true
						});
					}
					break;
				case CommandType.TerminateSession:
					const terminateCommand = clientCommand as ClientCommand.TerminateSession;
					console.log("[WS] Session termination requested by server:", terminateCommand.message);
					showMessage(terminateCommand.message || i18nGlobal.t('errors.sessionTerminated'), { type: 'error', duration: 0, closable: true });
					authStore.setLogout(); // This will trigger watchers to disconnect WS and clear state
					// Router guard should handle redirect
					break;
				case CommandType.NetworkDown:
					console.warn("[WS] Backend indicated network issues.");
					showMessage(i18nGlobal.t('errors.networkDown'), { type: 'warning' });
					break;
				case CommandType.NetworkUp:
					console.info("[WS] Backend indicated network is back up.");
					showMessage(i18nGlobal.t('info.networkUp'), { type: 'success' });
					break;
				case CommandType.CommandSucceeded:
					console.log("[WS] Command succeeded ack received.");
					break;
				default:
					console.warn("[WS] Received unknown command type:", clientCommand.command);
			}
		} catch (error) {
			console.error("[WS] Error parsing or handling WebSocket message:", error, "\nMessage was:", messageJson);
			showMessage(i18nGlobal.t('errors.websocketMessageError'), { type: 'error' });
		}
	}

	function sendCommand(command: EngineCommand) {
		if (!currentSessionId.value) {
			console.error("[WS] No active session ID. Cannot send command.");
			showMessage(i18nGlobal.t('errors.sendCommandNoSession'), { type: 'error' });
			return;
		}
		if (!websocket || websocket.readyState !== WebSocket.OPEN) {
			console.error("[WS] WebSocket is not connected. Cannot send command.");
			showMessage(i18nGlobal.t('errors.websocketNotConnected'), { type: 'error' });
			// Optional: Attempt reconnect or queue command? Maybe trigger connect if not connecting
			if (!websocket || websocket.readyState === WebSocket.CLOSED) {
				console.log("[WS] Attempting to reconnect before sending command...");
				connectWebSocket(currentSessionId.value);
				// Maybe queue the command to send after successful connection?
			}
			return;
		}


		// Construct the envelope - Backend needs to handle missing simplename/classname
		const envelope: Partial<EngineCommandEnvelope> = {
			session_id: currentSessionId.value,
			command: command,
		};

		try {
			const messageJson = JSON.stringify(envelope);
			console.log("[WS] Sending Command:", command.constructor.name, JSON.stringify(command).substring(0,100)+"...");
			// TODO: Gzip if required by backend
			websocket.send(messageJson);
		} catch (error) {
			console.error("[WS] Error sending command:", error);
			showMessage(i18nGlobal.t('errors.sendCommandFailed'), { type: 'error' });
		}
	}

	return {
		// connectWebSocket, // Connection is now managed internally by session ID changes
		disconnectWebSocket, // Expose for explicit disconnect (e.g., logout button)
		sendCommand,
		websocketConnected: computed(() => appStore.websocketConnected),
		lastMessageTimestamp: computed(() => appStore.lastMessageTimestamp),
	};
}

=== src/router/index.ts
import type { FormType } from "@/components/auth/types.d";
import { Layout, PageName } from "@/types/generated"; // Assuming PageName is generated
import { authCheck } from "@/utils/auth"; // Corrected import path
import { createRouter, createWebHistory } from "vue-router";

// Helper to map backend PageName to frontend route names/paths
// Adjust paths/names as needed to match your preferred frontend routing structure
const pageNameToRoute = {
	[PageName.LOGIN_PAGE]: { name: "Login", path: "/login" },
	[PageName.HOME_PAGE]: { name: "Home", path: "/" }, // Map to dashboard or appropriate home
	[PageName.USER_PAGE]: { name: "UserManagement", path: "/admin/users" },
	[PageName.COMPANY_PAGE]: { name: "CompanyManagement", path: "/admin/companies" }, // Assuming a dedicated page
	[PageName.SESSION_PAGE]: { name: "SessionManagement", path: "/admin/sessions" },
	[PageName.DE_SETUP_PAGE]: { name: "DeAuctionSetup", path: "/de/setup" }, // Can be combined with edit using :id?
	[PageName.DE_AUCTIONEER_PAGE]: { name: "DeAuctioneer", path: "/de/auction/:id/auctioneer" },
	[PageName.DE_TRADER_PAGE]: { name: "DeTrader", path: "/de/auction/:id/trader" },
	// Add mappings for other PageNames (CREDITOR, BH, MR, TE, TO) if they exist and have UIs
	[PageName.CREDITOR_AUCTIONEER_PAGE]: { name: "CreditorAuctioneer", path: "/creditor/auction/:id/auctioneer" },
	[PageName.CREDITOR_TRADER_PAGE]: { name: "CreditorTrader", path: "/creditor/auction/:id/trader" },
	// ... add others like BH_AUCTIONEER_PAGE etc. if needed
};

const router = createRouter({
	history: createWebHistory(import.meta.env.BASE_URL),
	routes: [
		// Redirect root to a default dashboard or home based on role later
		{
			path: "/",
			name: pageNameToRoute[PageName.HOME_PAGE].name, // Use mapping
			component: () => import("@/views/Dashboard/Analytics.vue"), // Use created placeholder
			meta: { title: "Dashboard", auth: true, roles: "all" },
		},

		// --- Authentication ---
		{
			path: pageNameToRoute[PageName.LOGIN_PAGE].path,
			name: pageNameToRoute[PageName.LOGIN_PAGE].name,
			component: () => import("@/views/Auth/Login.vue"), // Use created placeholder
			meta: {
				title: "Login",
				theme: { layout: Layout.Blank, boxed: { enabled: false }, padded: { enabled: false } },
				checkAuth: true, // Redirect if already logged in
				skipPin: true,
			},
		},
		// Add Register and ForgotPassword if needed, mapping them appropriately
		{
			path: "/register", // No direct PageName mapping, assuming standard flow
			name: "Register",
			component: () => import("@/views/Auth/Login.vue"), // Reuse Login view
			props: { formType: "signup" as FormType },
			meta: {
				title: "Register",
				theme: { layout: Layout.Blank, boxed: { enabled: false }, padded: { enabled: false } },
				checkAuth: true,
				skipPin: true,
			},
		},
		{
			path: "/forgot-password", // No direct PageName mapping
			name: "ForgotPassword",
			component: () => import("@/views/Auth/Login.vue"), // Reuse Login view
			props: { formType: "forgotpassword" as FormType },
			meta: {
				title: "Forgot Password",
				theme: { layout: Layout.Blank, boxed: { enabled: false }, padded: { enabled: false } },
				checkAuth: true,
				skipPin: true,
			},
		},
		{
			path: "/logout",
			name: "Logout",
			redirect: pageNameToRoute[PageName.LOGIN_PAGE].path, // Redirect to login after logout logic
		},

		// --- Admin / Auctioneer Specific ---
		{
			path: "/admin", // Group admin routes
			redirect: "/admin/users",
			meta: { auth: true, roles: ["AUCTIONEER"] }, // Role guard
			children: [
				{
					path: "users",
					name: pageNameToRoute[PageName.USER_PAGE].name,
					component: () => import("@/views/Admin/UserManagement.vue"), // Use created view
					meta: { title: "User Management" },
				},
				{
					path: "companies",
					name: pageNameToRoute[PageName.COMPANY_PAGE].name,
					component: () => import("@/views/Admin/CompanyManagement.vue"), // Use created view
					meta: { title: "Company Management" },
				},
				{
					path: "sessions",
					name: pageNameToRoute[PageName.SESSION_PAGE].name,
					component: () => import("@/views/Admin/SessionManagement.vue"), // Use created view
					meta: { title: "Session Management" },
				},
			],
		},

		// --- DE Auction ---
		{
			path: "/de", // Group DE routes
			redirect: pageNameToRoute[PageName.HOME_PAGE].path, // Redirect to home/dashboard
			meta: { auth: true, roles: "all" }, // Allow both roles, specific views handle finer control
			children: [
				{
					path: "setup", // Route for creating a new auction
					name: pageNameToRoute[PageName.DE_SETUP_PAGE].name + "Create", // Differentiate create/edit
					component: () => import("@/views/Auction/DeAuctionSetup.vue"), // Use created view
					meta: { title: "Create DE Auction", roles: ["AUCTIONEER"] },
				},
				{
					path: "setup/:id", // Route for editing an existing auction
					name: pageNameToRoute[PageName.DE_SETUP_PAGE].name + "Edit",
					component: () => import("@/views/Auction/DeAuctionSetup.vue"), // Reuse the setup view
					props: true, // Pass route params as props
					meta: { title: "Edit DE Auction", roles: ["AUCTIONEER"] },
				},
				{
					path: "auction/:id/auctioneer",
					name: pageNameToRoute[PageName.DE_AUCTIONEER_PAGE].name,
					component: () => import("@/views/Auction/DeAuctioneer.vue"), // Use created view
					props: true,
					meta: { title: "DE Auctioneer View", roles: ["AUCTIONEER"] },
				},
				{
					path: "auction/:id/trader",
					name: pageNameToRoute[PageName.DE_TRADER_PAGE].name,
					component: () => import("@/views/Auction/DeTrader.vue"), // Use created view
					props: true,
					meta: { title: "DE Trader View", roles: ["TRADER"] },
				},
			],
		},

		// --- Add routes for other auction types (Creditor, BH, MR, TE, TO) similarly ---
		// Example for Creditor
		{
			path: "/creditor",
			redirect: pageNameToRoute[PageName.HOME_PAGE].path, // Or a specific creditor dashboard/list
			meta: { auth: true, roles: "all" },
			children: [
				// Add setup/edit routes if applicable
				{
					path: "auction/:id/auctioneer",
					name: pageNameToRoute[PageName.CREDITOR_AUCTIONEER_PAGE].name,
					component: () => import("@/views/Auction/CreditorAuctioneer.vue"), // Use created placeholder
					props: true,
					meta: { title: "Creditor Auctioneer View", roles: ["AUCTIONEER"] },
				},
				{
					path: "auction/:id/trader",
					name: pageNameToRoute[PageName.CREDITOR_TRADER_PAGE].name,
					component: () => import("@/views/Auction/CreditorTrader.vue"), // Use created placeholder
					props: true,
					meta: { title: "Creditor Trader View", roles: ["TRADER"] },
				},
			]
		},


		// --- Catchall ---
		{
			path: "/:pathMatch(.*)*",
			name: "NotFound",
			component: () => import("@/views/NotFound.vue"),
			meta: {
				theme: { layout: Layout.Blank, boxed: { enabled: false }, padded: { enabled: false } },
				skipPin: true,
			},
		},
	],
});

router.beforeEach(route => {
	return authCheck(route); // Use Pinx's auth check utility
});

export default router;

=== src/utils/auth.ts
import type { Role, Roles, RouteMetaAuth } from "@/types/auth.d"; // Ensure Roles is defined correctly here
import type { RouteLocationNormalized, NavigationGuardNext } from "vue-router";
import { useAuthStore } from "@/stores/auth";
import _castArray from "lodash/castArray"; // Make sure lodash is installed

// Define the expected return type for a navigation guard
type NavigationGuardReturn = void | Error | string | boolean | RouteLocationNormalized;

export function authCheck(route: RouteLocationNormalized): NavigationGuardReturn {
	// Destructure directly from route.meta, relying on extended RouteMeta type
	// Make properties optional as they might not exist on all meta objects
	const { checkAuth, authRedirect, auth, roles }: Partial<RouteMetaAuth> = route.meta;
	const authStore = useAuthStore();

	// Logout handling - Check if redirectedFrom exists and its name is 'Logout'
	if (route.redirectedFrom && route.redirectedFrom.name === "Logout") {
		console.log("[AuthCheck] Detected redirection from Logout. Logging out.");
		authStore.setLogout();
		// No need to return anything here, the redirect to /login will happen naturally
	}

	// --- Auth Required Check ---
	// If the route requires authentication (`auth: true`)
	if (auth) {
		// Check if user is logged in
		if (!authStore.isLogged) {
			console.warn(`[AuthCheck] Auth required for "${String(route.name)}", but user not logged in. Redirecting to login.`);
			const loginPath = `/login?redirect=${encodeURIComponent(route.fullPath)}`; // Redirect back after login
			return loginPath; // Return path to redirect
		}
		// Check if user has the required role(s)
		// Ensure isRoleGranted handles undefined roles gracefully
		if (roles && !authStore.isRoleGranted(roles)) {
			console.warn(`[AuthCheck] Role check failed for "${String(route.name)}". User role: ${authStore.userRole}, Required: ${JSON.stringify(roles)}. Redirecting to home (or an unauthorized page).`);
			// Redirect to home or a dedicated 'Unauthorized' page
			return "/"; // Or return { name: 'Unauthorized' } if you have such a route
		}
	}

	// --- Logged-in User Accessing Public Auth Pages Check ---
	// If the route is marked with `checkAuth: true` (e.g., /login, /register)
	// and the user *is* already logged in
	if (checkAuth && authStore.isLogged) {
		console.log(`[AuthCheck] User is logged in, attempting to access auth page "${String(route.name)}". Redirecting away.`);
		// Redirect to the intended page after login, or home as a fallback
		// Simplified: Just redirect home. Checking target route roles here adds complexity.
		return authRedirect || "/";
	}

	// If none of the above conditions caused a redirect, allow navigation
	return true; // Explicitly allow navigation
}

=== src/views/Auth/main.scss
.page-auth {
	min-height: 100vh;

	.wrapper {
		min-height: 100vh;

		.image-box {
			position: relative;
            background-color: var(--primary-color); // Use CSS var

			&::after {
				content: "";
				width: 100%;
				height: 100%;
				position: absolute;
				top: 0;
				left: 0;
				background-image: url(@/assets/images/pattern-onboard.png); // Ensure this path is correct relative to your project structure
				background-size: 500px;
				background-position: center center;
                opacity: 0.1; // Make pattern subtle
			}
		}

		.form-box {
			padding: 50px;

			&.centered {
				flex-basis: 100%;
				.form-wrap { // This class might be inside AuthForm.vue in Pinx
					padding: 60px;
					width: 100%;
					max-width: 500px;
					background-color: var(--bg-default-color);
					border-radius: 20px;
					@apply shadow-xl;
				}

				@media (max-width: 600px) {
					padding: 4%;
					.form-wrap {
						padding: 8%;
					}
				}
			}
		}
	}
	@media (max-width: 800px) {
		.wrapper {
			.image-box {
				display: none;
			}

			.form-box {
				flex-basis: 100%;
                padding: 20px; // Adjust padding for smaller screens
			}
		}
	}
}

=== src/types/auth.d.ts
// Use string literals for roles based on backend AuUserRole enum
export type Role = "AUCTIONEER" | "TRADER";
// Allow 'all' for meta checks, and handle single or multiple roles
export type Roles = Role | Role[] | "all";

// Extend RouteMeta from vue-router
export interface RouteMetaAuth {
	checkAuth?: boolean; // Should redirect away if logged in (e.g., login page)
	authRedirect?: string; // Where to redirect if checkAuth fails (user is logged in)
	auth?: boolean; // Does this route require authentication?
	roles?: Roles; // Which roles can access this route?
}

// Ensure vue-router augmentation includes these fields
// This might be better placed in src/router-env.d.ts

=== src/router-env.d.ts
import type { RouteMetaAuth } from "@/types/auth.d";
import type { Layout } from "@/types/theme.d";
import "vue-router";

// To ensure it is treated as a module, add at least one `export` statement
export {};

declare module "vue-router" {
	interface RouteMeta extends RouteMetaAuth { // Include auth properties
		title?: string;
		theme?: {
			layout?: Layout;
			boxed?: { enabled?: boolean };
			padded?: { enabled?: boolean };
		};
		skipPin?: boolean;
	}
}

=== src/assets/images/brand-logo_dark.svg
<svg width="25" height="37" viewBox="0 0 25 37" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="pinx-icon">
<path id="Rectangle 49" d="M12.395 24.6388H0.117438V36.9164H6.34149C9.68476 36.9164 12.395 34.2061 12.395 30.8628V24.6388Z" fill="#00E19B"/>
<path id="Rectangle 50" d="M24.6726 12.3612H12.395V24.6388H18.6191C21.9624 24.6388 24.6726 21.9285 24.6726 18.5853V12.3612Z" fill="#00E19B"/>
<path id="Rectangle 51" d="M12.395 0.0836182H6.17097C2.82769 0.0836182 0.117438 2.79387 0.117438 6.13715V12.3612H12.395V0.0836182Z" fill="#00E19B"/>
<path id="Rectangle 52" d="M24.6726 6.22241C24.6726 2.83205 21.9242 0.0836182 18.5338 0.0836182H12.395V12.3612H24.6726V6.22241Z" fill="#66F2C7"/>
<path id="Rectangle 53" d="M12.395 18.4147C12.395 15.0715 9.68476 12.3612 6.34149 12.3612H0.117438V24.6388H12.395V18.4147Z" fill="#6267FF"/>
</g>
</svg>

=== src/assets/images/brand-logo_light.svg
<svg width="25" height="37" viewBox="0 0 25 37" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="pinx-icon">
<path id="Rectangle 49" d="M12.395 24.6388H0.117438V36.9164H6.34149C9.68476 36.9164 12.395 34.2061 12.395 30.8628V24.6388Z" fill="#00E19B"/>
<path id="Rectangle 50" d="M24.6726 12.3612H12.395V24.6388H18.6191C21.9624 24.6388 24.6726 21.9285 24.6726 18.5853V12.3612Z" fill="#00E19B"/>
<path id="Rectangle 51" d="M12.395 0.0836182H6.17097C2.82769 0.0836182 0.117438 2.79387 0.117438 6.13715V12.3612H12.395V0.0836182Z" fill="#00E19B"/>
<path id="Rectangle 52" d="M24.6726 6.22241C24.6726 2.83205 21.9242 0.0836182 18.5338 0.0836182H12.395V12.3612H24.6726V6.22241Z" fill="#66F2C7"/>
<path id="Rectangle 53" d="M12.395 18.4147C12.395 15.0715 9.68476 12.3612 6.34149 12.3612H0.117438V24.6388H12.395V18.4147Z" fill="#6267FF"/>
</g>
</svg>

=== src/assets/images/brand-icon_dark.svg
<svg width="25" height="37" viewBox="0 0 25 37" fill="none"
	xmlns="http://www.w3.org/2000/svg">
	<g id="pinx-icon">
		<path id="Rectangle 49" d="M12.395 24.6388H0.117438V36.9164H6.34149C9.68476 36.9164 12.395 34.2061 12.395 30.8628V24.6388Z" fill="#00E19B"/>
		<path id="Rectangle 50" d="M24.6726 12.3612H12.395V24.6388H18.6191C21.9624 24.6388 24.6726 21.9285 24.6726 18.5853V12.3612Z" fill="#00E19B"/>
		<path id="Rectangle 51" d="M12.395 0.0836182H6.17097C2.82769 0.0836182 0.117438 2.79387 0.117438 6.13715V12.3612H12.395V0.0836182Z" fill="#00E19B"/>
		<path id="Rectangle 52" d="M24.6726 6.22241C24.6726 2.83205 21.9242 0.0836182 18.5338 0.0836182H12.395V12.3612H24.6726V6.22241Z" fill="#66F2C7"/>
		<path id="Rectangle 53" d="M12.395 18.4147C12.395 15.0715 9.68476 12.3612 6.34149 12.3612H0.117438V24.6388H12.395V18.4147Z" fill="#6267FF"/>
	</g>
</svg>

=== src/assets/images/brand-icon_light.svg
<svg width="25" height="37" viewBox="0 0 25 37" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="pinx-icon">
<path id="Rectangle 49" d="M12.395 24.6388H0.117438V36.9164H6.34149C9.68476 36.9164 12.395 34.2061 12.395 30.8628V24.6388Z" fill="#00E19B"/>
<path id="Rectangle 50" d="M24.6726 12.3612H12.395V24.6388H18.6191C21.9624 24.6388 24.6726 21.9285 24.6726 18.5853V12.3612Z" fill="#00E19B"/>
<path id="Rectangle 51" d="M12.395 0.0836182H6.17097C2.82769 0.0836182 0.117438 2.79387 0.117438 6.13715V12.3612H12.395V0.0836182Z" fill="#00E19B"/>
<path id="Rectangle 52" d="M24.6726 6.22241C24.6726 2.83205 21.9242 0.0836182 18.5338 0.0836182H12.395V12.3612H24.6726V6.22241Z" fill="#66F2C7"/>
<path id="Rectangle 53" d="M12.395 18.4147C12.395 15.0715 9.68476 12.3612 6.34149 12.3612H0.117438V24.6388H12.395V18.4147Z" fill="#6267FF"/>
</g>
</svg>

=== src/assets/images/pattern-onboard.png
// NOTE: This is a placeholder. You need to add the actual pattern-onboard.png file
// to your src/assets/images directory. The content cannot be represented as text.
// Ensure the file path in src/views/Auth/main.scss matches the actual file.
// For now, this entry signifies the file *should* exist.

=== src/assets/images/google-icon.svg
<svg width="29" height="29" viewBox="0 0 29 29" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Google " clip-path="url(#clip0_128_92)">
<path id="Vector" d="M28.227 14.7976C28.227 13.8459 28.1499 12.8891 27.9852 11.9528H14.78V17.344H22.342C22.0282 19.0827 21.02 20.6208 19.5436 21.5982V25.0963H24.055C26.7043 22.6579 28.227 19.057 28.227 14.7976Z" fill="#4285F4"/>
<path id="Vector_2" d="M14.78 28.4761C18.5558 28.4761 21.7401 27.2363 24.0602 25.0963L19.5487 21.5982C18.2935 22.4522 16.673 22.9357 14.7851 22.9357C11.1327 22.9357 8.0359 20.4717 6.92475 17.1588H2.26923V20.7649C4.64586 25.4924 9.48657 28.4761 14.78 28.4761V28.4761Z" fill="#34A853"/>
<path id="Vector_3" d="M6.91963 17.1588C6.33319 15.42 6.33319 13.5372 6.91963 11.7985V8.19238H2.26925C0.283581 12.1483 0.283581 16.809 2.26925 20.7649L6.91963 17.1588V17.1588Z" fill="#FBBC04"/>
<path id="Vector_4" d="M14.78 6.01637C16.7759 5.9855 18.705 6.73656 20.1505 8.11521L24.1476 4.11815C21.6166 1.74152 18.2575 0.434887 14.78 0.476041C9.48657 0.476041 4.64586 3.45969 2.26923 8.19238L6.9196 11.7985C8.02561 8.48045 11.1276 6.01637 14.78 6.01637V6.01637Z" fill="#EA4335"/>
</g>
<defs>
<clipPath id="clip0_128_92">
<rect width="28" height="28" fill="white" transform="translate(0.5 0.475098)"/>
</clipPath>
</defs>
</svg>

=== src/assets/images/facebook-icon.svg
<svg width="29" height="29" viewBox="0 0 29 29" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Facebook " clip-path="url(#clip0_128_129)">
<path id="Vector" d="M28.5 14.4751C28.5 6.7431 22.232 0.475098 14.5 0.475098C6.76801 0.475098 0.5 6.7431 0.5 14.4751C0.5 21.4628 5.61957 27.2547 12.3125 28.305V18.522H8.75781V14.4751H12.3125V11.3907C12.3125 7.88197 14.4027 5.94385 17.6005 5.94385C19.1318 5.94385 20.7344 6.21728 20.7344 6.21728V9.6626H18.9691C17.23 9.6626 16.6875 10.7419 16.6875 11.8501V14.4751H20.5703L19.9496 18.522H16.6875V28.305C23.3804 27.2547 28.5 21.4628 28.5 14.4751Z" fill="#1877F2"/>
<path id="Vector_2" d="M19.9496 18.522L20.5703 14.4751H16.6875V11.8501C16.6875 10.7429 17.23 9.6626 18.9691 9.6626H20.7344V6.21729C20.7344 6.21729 19.1323 5.94385 17.6005 5.94385C14.4027 5.94385 12.3125 7.88197 12.3125 11.3907V14.4751H8.75781V18.522H12.3125V28.305C13.762 28.5318 15.238 28.5318 16.6875 28.305V18.522H19.9496Z" fill="white"/>
</g>
<defs>
<clipPath id="clip0_128_129">
<rect width="28" height="28" fill="white" transform="translate(0.5 0.475098)"/>
</clipPath>
</defs>
</svg>


=== src/composables/useGlobalActions.ts
import type { MessageOptions } from "naive-ui"
import type { MessageApiInjection, MessageReactive } from "naive-ui/es/message/src/MessageProvider"
import type { NotificationOptions } from "naive-ui/es/notification/src/NotificationEnvironment"
import type { NotificationApiInjection, NotificationReactive } from "naive-ui/es/notification/src/NotificationProvider"
import type { Writable } from "type-fest"

export type NotificationObject = Writable<NotificationOptions>

interface InitPayload {
	message: MessageApiInjection
	notification: NotificationApiInjection
}

let message: MessageApiInjection | null = null
let notification: NotificationApiInjection | null = null

export function useGlobalActions() {
	return {
		init: (payload: InitPayload): void => {
			message = payload.message
			notification = payload.notification
		},
		message: (content: string, options?: MessageOptions): MessageReactive | undefined => {
            if (!message) {
                console.error("Message provider not initialized. Call init() first in your main layout/provider.");
                return undefined;
            }
			return message?.create(content, options || { type: "info" })
		},
		notification: (options: NotificationOptions): NotificationReactive | undefined => {
            if (!notification) {
                console.error("Notification provider not initialized. Call init() first in your main layout/provider.");
                return undefined;
            }
			return notification?.create(options)
		}
	}
}

=== src/components/common/PasswordStrengthMeter.vue
<template>
	<div class="flex flex-col gap-4">
		<div v-if="showMeter" class="flex h-2 w-full items-center gap-2">
			<div v-for="index of maxScore" :key="index" class="h-full grow rounded-sm" :class="getStatus(index)"></div>
		</div>
		<div v-if="showList" class="flex flex-col gap-3">
			<div v-if="minLength" class="flex items-center gap-2">
				<Icon
					:size="18"
					:name="hasLength ? 'solar:check-square-bold-duotone' : 'solar:close-square-bold-duotone'"
					:class="[hasLength ? 'text-success/50' : 'text-tertiary/50']"
				/>
				<span class="text-sm">At least 8 characters</span>
			</div>
			<div v-if="lowercase" class="flex items-center gap-2">
				<Icon
					:size="18"
					:name="hasLower ? 'solar:check-square-bold-duotone' : 'solar:close-square-bold-duotone'"
					:class="[hasLower ? 'text-success/50' : 'text-tertiary/50']"
				/>
				<span class="text-sm">At least 1 lowercase character</span>
			</div>
			<div v-if="uppercase" class="flex items-center gap-2">
				<Icon
					:size="18"
					:name="hasUpper ? 'solar:check-square-bold-duotone' : 'solar:close-square-bold-duotone'"
					:class="[hasUpper ? 'text-success/50' : 'text-tertiary/50']"
				/>
				<span class="text-sm">At least 1 Uppercase character</span>
			</div>
			<div v-if="number" class="flex items-center gap-2">
				<Icon
					:size="18"
					:name="hasNumber ? 'solar:check-square-bold-duotone' : 'solar:close-square-bold-duotone'"
					:class="[hasNumber ? 'text-success/50' : 'text-tertiary/50']"
				/>
				<span class="text-sm">At least 1 number</span>
			</div>
			<div v-if="special" class="flex items-center gap-2">
				<Icon
					:size="18"
					:name="hasSpecial ? 'solar:check-square-bold-duotone' : 'solar:close-square-bold-duotone'"
					:class="[hasSpecial ? 'text-success/50' : 'text-tertiary/50']"
				/>
				<span class="text-sm">
					At least 1 special character
					<code>{{ specialString }}</code>
				</span>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import Icon from "@/components/common/Icon.vue"; // Assuming Icon component exists
import { computed, watch } from "vue";

const {
	password,
	minLength = 8,
	lowercase = true,
	uppercase = true,
	number = true,
	special = true,
	showMeter = true,
	showList = true
} = defineProps<{
	password: string;
	minLength?: number;
	lowercase?: boolean;
	uppercase?: boolean;
	number?: boolean;
	special?: string | boolean;
	showMeter?: boolean;
	showList?: boolean;
}>();

const emit = defineEmits<{
	/** 0 | 1 | 2 | 3 | 4 | 5 */
	(e: "score", value: number): void;
	/** 0% - 100% */
	(e: "strength", value: number): void;
}>();

const specialString = computed<string>(() => (typeof special === "string" ? special : "!@#$%^&*"));
const hasLength = computed<boolean>(() => password.length >= minLength);
const hasLower = computed<boolean>(() => /[a-z]/.test(password));
const hasUpper = computed<boolean>(() => /[A-Z]/.test(password));
const hasNumber = computed<boolean>(() => /\d/.test(password));
const hasSpecial = computed<boolean>(() => new RegExp(`[${specialString.value.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')}]`).test(password)); // Escape special regex chars

const maxScore = computed<number>(() => {
	let score = 0;

	if (minLength) score++;
	if (lowercase) score++;
	if (uppercase) score++;
	if (number) score++;
	if (special) score++;

	return score;
});

const score = computed<number>(() => {
	let score = 0;

	if (minLength && hasLength.value) score++;
	if (lowercase && hasLower.value) score++;
	if (uppercase && hasUpper.value) score++;
	if (number && hasNumber.value) score++;
	if (special && hasSpecial.value) score++;

	return score;
});

const strength = computed(() => maxScore.value > 0 ? (score.value / maxScore.value) * 100 : 0);

function getStatus(index: number): string {
	const indexStrength = maxScore.value > 0 ? (index / maxScore.value) * 100 : 0;

	if (indexStrength <= strength.value) {
		if (strength.value === 100) return "bg-success"; // Use Tailwind classes or CSS variables
		if (strength.value > 50) return "bg-warning";
		if (strength.value > 0) return "bg-error"; // Changed from bg-error to indicate some strength
	}
	return "bg-border"; // Use a neutral border/background color
}


watch(
	[score, strength],
	([valScore, valStrength]) => {
		emit("score", valScore);
		emit("strength", valStrength);
	},
	{ immediate: true }
);
</script>

<style scoped>
/* Add Tailwind utility classes directly in the template or define them here/globally */
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-error { background-color: var(--error-color); }
.bg-border { background-color: var(--border-color); } /* Or var(--bg-secondary-color) */

.text-success\/50 { color: rgba(var(--success-color-rgb), 0.5); }
.text-tertiary\/50 { color: rgba(var(--fg-tertiary-color-rgb), 0.5); } /* Assuming --fg-tertiary-color-rgb exists */
</style>

=== src/App.vue
<template>
	<Provider>
		<component
			:is="layoutComponent"
			id="app-layout"
			:class="[
				`theme-${themeName}`,
				`layout-${layoutComponentName}`,
				`route-${routeName}`,
				{ 'opacity-0': loading }
			]"
		>
			<RouterView v-slot="{ Component: RouterComponent }">
				<transition :name="`router-${routerTransition}`" mode="out-in" appear>
					<!--
						Here you can choose whether to keep each page alive by wrapping <component/> inside <keep-alive>.

						Please note:
						Keep in mind that third-party components might not handle this behavior.
					-->
					<component :is="RouterComponent" id="app-page" :key="routeName + forceRefresh" />
				</transition>
			</RouterView>
		</component>

		<SplashScreen :show="loading" />
		<SearchDialog v-if="isLogged" />
		<LayoutSettings />
	</Provider>
</template>

<script lang="ts" setup>
import type { Layout, RouterTransition, ThemeNameEnum } from "@/types/theme.d";
import type { Component } from "vue";
import type { RouteLocationNormalized } from "vue-router";
import Blank from "@/app-layouts/Blank";
import Provider from "@/app-layouts/common/Provider.vue"; // Ensure Provider is correctly imported/created
import SplashScreen from "@/app-layouts/common/SplashScreen.vue"; // Ensure SplashScreen is correctly imported/created
import HorizontalNav from "@/app-layouts/HorizontalNav";
import VerticalNav from "@/app-layouts/VerticalNav";
import LayoutSettings from "@/components/common/LayoutSettings.vue"; // Ensure LayoutSettings is correctly imported/created
import SearchDialog from "@/components/common/SearchDialog.vue"; // Ensure SearchDialog is correctly imported/created
import { useAuthStore } from "@/stores/auth";
import { useMainStore } from "@/stores/main";
import { useThemeStore } from "@/stores/theme";
import { computed, onMounted, ref, watch } from "vue";
import { useRoute, useRouter } from "vue-router";

const router = useRouter();
const route = useRoute();
const loading = ref(true);

const layoutComponents = {
	VerticalNav,
	HorizontalNav,
	Blank
};

const themeStore = useThemeStore();
const mainStore = useMainStore();
const authStore = useAuthStore();

const routeName = computed<string>(() => route?.name?.toString() || "");
const forceRefresh = computed<number>(() => mainStore.forceRefresh);
const forceLayout = ref<Layout | null>(null);
const layout = computed<Layout>(() => themeStore.layout);
const layoutComponentName = computed<Layout>(() => forceLayout.value || layout.value);
const layoutComponent = computed<Component>(() => layoutComponents[layoutComponentName.value]);
const routerTransition = computed<RouterTransition>(() => themeStore.routerTransition);
const themeName = computed<ThemeNameEnum>(() => themeStore.themeName);
const isLogged = computed(() => authStore.isLogged);

function checkThemeOverrides(currentRoute: RouteLocationNormalized) {
	if (currentRoute.meta?.theme?.layout !== undefined) {
		forceLayout.value = currentRoute.meta.theme.layout;
	} else {
		forceLayout.value = null;
	}
}

watch(layoutComponentName, () => {
	// This might be too early if theme init hasn't finished
	// loading.value = false;
});

router.afterEach(currentRoute => {
	checkThemeOverrides(currentRoute);
});

onMounted(() => {
	checkThemeOverrides(route);

	// *** Initialize theme watchers and CSS vars ***
	themeStore.initTheme();

	// Delay setting loading to false until after theme init might have run
	setTimeout(() => {
		loading.value = false;
	}, 50); // Adjust delay if needed
});
</script>

=== src/lang/index.ts
import type { LocaleCodes, MessageSchema } from "./config";
import { createI18n } from "vue-i18n";
import { getI18NConf } from "./config";

const instance = createI18n<[MessageSchema], LocaleCodes>(getI18NConf());

export default instance; // Default export is the i18n instance

// *** Named export for the global scope ***
export const i18nGlobal = instance.global;

=== src/stores/appStore.ts
import type {
	AuctionRowElement,
	CompanyElement,
	CounterpartyCreditElement,
	MessageElement,
	SessionUserValue,
	TimeValue,
	UserElement
} from "@/types/generated"; // Assuming generated types exist
import type { DeAuctionValue, DeMatrixRoundElement } from "@/types/generated"; // Assuming generated types exist
import { defineStore } from "pinia";

// Mirroring the backend's LiveClientStore structure
interface AppState {
	auction_rows: AuctionRowElement[];
	companies: CompanyElement[];
	counterparty_credits: CounterpartyCreditElement[];
	de_auction: DeAuctionValue | null;
	session_user: SessionUserValue | null;
	time: TimeValue | null;
	users: UserElement[];
	// Add a specific place for matrix rounds if needed frequently outside de_auction
	de_matrix_rounds: DeMatrixRoundElement[];
	messages: MessageElement[]; // Assuming messages might be added incrementally
	websocketConnected: boolean;
	lastMessageTimestamp: number | null;
}

export const useAppStore = defineStore("app", {
	state: (): AppState => ({
		auction_rows: [],
		companies: [],
		counterparty_credits: [],
		de_auction: null,
		session_user: null,
		time: null,
		users: [],
		de_matrix_rounds: [],
		messages: [],
		websocketConnected: false,
		lastMessageTimestamp: null,
	}),

	actions: {
		setLiveStore(storeData: Partial<AppState>) {
			// Deep merge might be better, but direct assignment matches backend push
			// Consider using lodash merge or similar if partial updates are expected
			// within SetLiveStore, otherwise, this assumes a full state push.
			if (storeData.auction_rows !== undefined) this.auction_rows = storeData.auction_rows;
			if (storeData.companies !== undefined) this.companies = storeData.companies;
			if (storeData.counterparty_credits !== undefined) this.counterparty_credits = storeData.counterparty_credits;
			if (storeData.de_auction !== undefined) this.de_auction = storeData.de_auction;
			if (storeData.session_user !== undefined) this.session_user = storeData.session_user;
			if (storeData.time !== undefined) this.time = storeData.time;
			if (storeData.users !== undefined) this.users = storeData.users;

			// If messages/matrix rounds are part of LiveClientStore, update them here too.
			// If they are only updated via AddElements, handle them there.
			if (storeData.de_auction?.messages) {
				this.messages = storeData.de_auction.messages;
			}
			if (storeData.de_auction?.matrix_last_round) {
				// Assuming only the last round is sent in the main store push
				this.de_matrix_rounds = [storeData.de_auction.matrix_last_round];
			}

			console.log("LiveStore updated"); // Reduced verbosity
		},

		addElements(path: string, elements: any[] | null) {
			console.log(`AddElements received for path: ${path}`, elements ? `${elements.length} elements` : 'null (clear)');
			if (elements === null) {
				// Handle clearing the array
				if (path === "AuctionRowElement") this.auction_rows = [];
				else if (path === "CompanyElement") this.companies = [];
				else if (path === "CounterpartyCreditElement") this.counterparty_credits = [];
				else if (path === "MessageElement") this.messages = [];
				else if (path === "UserElement") this.users = [];
				else if (path === "DeMatrixRoundElement") this.de_matrix_rounds = [];
				// Add other paths as needed
				else console.warn(`AddElements: Unknown path to clear: ${path}`);
				return;
			}

			if (!Array.isArray(elements)) {
				console.error("AddElements: received non-array elements", elements);
				return;
			}

			// Find the target array and update/add elements
			let targetArray: any[] | undefined;
			if (path === "AuctionRowElement") targetArray = this.auction_rows;
			else if (path === "CompanyElement") targetArray = this.companies;
			else if (path === "CounterpartyCreditElement") targetArray = this.counterparty_credits;
			else if (path === "MessageElement") targetArray = this.messages;
			else if (path === "UserElement") targetArray = this.users;
			else if (path === "DeMatrixRoundElement") targetArray = this.de_matrix_rounds;
			// Add other paths as needed

			if (targetArray) {
				elements.forEach(newElement => {
					if (!newElement || typeof newElement.id === 'undefined') {
						console.warn("AddElements: Skipping element without id", newElement);
						return;
					}
					const index = targetArray.findIndex(item => item.id === newElement.id);
					if (index !== -1) {
						// Update existing element (important for reactivity)
						// Use spread or Object.assign for better reactivity handling if needed
						targetArray[index] = { ...targetArray[index], ...newElement };
					} else {
						// Add new element
						targetArray.push(newElement);
					}
				});
				// Optional: Sort arrays if needed after updates
				if (path === 'MessageElement') this.messages.sort((a, b) => a.timestamp - b.timestamp);
				if (path === 'DeMatrixRoundElement') this.de_matrix_rounds.sort((a, b) => a.round_number - b.round_number);

			} else {
				console.warn(`AddElements: Unknown path: ${path}`);
			}
		},

		setWebsocketConnected(status: boolean) {
			this.websocketConnected = status;
		},

		setLastMessageTimestamp(ts: number) {
			this.lastMessageTimestamp = ts;
		},

		resetState() {
			this.$reset();
			console.log("AppStore state reset");
		}
	},
	getters: {
		isAuctioneer: (state) => state.session_user?.isAuctioneer ?? false,
		isTrader: (state) => !state.session_user?.isAuctioneer && !!state.session_user?.role, // Assuming non-auctioneer logged in is trader
		currentSessionId: (state) => state.session_user?.session_id,
		currentPage: (state) => state.session_user?.current_page,
		currentAuctionId: (state) => state.session_user?.current_auction_id,
		// Add more specific getters as needed e.g., getTraderInfo, getAuctioneerStatus
		getDeAuctioneerStatus: (state) => state.de_auction?.auctioneer_status,
		getDeCommonStatus: (state) => state.de_auction?.common_status,
		getDeTraderInfo: (state) => state.de_auction?.trader_info,
		getDeTraderHistory: (state) => state.de_auction?.trader_history_rows ?? [],
		getDeBlotter: (state) => state.de_auction?.blotter,
		getDeMatrixLastRound: (state) => state.de_auction?.matrix_last_round,
		getMessagesForCurrentAuction: (state) => {
			// Filter messages based on current auction context if needed,
			// or assume messages in the store are already filtered by backend push
			// Ensure sorting happens here if AddElements doesn't guarantee order
			return [...state.messages].sort((a, b) => a.timestamp - b.timestamp);
		}
	}
});

=== src/services/websocket.ts
import { useAppStore } from "@/stores/appStore";
import { useAuthStore } from "@/stores/auth";
import { useMainStore } from "@/stores/main";
import { useGlobalActions } from "@/composables/useGlobalActions";
import { ref, watch } from "vue";
import { i18nGlobal } from "@/lang"; // Assuming i18n setup for messages

// Assuming generated types exist
import type { ClientCommand, EngineCommand, EngineCommandEnvelope } from "@/types/generated";
import { CommandType, BrowserMessageKind } from "@/types/generated";

// Make WS URL configurable via environment variables
const WS_BASE_URL = import.meta.env.VITE_WS_URL || 'ws://localhost:8080/socket'; // Default fallback

let websocket: WebSocket | null = null;
let reconnectAttempts = 0;
const maxReconnectAttempts = 5;
const reconnectInterval = 5000; // 5 seconds
let reconnectTimeout: NodeJS.Timeout | null = null;
let currentSessionId = ref<string | null>(null); // Track the session ID we *should* be connected with

export function useWebSocket() {
	const appStore = useAppStore();
	const authStore = useAuthStore();
	const mainStore = useMainStore();
	const { message: showMessage, notification: showNotification } = useGlobalActions(); // Assuming global setup

	// Watch for session ID changes to connect/disconnect
	// This is the primary trigger for connection management
	watch(() => appStore.currentSessionId, (newSid, oldSid) => {
		console.log(`[WS] Session ID watcher: changed from ${oldSid} to ${newSid}`);
		if (newSid && newSid !== currentSessionId.value) {
			console.log(`[WS] New session ID detected (${newSid}), initiating connection.`);
			currentSessionId.value = newSid; // Update the target session ID
			disconnectWebSocket(false); // Disconnect old if exists, don't trigger reconnect logic
			connectWebSocket(newSid);
		} else if (!newSid && currentSessionId.value) {
			console.log("[WS] Session ID removed, disconnecting.");
			currentSessionId.value = null; // Clear target session ID
			disconnectWebSocket(false); // Disconnect cleanly
		}
	}, { immediate: true });

	// Watch for login status changes (secondary check, mainly for logout)
	watch(() => authStore.isLogged, (loggedIn) => {
		console.log(`[WS] Auth state watcher: loggedIn=${loggedIn}`);
		if (!loggedIn && currentSessionId.value) {
			console.log("[WS] User logged out, disconnecting WebSocket.");
			currentSessionId.value = null; // Clear target session ID
			disconnectWebSocket(false); // Clean disconnect on logout
			appStore.resetState(); // Clear app state on logout
		}
		// Connection on login is handled by the session_id watch
	});

	function getWebSocketURL(sessionId: string): string {
		// Add browser info as query params, matching backend expectations
		const browserInfo = {
			browser_name: navigator.userAgentData?.brands?.[0]?.brand || navigator.vendor || "Unknown",
			browser_version: navigator.userAgentData?.brands?.[0]?.version || navigator.appVersion || "Unknown",
			browser_os: navigator.platform || "Unknown",
		};
		const queryParams = new URLSearchParams(browserInfo).toString();
		const url = `${WS_BASE_URL}/${sessionId}`; // Use template literal
		return `${url}?${queryParams}`;
	}

	function connectWebSocket(sessionId: string) {
		if (!sessionId) {
			console.error("[WS] Cannot connect: No Session ID provided.");
			return;
		}
		// Prevent multiple connections for the same session ID
		if (websocket && websocket.readyState !== WebSocket.CLOSED && websocket.url.includes(sessionId)) {
			console.log(`[WS] Already connected or connecting to session ${sessionId}.`);
			return;
		}
		// If a WS exists for a *different* session, disconnect it first
		if (websocket && websocket.readyState !== WebSocket.CLOSED && !websocket.url.includes(sessionId)) {
			console.warn(`[WS] Disconnecting obsolete WebSocket for session ${websocket.url.split('/').pop()?.split('?')[0]}`);
			disconnectWebSocket(false);
		}


		const url = getWebSocketURL(sessionId);
		console.log(`[WS] Connecting to: ${url}`);
		appStore.setWebsocketConnected(false);
		mainStore.loadingBar?.start();

		try {
			websocket = new WebSocket(url);
		} catch (error) {
			console.error("[WS] Failed to create WebSocket:", error);
			mainStore.loadingBar?.error();
			scheduleReconnect(sessionId); // Attempt reconnect even if creation fails
			return;
		}


		websocket.onopen = () => {
			// Double check if this is still the desired session before proceeding
			if (currentSessionId.value !== sessionId) {
				console.warn(`[WS] Connection opened for ${sessionId}, but desired session is now ${currentSessionId.value}. Closing.`);
				disconnectWebSocket(false);
				return;
			}
			console.log(`[WS] Connection established for session ${sessionId}.`);
			appStore.setWebsocketConnected(true);
			reconnectAttempts = 0; // Reset attempts on successful connection
			clearTimeout(reconnectTimeout!);
			mainStore.loadingBar?.finish();
			// Backend should send ClientSocketCommand(OPENED) and then push initial state
		};

		websocket.onmessage = (event) => {
			// Ensure this message is for the *current* session
			if (currentSessionId.value !== sessionId) {
				console.warn(`[WS] Received message for obsolete session ${sessionId}. Ignoring.`);
				return;
			}
			appStore.setLastMessageTimestamp(Date.now());
			// Assume backend sends gzipped binary data - Placeholder for ungzip
			if (event.data instanceof Blob) {
				const reader = new FileReader();
				reader.onload = function () {
					try {
						const arrayBuffer = reader.result as ArrayBuffer;
						// *** Placeholder for ungzip ***
						// Replace this with actual ungzip logic using pako or similar
						console.warn("[WS] Binary message received, ungzip not implemented. Decoding as UTF-8 text.");
						const textDecoder = new TextDecoder("utf-8");
						handleMessage(textDecoder.decode(arrayBuffer));
						// *** End Placeholder ***

					} catch (error) {
						console.error("[WS] Error processing binary message:", error);
						mainStore.loadingBar?.error();
					}
				};
				reader.onerror = (error) => {
					console.error("[WS] Error reading Blob:", error);
					mainStore.loadingBar?.error();
				};
				reader.readAsArrayBuffer(event.data);
			} else if (typeof event.data === 'string') {
				// Handle plain text messages
				// console.log("[WS] Received Text Message (raw):", event.data.substring(0, 200) + "...");
				handleMessage(event.data);
			} else {
				console.warn("[WS] Received unexpected message type:", typeof event.data);
			}
		};

		websocket.onerror = (error) => {
			console.error(`[WS] Error for session ${sessionId}:`, error);
			// Only attempt reconnect if this is the currently active session
			if (currentSessionId.value === sessionId) {
				appStore.setWebsocketConnected(false);
				mainStore.loadingBar?.error();
				showMessage(i18nGlobal.t('errors.websocketConnection'), { type: 'error', duration: 0, closable: true });
				scheduleReconnect(sessionId);
			} else {
				console.log(`[WS] Ignoring error for obsolete session ${sessionId}.`);
			}
		};

		websocket.onclose = (event) => {
			console.log(`[WS] Connection closed for session ${sessionId}: Code=${event.code}, Reason=${event.reason}, Clean=${event.wasClean}`);
			// Only manage state and reconnect if this was the *intended* active connection
			if (currentSessionId.value === sessionId) {
				appStore.setWebsocketConnected(false);
				websocket = null; // Clear the reference only if it's the current one closing

				// Reconnect only if:
				// 1. It wasn't a clean close (code 1000 is normal) OR it was closed unexpectedly (e.g., server restart 1006)
				// 2. The user is still supposed to be logged in (authStore.isLogged)
				// 3. This session ID is still the one we want (currentSessionId.value === sessionId)
				if ((!event.wasClean || event.code === 1006) && authStore.isLogged) {
					console.log("[WS] Unclean close or server issue, attempting reconnect...");
					showMessage(i18nGlobal.t('errors.websocketDisconnected'), { type: 'warning' });
					mainStore.loadingBar?.error();
					scheduleReconnect(sessionId);
				} else {
					console.log("[WS] Clean close or user logged out/session changed, not reconnecting.");
					mainStore.loadingBar?.finish(); // Finish loading bar if closed cleanly
				}
			} else {
				console.log(`[WS] Ignoring close event for obsolete session ${sessionId}.`);
				// If the closed websocket is the one stored in the global `websocket` variable, clear it
				if (websocket === event.target) {
					websocket = null;
				}
			}
		};
	}

	function scheduleReconnect(sessionId: string) {
		// Only schedule if this is still the desired session
		if (currentSessionId.value !== sessionId) {
			console.log(`[WS] Reconnect cancelled for ${sessionId}, desired session is ${currentSessionId.value}.`);
			return;
		}

		if (reconnectAttempts >= maxReconnectAttempts) {
			console.error("[WS] Max reconnect attempts reached. Giving up.");
			showMessage(i18nGlobal.t('errors.websocketReconnectFailed'), { type: 'error', duration: 0, closable: true });
			// Optional: Force logout or redirect to login
			// authStore.setLogout(); // This might trigger disconnectWebSocket via watcher
			// router.push('/login');
			return;
		}

		clearTimeout(reconnectTimeout!); // Clear any existing timeout
		reconnectAttempts++;
		const delay = reconnectInterval * Math.pow(2, reconnectAttempts - 1); // Exponential backoff
		console.log(`[WS] Scheduling reconnect attempt ${reconnectAttempts}/${maxReconnectAttempts} for session ${sessionId} in ${delay / 1000}s`);

		reconnectTimeout = setTimeout(() => {
			// Double-check the session ID *again* before actually connecting
			if (currentSessionId.value === sessionId) {
				console.log(`[WS] Attempting reconnect ${reconnectAttempts} for session ${sessionId}...`);
				connectWebSocket(sessionId);
			} else {
				console.log(`[WS] Reconnect attempt ${reconnectAttempts} cancelled for ${sessionId}, desired session changed to ${currentSessionId.value}.`);
				reconnectAttempts = 0; // Reset attempts as we are no longer trying for this session
			}
		}, delay);
	}

	function disconnectWebSocket(clean = true) {
		clearTimeout(reconnectTimeout!); // Always clear reconnect timeout on disconnect
		reconnectAttempts = 0; // Reset attempts
		if (websocket) {
			const reason = clean ? "Client requested disconnect" : "Switching sessions";
			console.log(`[WS] Disconnecting WebSocket (${reason}). State: ${websocket.readyState}`);
			if (websocket.readyState === WebSocket.OPEN || websocket.readyState === WebSocket.CONNECTING) {
				websocket.close(1000, reason); // 1000 is normal closure
			}
			websocket = null; // Clear the reference
		}
		// Only update store if it reflects the current state intention
		if (appStore.websocketConnected) {
			appStore.setWebsocketConnected(false);
		}
	}


	function handleMessage(messageJson: string) {
		try {
			// console.log("[WS] Handling Message:", messageJson.substring(0, 200) + "...");
			const clientCommand = JSON.parse(messageJson) as ClientCommand; // Assuming ClientCommand is the base type

			switch (clientCommand.command) {
				case CommandType.SetLiveStore:
					const storeCommand = clientCommand as ClientCommand.StoreCommand.SetLiveStore;
					appStore.setLiveStore(storeCommand.store);
					break;
				case CommandType.AddElements:
					const addElementsCommand = clientCommand as ClientCommand.StoreCommand.AddElements;
					appStore.addElements(addElementsCommand.path, addElementsCommand.elements);
					break;
				case CommandType.ShowMessage:
					const showMessageCommand = clientCommand as ClientCommand.ShowMessage;
					const messageType = showMessageCommand.browser_message_kind === BrowserMessageKind.ALERT ? 'error' : 'info'; // Or map more granularly
					const messageText = showMessageCommand.message.join('\n');
					if (messageType === 'error') {
						showMessage(messageText, { type: 'error', duration: 5000, closable: true });
					} else {
						showNotification({
							content: messageText,
							type: messageType, // info, success, warning
							duration: 4000,
							keepAliveOnHover: true
						});
					}
					break;
				case CommandType.TerminateSession:
					const terminateCommand = clientCommand as ClientCommand.TerminateSession;
					console.log("[WS] Session termination requested by server:", terminateCommand.message);
					showMessage(terminateCommand.message || i18nGlobal.t('errors.sessionTerminated'), { type: 'error', duration: 0, closable: true });
					authStore.setLogout(); // This will trigger watchers to disconnect WS and clear state
					// Router guard should handle redirect
					break;
				case CommandType.NetworkDown:
					console.warn("[WS] Backend indicated network issues.");
					showMessage(i18nGlobal.t('errors.networkDown'), { type: 'warning' });
					break;
				case CommandType.NetworkUp:
					console.info("[WS] Backend indicated network is back up.");
					showMessage(i18nGlobal.t('info.networkUp'), { type: 'success' });
					break;
				case CommandType.CommandSucceeded:
					console.log("[WS] Command succeeded ack received.");
					break;
				default:
					console.warn("[WS] Received unknown command type:", clientCommand.command);
			}
		} catch (error) {
			console.error("[WS] Error parsing or handling WebSocket message:", error, "\nMessage was:", messageJson);
			showMessage(i18nGlobal.t('errors.websocketMessageError'), { type: 'error' });
		}
	}

	function sendCommand(command: EngineCommand) {
		if (!currentSessionId.value) {
			console.error("[WS] No active session ID. Cannot send command.");
			showMessage(i18nGlobal.t('errors.sendCommandNoSession'), { type: 'error' });
			return;
		}
		if (!websocket || websocket.readyState !== WebSocket.OPEN) {
			console.error("[WS] WebSocket is not connected. Cannot send command.");
			showMessage(i18nGlobal.t('errors.websocketNotConnected'), { type: 'error' });
			// Optional: Attempt reconnect or queue command? Maybe trigger connect if not connecting
			if (!websocket || websocket.readyState === WebSocket.CLOSED) {
				console.log("[WS] Attempting to reconnect before sending command...");
				connectWebSocket(currentSessionId.value);
				// Maybe queue the command to send after successful connection?
			}
			return;
		}


		// Construct the envelope - Backend needs to handle missing simplename/classname
		const envelope: Partial<EngineCommandEnvelope> = {
			session_id: currentSessionId.value,
			command: command,
		};

		try {
			const messageJson = JSON.stringify(envelope);
			console.log("[WS] Sending Command:", command.constructor.name, JSON.stringify(command).substring(0,100)+"...");
			// TODO: Gzip if required by backend
			websocket.send(messageJson);
		} catch (error) {
			console.error("[WS] Error sending command:", error);
			showMessage(i18nGlobal.t('errors.sendCommandFailed'), { type: 'error' });
		}
	}

	return {
		// connectWebSocket, // Connection is now managed internally by session ID changes
		disconnectWebSocket, // Expose for explicit disconnect (e.g., logout button)
		sendCommand,
		websocketConnected: computed(() => appStore.websocketConnected),
		lastMessageTimestamp: computed(() => appStore.lastMessageTimestamp),
	};
}

=== src/router/index.ts
import type { FormType } from "@/components/auth/types.d";
import { Layout, PageName } from "@/types/generated"; // Assuming PageName is generated
import { authCheck } from "@/utils/auth"; // Corrected import path
import { createRouter, createWebHistory } from "vue-router";

// Helper to map backend PageName to frontend route names/paths
// Adjust paths/names as needed to match your preferred frontend routing structure
const pageNameToRoute = {
	[PageName.LOGIN_PAGE]: { name: "Login", path: "/login" },
	[PageName.HOME_PAGE]: { name: "Home", path: "/" }, // Map to dashboard or appropriate home
	[PageName.USER_PAGE]: { name: "UserManagement", path: "/admin/users" },
	[PageName.COMPANY_PAGE]: { name: "CompanyManagement", path: "/admin/companies" }, // Assuming a dedicated page
	[PageName.SESSION_PAGE]: { name: "SessionManagement", path: "/admin/sessions" },
	[PageName.DE_SETUP_PAGE]: { name: "DeAuctionSetup", path: "/de/setup" }, // Can be combined with edit using :id?
	[PageName.DE_AUCTIONEER_PAGE]: { name: "DeAuctioneer", path: "/de/auction/:id/auctioneer" },
	[PageName.DE_TRADER_PAGE]: { name: "DeTrader", path: "/de/auction/:id/trader" },
	// Add mappings for other PageNames (CREDITOR, BH, MR, TE, TO) if they exist and have UIs
	[PageName.CREDITOR_AUCTIONEER_PAGE]: { name: "CreditorAuctioneer", path: "/creditor/auction/:id/auctioneer" },
	[PageName.CREDITOR_TRADER_PAGE]: { name: "CreditorTrader", path: "/creditor/auction/:id/trader" },
	// ... add others like BH_AUCTIONEER_PAGE etc. if needed
};

const router = createRouter({
	history: createWebHistory(import.meta.env.BASE_URL),
	routes: [
		// Redirect root to a default dashboard or home based on role later
		{
			path: "/",
			name: pageNameToRoute[PageName.HOME_PAGE].name, // Use mapping
			component: () => import("@/views/Dashboard/Analytics.vue"), // Use created placeholder
			meta: { title: "Dashboard", auth: true, roles: "all" },
		},

		// --- Authentication ---
		{
			path: pageNameToRoute[PageName.LOGIN_PAGE].path,
			name: pageNameToRoute[PageName.LOGIN_PAGE].name,
			component: () => import("@/views/Auth/Login.vue"), // Use created placeholder
			meta: {
				title: "Login",
				theme: { layout: Layout.Blank, boxed: { enabled: false }, padded: { enabled: false } },
				checkAuth: true, // Redirect if already logged in
				skipPin: true,
			},
		},
		// Add Register and ForgotPassword if needed, mapping them appropriately
		{
			path: "/register", // No direct PageName mapping, assuming standard flow
			name: "Register",
			component: () => import("@/views/Auth/Login.vue"), // Reuse Login view
			props: { formType: "signup" as FormType },
			meta: {
				title: "Register",
				theme: { layout: Layout.Blank, boxed: { enabled: false }, padded: { enabled: false } },
				checkAuth: true,
				skipPin: true,
			},
		},
		{
			path: "/forgot-password", // No direct PageName mapping
			name: "ForgotPassword",
			component: () => import("@/views/Auth/Login.vue"), // Reuse Login view
			props: { formType: "forgotpassword" as FormType },
			meta: {
				title: "Forgot Password",
				theme: { layout: Layout.Blank, boxed: { enabled: false }, padded: { enabled: false } },
				checkAuth: true,
				skipPin: true,
			},
		},
		{
			path: "/logout",
			name: "Logout",
			redirect: pageNameToRoute[PageName.LOGIN_PAGE].path, // Redirect to login after logout logic
		},

		// --- Admin / Auctioneer Specific ---
		{
			path: "/admin", // Group admin routes
			redirect: "/admin/users",
			meta: { auth: true, roles: ["AUCTIONEER"] }, // Role guard
			children: [
				{
					path: "users",
					name: pageNameToRoute[PageName.USER_PAGE].name,
					component: () => import("@/views/Admin/UserManagement.vue"), // Use created view
					meta: { title: "User Management" },
				},
				{
					path: "companies",
					name: pageNameToRoute[PageName.COMPANY_PAGE].name,
					component: () => import("@/views/Admin/CompanyManagement.vue"), // Use created view
					meta: { title: "Company Management" },
				},
				{
					path: "sessions",
					name: pageNameToRoute[PageName.SESSION_PAGE].name,
					component: () => import("@/views/Admin/SessionManagement.vue"), // Use created view
					meta: { title: "Session Management" },
				},
			],
		},

		// --- DE Auction ---
		{
			path: "/de", // Group DE routes
			redirect: pageNameToRoute[PageName.HOME_PAGE].path, // Redirect to home/dashboard
			meta: { auth: true, roles: "all" }, // Allow both roles, specific views handle finer control
			children: [
				{
					path: "setup", // Route for creating a new auction
					name: pageNameToRoute[PageName.DE_SETUP_PAGE].name + "Create", // Differentiate create/edit
					component: () => import("@/views/Auction/DeAuctionSetup.vue"), // Use created view
					meta: { title: "Create DE Auction", roles: ["AUCTIONEER"] },
				},
				{
					path: "setup/:id", // Route for editing an existing auction
					name: pageNameToRoute[PageName.DE_SETUP_PAGE].name + "Edit",
					component: () => import("@/views/Auction/DeAuctionSetup.vue"), // Reuse the setup view
					props: true, // Pass route params as props
					meta: { title: "Edit DE Auction", roles: ["AUCTIONEER"] },
				},
				{
					path: "auction/:id/auctioneer",
					name: pageNameToRoute[PageName.DE_AUCTIONEER_PAGE].name,
					component: () => import("@/views/Auction/DeAuctioneer.vue"), // Use created view
					props: true,
					meta: { title: "DE Auctioneer View", roles: ["AUCTIONEER"] },
				},
				{
					path: "auction/:id/trader",
					name: pageNameToRoute[PageName.DE_TRADER_PAGE].name,
					component: () => import("@/views/Auction/DeTrader.vue"), // Use created view
					props: true,
					meta: { title: "DE Trader View", roles: ["TRADER"] },
				},
			],
		},

		// --- Add routes for other auction types (Creditor, BH, MR, TE, TO) similarly ---
		// Example for Creditor
		{
			path: "/creditor",
			redirect: pageNameToRoute[PageName.HOME_PAGE].path, // Or a specific creditor dashboard/list
			meta: { auth: true, roles: "all" },
			children: [
				// Add setup/edit routes if applicable
				{
					path: "auction/:id/auctioneer",
					name: pageNameToRoute[PageName.CREDITOR_AUCTIONEER_PAGE].name,
					component: () => import("@/views/Auction/CreditorAuctioneer.vue"), // Use created placeholder
					props: true,
					meta: { title: "Creditor Auctioneer View", roles: ["AUCTIONEER"] },
				},
				{
					path: "auction/:id/trader",
					name: pageNameToRoute[PageName.CREDITOR_TRADER_PAGE].name,
					component: () => import("@/views/Auction/CreditorTrader.vue"), // Use created placeholder
					props: true,
					meta: { title: "Creditor Trader View", roles: ["TRADER"] },
				},
			]
		},


		// --- Catchall ---
		{
			path: "/:pathMatch(.*)*",
			name: "NotFound",
			component: () => import("@/views/NotFound.vue"),
			meta: {
				theme: { layout: Layout.Blank, boxed: { enabled: false }, padded: { enabled: false } },
				skipPin: true,
			},
		},
	],
});

router.beforeEach(route => {
	return authCheck(route); // Use Pinx's auth check utility
});

export default router;

=== src/utils/auth.ts
import type { Role, Roles, RouteMetaAuth } from "@/types/auth.d"; // Ensure Roles is defined correctly here
import type { RouteLocationNormalized, NavigationGuardNext } from "vue-router";
import { useAuthStore } from "@/stores/auth";
import _castArray from "lodash/castArray"; // Make sure lodash is installed

// Define the expected return type for a navigation guard
type NavigationGuardReturn = void | Error | string | boolean | RouteLocationNormalized;

export function authCheck(route: RouteLocationNormalized): NavigationGuardReturn {
	// Destructure directly from route.meta, relying on extended RouteMeta type
	// Make properties optional as they might not exist on all meta objects
	const { checkAuth, authRedirect, auth, roles }: Partial<RouteMetaAuth> = route.meta;
	const authStore = useAuthStore();

	// Logout handling - Check if redirectedFrom exists and its name is 'Logout'
	if (route.redirectedFrom && route.redirectedFrom.name === "Logout") {
		console.log("[AuthCheck] Detected redirection from Logout. Logging out.");
		authStore.setLogout();
		// No need to return anything here, the redirect to /login will happen naturally
	}

	// --- Auth Required Check ---
	// If the route requires authentication (`auth: true`)
	if (auth) {
		// Check if user is logged in
		if (!authStore.isLogged) {
			console.warn(`[AuthCheck] Auth required for "${String(route.name)}", but user not logged in. Redirecting to login.`);
			const loginPath = `/login?redirect=${encodeURIComponent(route.fullPath)}`; // Redirect back after login
			return loginPath; // Return path to redirect
		}
		// Check if user has the required role(s)
		// Ensure isRoleGranted handles undefined roles gracefully
		if (roles && !authStore.isRoleGranted(roles)) {
			console.warn(`[AuthCheck] Role check failed for "${String(route.name)}". User role: ${authStore.userRole}, Required: ${JSON.stringify(roles)}. Redirecting to home (or an unauthorized page).`);
			// Redirect to home or a dedicated 'Unauthorized' page
			return "/"; // Or return { name: 'Unauthorized' } if you have such a route
		}
	}

	// --- Logged-in User Accessing Public Auth Pages Check ---
	// If the route is marked with `checkAuth: true` (e.g., /login, /register)
	// and the user *is* already logged in
	if (checkAuth && authStore.isLogged) {
		console.log(`[AuthCheck] User is logged in, attempting to access auth page "${String(route.name)}". Redirecting away.`);
		// Redirect to the intended page after login, or home as a fallback
		// Simplified: Just redirect home. Checking target route roles here adds complexity.
		return authRedirect || "/";
	}

	// If none of the above conditions caused a redirect, allow navigation
	return true; // Explicitly allow navigation
}

=== src/views/Auth/main.scss
.page-auth {
	min-height: 100vh;

	.wrapper {
		min-height: 100vh;

		.image-box {
			position: relative;
            background-color: var(--primary-color); // Use CSS var

			&::after {
				content: "";
				width: 100%;
				height: 100%;
				position: absolute;
				top: 0;
				left: 0;
				background-image: url(@/assets/images/pattern-onboard.png); // Ensure this path is correct relative to your project structure
				background-size: 500px;
				background-position: center center;
                opacity: 0.1; // Make pattern subtle
			}
		}

		.form-box {
			padding: 50px;

			&.centered {
				flex-basis: 100%;
				.form-wrap { // This class might be inside AuthForm.vue in Pinx
					padding: 60px;
					width: 100%;
					max-width: 500px;
					background-color: var(--bg-default-color);
					border-radius: 20px;
					@apply shadow-xl;
				}

				@media (max-width: 600px) {
					padding: 4%;
					.form-wrap {
						padding: 8%;
					}
				}
			}
		}
	}
	@media (max-width: 800px) {
		.wrapper {
			.image-box {
				display: none;
			}

			.form-box {
				flex-basis: 100%;
                padding: 20px; // Adjust padding for smaller screens
			}
		}
	}
}

=== src/types/auth.d.ts
// Use string literals for roles based on backend AuUserRole enum
export type Role = "AUCTIONEER" | "TRADER";
// Allow 'all' for meta checks, and handle single or multiple roles
export type Roles = Role | Role[] | "all";

// Extend RouteMeta from vue-router
export interface RouteMetaAuth {
	checkAuth?: boolean; // Should redirect away if logged in (e.g., login page)
	authRedirect?: string; // Where to redirect if checkAuth fails (user is logged in)
	auth?: boolean; // Does this route require authentication?
	roles?: Roles; // Which roles can access this route?
}

// Ensure vue-router augmentation includes these fields
// This might be better placed in src/router-env.d.ts

=== src/router-env.d.ts
import type { RouteMetaAuth } from "@/types/auth.d";
import type { Layout } from "@/types/theme.d";
import "vue-router";

// To ensure it is treated as a module, add at least one `export` statement
export {};

declare module "vue-router" {
	interface RouteMeta extends RouteMetaAuth { // Include auth properties
		title?: string;
		theme?: {
			layout?: Layout;
			boxed?: { enabled?: boolean };
			padded?: { enabled?: boolean };
		};
		skipPin?: boolean;
	}
}

=== src/composables/useGlobalActions.ts
import type { MessageOptions } from "naive-ui"
import type { MessageApiInjection, MessageReactive } from "naive-ui/es/message/src/MessageProvider"
import type { NotificationOptions } from "naive-ui/es/notification/src/NotificationEnvironment"
import type { NotificationApiInjection, NotificationReactive } from "naive-ui/es/notification/src/NotificationProvider"
import type { Writable } from "type-fest"

export type NotificationObject = Writable<NotificationOptions>

interface InitPayload {
	message: MessageApiInjection
	notification: NotificationApiInjection
}

let message: MessageApiInjection | null = null
let notification: NotificationApiInjection | null = null

export function useGlobalActions() {
	return {
		init: (payload: InitPayload): void => {
			message = payload.message
			notification = payload.notification
		},
		message: (content: string, options?: MessageOptions): MessageReactive | undefined => {
            if (!message) {
                console.error("Message provider not initialized. Call init() first in your main layout/provider.");
                return undefined;
            }
			return message?.create(content, options || { type: "info" })
		},
		notification: (options: NotificationOptions): NotificationReactive | undefined => {
            if (!notification) {
                console.error("Notification provider not initialized. Call init() first in your main layout/provider.");
                return undefined;
            }
			return notification?.create(options)
		}
	}
}

=== src/components/common/PasswordStrengthMeter.vue
<template>
	<div class="flex flex-col gap-4">
		<div v-if="showMeter" class="flex h-2 w-full items-center gap-2">
			<div v-for="index of maxScore" :key="index" class="h-full grow rounded-sm" :class="getStatus(index)"></div>
		</div>
		<div v-if="showList" class="flex flex-col gap-3">
			<div v-if="minLength" class="flex items-center gap-2">
				<Icon
					:size="18"
					:name="hasLength ? 'solar:check-square-bold-duotone' : 'solar:close-square-bold-duotone'"
					:class="[hasLength ? 'text-success/50' : 'text-tertiary/50']"
				/>
				<span class="text-sm">At least 8 characters</span>
			</div>
			<div v-if="lowercase" class="flex items-center gap-2">
				<Icon
					:size="18"
					:name="hasLower ? 'solar:check-square-bold-duotone' : 'solar:close-square-bold-duotone'"
					:class="[hasLower ? 'text-success/50' : 'text-tertiary/50']"
				/>
				<span class="text-sm">At least 1 lowercase character</span>
			</div>
			<div v-if="uppercase" class="flex items-center gap-2">
				<Icon
					:size="18"
					:name="hasUpper ? 'solar:check-square-bold-duotone' : 'solar:close-square-bold-duotone'"
					:class="[hasUpper ? 'text-success/50' : 'text-tertiary/50']"
				/>
				<span class="text-sm">At least 1 Uppercase character</span>
			</div>
			<div v-if="number" class="flex items-center gap-2">
				<Icon
					:size="18"
					:name="hasNumber ? 'solar:check-square-bold-duotone' : 'solar:close-square-bold-duotone'"
					:class="[hasNumber ? 'text-success/50' : 'text-tertiary/50']"
				/>
				<span class="text-sm">At least 1 number</span>
			</div>
			<div v-if="special" class="flex items-center gap-2">
				<Icon
					:size="18"
					:name="hasSpecial ? 'solar:check-square-bold-duotone' : 'solar:close-square-bold-duotone'"
					:class="[hasSpecial ? 'text-success/50' : 'text-tertiary/50']"
				/>
				<span class="text-sm">
					At least 1 special character
					<code>{{ specialString }}</code>
				</span>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import Icon from "@/components/common/Icon.vue"; // Assuming Icon component exists
import { computed, watch } from "vue";

const {
	password,
	minLength = 8,
	lowercase = true,
	uppercase = true,
	number = true,
	special = true,
	showMeter = true,
	showList = true
} = defineProps<{
	password: string;
	minLength?: number;
	lowercase?: boolean;
	uppercase?: boolean;
	number?: boolean;
	special?: string | boolean;
	showMeter?: boolean;
	showList?: boolean;
}>();

const emit = defineEmits<{
	/** 0 | 1 | 2 | 3 | 4 | 5 */
	(e: "score", value: number): void;
	/** 0% - 100% */
	(e: "strength", value: number): void;
}>();

const specialString = computed<string>(() => (typeof special === "string" ? special : "!@#$%^&*"));
const hasLength = computed<boolean>(() => password.length >= minLength);
const hasLower = computed<boolean>(() => /[a-z]/.test(password));
const hasUpper = computed<boolean>(() => /[A-Z]/.test(password));
const hasNumber = computed<boolean>(() => /\d/.test(password));
const hasSpecial = computed<boolean>(() => new RegExp(`[${specialString.value.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')}]`).test(password)); // Escape special regex chars

const maxScore = computed<number>(() => {
	let score = 0;

	if (minLength) score++;
	if (lowercase) score++;
	if (uppercase) score++;
	if (number) score++;
	if (special) score++;

	return score;
});

const score = computed<number>(() => {
	let score = 0;

	if (minLength && hasLength.value) score++;
	if (lowercase && hasLower.value) score++;
	if (uppercase && hasUpper.value) score++;
	if (number && hasNumber.value) score++;
	if (special && hasSpecial.value) score++;

	return score;
});

const strength = computed(() => maxScore.value > 0 ? (score.value / maxScore.value) * 100 : 0);

function getStatus(index: number): string {
	const indexStrength = maxScore.value > 0 ? (index / maxScore.value) * 100 : 0;

	if (indexStrength <= strength.value) {
		if (strength.value === 100) return "bg-success"; // Use Tailwind classes or CSS variables
		if (strength.value > 50) return "bg-warning";
		if (strength.value > 0) return "bg-error"; // Changed from bg-error to indicate some strength
	}
	return "bg-border"; // Use a neutral border/background color
}


watch(
	[score, strength],
	([valScore, valStrength]) => {
		emit("score", valScore);
		emit("strength", valStrength);
	},
	{ immediate: true }
);
</script>

<style scoped>
/* Add Tailwind utility classes directly in the template or define them here/globally */
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-error { background-color: var(--error-color); }
.bg-border { background-color: var(--border-color); } /* Or var(--bg-secondary-color) */

.text-success\/50 { color: rgba(var(--success-color-rgb), 0.5); }
.text-tertiary\/50 { color: rgba(var(--fg-tertiary-color-rgb), 0.5); } /* Assuming --fg-tertiary-color-rgb exists */
</style>

=== src/lang/config.ts
import type { RecursiveKeyOf } from "@/types/common"; // Assuming this type exists
import * as locales from "./locales";

export type LocaleCodes = keyof typeof locales;
export type MessageSchema = (typeof locales)[LocaleCodes];
export type I18nString = RecursiveKeyOf<MessageSchema>;

export function getI18NConf() {
	const localesEntries = Object.entries<MessageSchema>(locales);

	const messages = localesEntries.reduce((acc: { [key: string]: MessageSchema }, cur: [string, MessageSchema]) => {
		acc[cur[0]] = cur[1];
		return acc;
	}, {}) as { [key in LocaleCodes]: MessageSchema };

	// Set default locale here if needed, or rely on i18n instance creation
	const defaultLocale: LocaleCodes = 'en'; // Example default

	return {
		legacy: false, // Use Composition API
		locale: defaultLocale, // Default locale
		fallbackLocale: 'en', // Fallback locale
		messages // Your locale messages
	};
}

=== src/lang/index.ts
import type { LocaleCodes, MessageSchema } from "./config";
import { createI18n } from "vue-i18n";
import { getI18NConf } from "./config"; // Corrected import

const instance = createI18n<[MessageSchema], LocaleCodes>(getI18NConf());

export default instance; // Default export is the i18n instance

// *** Named export for the global scope ***
export const i18nGlobal = instance.global;

// --- Include ALL other previously generated files here ---
// (App.vue, main.ts, stores/*, components/*, views/*, other utils/*, other composables/*, assets/* etc.)
// ... (rest of the codebase.naiveui.txt content from the *previous* response, including Logo.vue, PasswordStrengthMeter.vue, etc.)

=== src/stores/appStore.ts
import type {
	AuctionRowElement,
	CompanyElement,
	CounterpartyCreditElement,
	MessageElement,
	SessionUserValue,
	TimeValue,
	UserElement
} from "@/types/generated"; // Assuming generated types exist
import type { DeAuctionValue, DeMatrixRoundElement } from "@/types/generated"; // Assuming generated types exist
import { defineStore } from "pinia";

// Mirroring the backend's LiveClientStore structure
interface AppState {
	auction_rows: AuctionRowElement[];
	companies: CompanyElement[];
	counterparty_credits: CounterpartyCreditElement[];
	de_auction: DeAuctionValue | null;
	session_user: SessionUserValue | null;
	time: TimeValue | null;
	users: UserElement[];
	// Add a specific place for matrix rounds if needed frequently outside de_auction
	de_matrix_rounds: DeMatrixRoundElement[];
	messages: MessageElement[]; // Assuming messages might be added incrementally
	websocketConnected: boolean;
	lastMessageTimestamp: number | null;
}

export const useAppStore = defineStore("app", {
	state: (): AppState => ({
		auction_rows: [],
		companies: [],
		counterparty_credits: [],
		de_auction: null,
		session_user: null,
		time: null,
		users: [],
		de_matrix_rounds: [],
		messages: [],
		websocketConnected: false,
		lastMessageTimestamp: null,
	}),

	actions: {
		setLiveStore(storeData: Partial<AppState>) {
			// Deep merge might be better, but direct assignment matches backend push
			// Consider using lodash merge or similar if partial updates are expected
			// within SetLiveStore, otherwise, this assumes a full state push.
			if (storeData.auction_rows !== undefined) this.auction_rows = storeData.auction_rows;
			if (storeData.companies !== undefined) this.companies = storeData.companies;
			if (storeData.counterparty_credits !== undefined) this.counterparty_credits = storeData.counterparty_credits;
			if (storeData.de_auction !== undefined) this.de_auction = storeData.de_auction;
			if (storeData.session_user !== undefined) this.session_user = storeData.session_user;
			if (storeData.time !== undefined) this.time = storeData.time;
			if (storeData.users !== undefined) this.users = storeData.users;

			// If messages/matrix rounds are part of LiveClientStore, update them here too.
			// If they are only updated via AddElements, handle them there.
			if (storeData.de_auction?.messages) {
				this.messages = storeData.de_auction.messages;
			}
			if (storeData.de_auction?.matrix_last_round) {
				// Assuming only the last round is sent in the main store push
				this.de_matrix_rounds = [storeData.de_auction.matrix_last_round];
			}

			console.log("LiveStore updated"); // Reduced verbosity
		},

		addElements(path: string, elements: any[] | null) {
			console.log(`AddElements received for path: ${path}`, elements ? `${elements.length} elements` : 'null (clear)');
			if (elements === null) {
				// Handle clearing the array
				if (path === "AuctionRowElement") this.auction_rows = [];
				else if (path === "CompanyElement") this.companies = [];
				else if (path === "CounterpartyCreditElement") this.counterparty_credits = [];
				else if (path === "MessageElement") this.messages = [];
				else if (path === "UserElement") this.users = [];
				else if (path === "DeMatrixRoundElement") this.de_matrix_rounds = [];
				// Add other paths as needed
				else console.warn(`AddElements: Unknown path to clear: ${path}`);
				return;
			}

			if (!Array.isArray(elements)) {
				console.error("AddElements: received non-array elements", elements);
				return;
			}

			// Find the target array and update/add elements
			let targetArray: any[] | undefined;
			if (path === "AuctionRowElement") targetArray = this.auction_rows;
			else if (path === "CompanyElement") targetArray = this.companies;
			else if (path === "CounterpartyCreditElement") targetArray = this.counterparty_credits;
			else if (path === "MessageElement") targetArray = this.messages;
			else if (path === "UserElement") targetArray = this.users;
			else if (path === "DeMatrixRoundElement") targetArray = this.de_matrix_rounds;
			// Add other paths as needed

			if (targetArray) {
				elements.forEach(newElement => {
					if (!newElement || typeof newElement.id === 'undefined') {
						console.warn("AddElements: Skipping element without id", newElement);
						return;
					}
					const index = targetArray.findIndex(item => item.id === newElement.id);
					if (index !== -1) {
						// Update existing element (important for reactivity)
						// Use spread or Object.assign for better reactivity handling if needed
						targetArray[index] = { ...targetArray[index], ...newElement };
					} else {
						// Add new element
						targetArray.push(newElement);
					}
				});
				// Optional: Sort arrays if needed after updates
				if (path === 'MessageElement') this.messages.sort((a, b) => a.timestamp - b.timestamp);
				if (path === 'DeMatrixRoundElement') this.de_matrix_rounds.sort((a, b) => a.round_number - b.round_number);

			} else {
				console.warn(`AddElements: Unknown path: ${path}`);
			}
		},

		setWebsocketConnected(status: boolean) {
			this.websocketConnected = status;
		},

		setLastMessageTimestamp(ts: number) {
			this.lastMessageTimestamp = ts;
		},

		resetState() {
			this.$reset();
			console.log("AppStore state reset");
		}
	},
	getters: {
		isAuctioneer: (state) => state.session_user?.isAuctioneer ?? false,
		isTrader: (state) => !state.session_user?.isAuctioneer && !!state.session_user?.role, // Assuming non-auctioneer logged in is trader
		currentSessionId: (state) => state.session_user?.session_id,
		currentPage: (state) => state.session_user?.current_page,
		currentAuctionId: (state) => state.session_user?.current_auction_id,
		// Add more specific getters as needed e.g., getTraderInfo, getAuctioneerStatus
		getDeAuctioneerStatus: (state) => state.de_auction?.auctioneer_status,
		getDeCommonStatus: (state) => state.de_auction?.common_status,
		getDeTraderInfo: (state) => state.de_auction?.trader_info,
		getDeTraderHistory: (state) => state.de_auction?.trader_history_rows ?? [],
		getDeBlotter: (state) => state.de_auction?.blotter,
		getDeMatrixLastRound: (state) => state.de_auction?.matrix_last_round,
		getMessagesForCurrentAuction: (state) => {
			// Filter messages based on current auction context if needed,
			// or assume messages in the store are already filtered by backend push
			// Ensure sorting happens here if AddElements doesn't guarantee order
			return [...state.messages].sort((a, b) => a.timestamp - b.timestamp);
		}
	}
});

// === This file includes ALL previously provided files PLUS the Blank layout directory ===

=== src/app-layouts/Blank/index.ts
export { default } from "./Blank.vue";

=== src/app-layouts/Blank/main.scss
.layout-Blank {
	.page-wrapped {
		height: 100svh;
	}
	.page-min-wrapped {
		min-height: 100svh;
	}
}

=== src/app-layouts/Blank/Blank.vue
<template>
	<div class="layout flex">
		<MainContainer class="grow">
			<slot />
		</MainContainer>
	</div>
</template>

<script lang="ts" setup>
import MainContainer from "./MainContainer.vue";
import "./main.scss"; // Import the local SCSS
</script>

<style lang="scss" scoped>
.layout {
	width: 100vw;
	height: 100vh;
	height: 100svh;
	overflow: hidden;
	perspective: 1000px;
}
</style>

=== src/app-layouts/Blank/MainContainer.vue
<template>
	<div id="app-main" class="main">
		<n-scrollbar ref="scrollbar">
			<div
				id="app-view"
				class="view"
				:class="{ boxed, 'view-padded': overridePadded === true, 'view-no-padded': overridePadded === false }"
			>
				<slot />
			</div>
		</n-scrollbar>
	</div>
</template>

<script lang="ts" setup>
import type { RouteLocationNormalizedGeneric } from "vue-router";
import { useThemeStore } from "@/stores/theme";
import { NScrollbar } from "naive-ui";
import { computed, onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";

const themeStore = useThemeStore();
const router = useRouter();
const route = useRoute();
const themeBoxed = computed<boolean>(() => themeStore.isBoxed);
const overrideBoxed = ref<boolean | undefined>(undefined);
const overridePadded = ref<boolean | undefined>(undefined);
const boxed = computed<boolean>(() => (overrideBoxed.value !== undefined ? overrideBoxed.value : themeBoxed.value));
const scrollbar = ref();

function checkThemeOverrides(currentRoute: RouteLocationNormalizedGeneric) {
	if (currentRoute.meta?.theme?.boxed?.enabled !== undefined) {
		overrideBoxed.value = currentRoute.meta.theme.boxed.enabled;
	} else {
		overrideBoxed.value = undefined;
	}

	if (currentRoute.meta?.theme?.padded?.enabled !== undefined) {
		overridePadded.value = currentRoute.meta.theme.padded.enabled;
	} else {
		overridePadded.value = undefined;
	}
}

router.beforeEach(checkThemeOverrides);

onMounted(() => {
	checkThemeOverrides(route);

	router.afterEach(() => {
		if (scrollbar?.value?.scrollTo) {
			scrollbar?.value.scrollTo({ top: 0, behavior: "smooth" });
		}
	});
});
</script>

<style lang="scss" scoped>
.main {
	width: 100%;
	position: relative;
	background-color: var(--bg-body-color);

	.view {
		padding: 0 var(--view-padding);
		width: 100%;
		margin: 0 auto;

		&.boxed {
			max-width: var(--boxed-width);
		}
		&.view-no-padded {
			padding: 0;
		}
	}
}
</style>

// --- Include all other previously generated files here ---
// (App.vue, main.ts, stores/*, components/*, views/*, other utils/*, other composables/*, assets/* etc.)
// ... (rest of the codebase.naiveui.txt content from the *previous* response, including Logo.vue, PasswordStrengthMeter.vue, config.ts etc.)

=== src/App.vue
<template>
	<Provider>
		<component
			:is="layoutComponent"
			id="app-layout"
			:class="[
				`theme-${themeName}`,
				`layout-${layoutComponentName}`,
				`route-${routeName}`,
				{ 'opacity-0': loading }
			]"
		>
			<RouterView v-slot="{ Component: RouterComponent }">
				<transition :name="`router-${routerTransition}`" mode="out-in" appear>
					<!--
						Here you can choose whether to keep each page alive by wrapping <component/> inside <keep-alive>.

						Please note:
						Keep in mind that third-party components might not handle this behavior.
					-->
					<component :is="RouterComponent" id="app-page" :key="routeName + forceRefresh" />
				</transition>
			</RouterView>
		</component>

		<SplashScreen :show="loading" />
		<SearchDialog v-if="isLogged" />
		<LayoutSettings />
	</Provider>
</template>

<script lang="ts" setup>
import type { Layout, RouterTransition, ThemeNameEnum } from "@/types/theme.d";
import type { Component } from "vue";
import type { RouteLocationNormalized } from "vue-router";
import Blank from "@/app-layouts/Blank"; // This should now resolve correctly
import Provider from "@/app-layouts/common/Provider.vue"; // Ensure Provider is correctly imported/created
import SplashScreen from "@/app-layouts/common/SplashScreen.vue"; // Ensure SplashScreen is correctly imported/created
import HorizontalNav from "@/app-layouts/HorizontalNav"; // Ensure HorizontalNav is correctly imported/created
import VerticalNav from "@/app-layouts/VerticalNav"; // Ensure VerticalNav is correctly imported/created
import LayoutSettings from "@/components/common/LayoutSettings.vue"; // Ensure LayoutSettings is correctly imported/created
import SearchDialog from "@/components/common/SearchDialog.vue"; // Ensure SearchDialog is correctly imported/created
import { useAuthStore } from "@/stores/auth";
import { useMainStore } from "@/stores/main";
import { useThemeStore } from "@/stores/theme";
import { computed, onMounted, ref, watch } from "vue";
import { useRoute, useRouter } from "vue-router";

const router = useRouter();
const route = useRoute();
const loading = ref(true);

const layoutComponents = {
	VerticalNav,
	HorizontalNav,
	Blank
};

const themeStore = useThemeStore();
const mainStore = useMainStore();
const authStore = useAuthStore();

const routeName = computed<string>(() => route?.name?.toString() || "");
const forceRefresh = computed<number>(() => mainStore.forceRefresh);
const forceLayout = ref<Layout | null>(null);
const layout = computed<Layout>(() => themeStore.layout);
const layoutComponentName = computed<Layout>(() => forceLayout.value || layout.value);
const layoutComponent = computed<Component>(() => layoutComponents[layoutComponentName.value]);
const routerTransition = computed<RouterTransition>(() => themeStore.routerTransition);
const themeName = computed<ThemeNameEnum>(() => themeStore.themeName);
const isLogged = computed(() => authStore.isLogged);

function checkThemeOverrides(currentRoute: RouteLocationNormalized) {
	if (currentRoute.meta?.theme?.layout !== undefined) {
		forceLayout.value = currentRoute.meta.theme.layout;
	} else {
		forceLayout.value = null;
	}
}

watch(layoutComponentName, () => {
	// This might be too early if theme init hasn't finished
	// loading.value = false;
});

router.afterEach(currentRoute => {
	checkThemeOverrides(currentRoute);
});

onMounted(() => {
	checkThemeOverrides(route);

	// *** Initialize theme watchers and CSS vars ***
	themeStore.initTheme();

	// Delay setting loading to false until after theme init might have run
	setTimeout(() => {
		loading.value = false;
	}, 50); // Adjust delay if needed
});
</script>


// === codebase.naiveui.full.txt ===

// --- Root Files ---
=== src/App.vue
<template>
	<Provider>
		<component
			:is="layoutComponent"
			id="app-layout"
			:class="[
				`theme-${themeName}`,
				`layout-${layoutComponentName}`,
				`route-${routeName}`,
				{ 'opacity-0': loading }
			]"
		>
			<RouterView v-slot="{ Component: RouterComponent }">
				<transition :name="`router-${routerTransition}`" mode="out-in" appear>
					<!--
						Here you can choose whether to keep each page alive by wrapping <component/> inside <keep-alive>.

						Please note:
						Keep in mind that third-party components might not handle this behavior.
					-->
					<component :is="RouterComponent" id="app-page" :key="routeName + forceRefresh" />
				</transition>
			</RouterView>
		</component>

		<SplashScreen :show="loading" />
		<SearchDialog v-if="isLogged" />
		<LayoutSettings />
	</Provider>
</template>

<script lang="ts" setup>
import type { Layout, RouterTransition, ThemeNameEnum } from "@/types/theme.d";
import type { Component } from "vue";
import type { RouteLocationNormalized } from "vue-router";
import Blank from "@/app-layouts/Blank"; // This should now resolve correctly
import Provider from "@/app-layouts/common/Provider.vue";
import SplashScreen from "@/app-layouts/common/SplashScreen.vue";
import HorizontalNav from "@/app-layouts/HorizontalNav";
import VerticalNav from "@/app-layouts/VerticalNav";
import LayoutSettings from "@/components/common/LayoutSettings.vue";
import SearchDialog from "@/components/common/SearchDialog.vue";
import { useAuthStore } from "@/stores/auth";
import { useMainStore } from "@/stores/main";
import { useThemeStore } from "@/stores/theme";
import { computed, onMounted, ref, watch } from "vue";
import { useRoute, useRouter } from "vue-router";

const router = useRouter();
const route = useRoute();
const loading = ref(true);

const layoutComponents = {
	VerticalNav,
	HorizontalNav,
	Blank
};

const themeStore = useThemeStore();
const mainStore = useMainStore();
const authStore = useAuthStore();

const routeName = computed<string>(() => route?.name?.toString() || "");
const forceRefresh = computed<number>(() => mainStore.forceRefresh);
const forceLayout = ref<Layout | null>(null);
const layout = computed<Layout>(() => themeStore.layout);
const layoutComponentName = computed<Layout>(() => forceLayout.value || layout.value);
const layoutComponent = computed<Component>(() => layoutComponents[layoutComponentName.value]);
const routerTransition = computed<RouterTransition>(() => themeStore.routerTransition);
const themeName = computed<ThemeNameEnum>(() => themeStore.themeName);
const isLogged = computed(() => authStore.isLogged);

function checkThemeOverrides(currentRoute: RouteLocationNormalized) {
	if (currentRoute.meta?.theme?.layout !== undefined) {
		forceLayout.value = currentRoute.meta.theme.layout;
	} else {
		forceLayout.value = null;
	}
}

watch(layoutComponentName, () => {
	// This might be too early if theme init hasn't finished
	// loading.value = false;
});

router.afterEach(currentRoute => {
	checkThemeOverrides(currentRoute);
});

onMounted(() => {
	checkThemeOverrides(route);

	// *** Initialize theme watchers and CSS vars ***
	themeStore.initTheme();

	// Delay setting loading to false until after theme init might have run
	setTimeout(() => {
		loading.value = false;
	}, 50); // Adjust delay if needed
});
</script>

=== src/design-tokens.json
{
	"borderRadius": {
		"default": "8px",
		"small": "4px"
	},
	"lineHeight": {
		"default": "1.25"
	},
	"fontSize": {
		"default": "16px",
		"cardTitle": "18px"
	},
	"fontWeight": {
		"default": "400",
		"strong": "600",
		"cardTitle": "600"
	},
	"fontFamily": {
		"default": "'Public Sans', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'",
		"display": "'Lexend', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'",
		"mono": "'JetBrains Mono', SFMono-Regular, Menlo, Consolas, Courier, monospace"
	},
	"typography": {
		"h1": {
			"fontFamily": "{fontFamily.display}",
			"fontSize": "30px",
			"fontWeight": "700",
			"lineHeight": "38"
		},
		"h2": {
			"fontFamily": "{fontFamily.display}",
			"fontSize": "26px",
			"fontWeight": "700",
			"lineHeight": "33"
		},
		"h3": {
			"fontFamily": "{fontFamily.display}",
			"fontSize": "22px",
			"fontWeight": "700",
			"lineHeight": "28"
		},
		"h4": {
			"fontFamily": "{fontFamily.display}",
			"fontSize": "18px",
			"fontWeight": "500",
			"lineHeight": "23"
		},
		"h5": {
			"fontFamily": "{fontFamily.display}",
			"fontSize": "14px",
			"fontWeight": "700",
			"lineHeight": "18"
		},
		"h6": {
			"fontFamily": "{fontFamily.default}",
			"fontSize": "12px",
			"fontWeight": "500",
			"lineHeight": "15"
		},
		"p": {
			"fontFamily": "{fontFamily.default}",
			"fontSize": "{fontSize.default}",
			"lineHeight": "20"
		}
	},
	"colors": {
		"light": {
			"sidebarBackground": "rgb(255, 255, 255)",
			"bodyBackground": "rgb(245, 247, 249)",
			"background": "rgb(255, 255, 255)",
			"backgroundSecondary": "rgb(249, 250, 251)",
			"text": "rgb(0, 0, 0)",
			"textSecondary": "rgb(73, 84, 101)",
			"textTertiary": "rgb(114, 115, 115)",
			"border": "rgb(226, 230, 233)",
			"hover": "rgba(221, 224, 225, 0.5)",
			"primary": "rgb(0, 179, 122)",
			"info": "rgb(97, 102, 255)",
			"success": "rgb(0, 179, 122)",
			"warning": "rgb(255, 183, 0)",
			"error": "rgb(255, 0, 85)",
			"extra1": "rgb(97, 102, 255)",
			"extra2": "rgb(255, 97, 200)",
			"extra3": "rgb(255, 183, 0)",
			"extra4": "rgb(255, 0, 85)"
		},
		"dark": {
			"sidebarBackground": "rgb(29, 31, 37)",
			"bodyBackground": "rgb(20, 22, 26)",
			"background": "rgb(38, 39, 44)",
			"backgroundSecondary": "rgb(29, 31, 37)",
			"text": "rgb(255, 255, 255)",
			"textSecondary": "rgb(172, 181, 190)",
			"textTertiary": "rgb(172, 181, 190)",
			"border": "rgb(70, 71, 79)",
			"hover": "rgba(59, 61, 68, 0.5)",
			"primary": "rgb(0, 224, 153)",
			"info": "rgb(97, 102, 255)",
			"success": "rgb(0, 224, 153)",
			"warning": "rgb(255, 183, 0)",
			"error": "rgb(255, 0, 85)",
			"extra1": "rgb(97, 102, 255)",
			"extra2": "rgb(255, 97, 200)",
			"extra3": "rgb(255, 183, 0)",
			"extra4": "rgb(255, 0, 85)"
		}
	}
}

=== src/global.d.ts
export {}

declare global {
	interface Document {
		startViewTransition?: (callback: () => Promise<void> | void) => ViewTransition
	}

	interface ViewTransition {
		readonly ready: Promise<undefined>
		readonly finished: Promise<undefined>
		readonly updateCallbackDone: Promise<undefined>
		skipTransition: () => void
	}

	interface CSSStyleDeclaration {
		viewTransitionName: string
	}
}

=== src/i18n.d.ts
import type { MessageSchema } from "@/lang/config"
import type { RecursiveKeyOf } from "@/types/common"

// To ensure it is treated as a module, add at least one `export` statement
export {}

declare module "@vue/runtime-core" {
	interface ComponentCustomProperties {
		$t: {
			// Firma 1: Traduzione semplice
			(key: RecursiveKeyOf<MessageSchema>): string

			// Firma 2: Traduzione con numero plurale
			(key: RecursiveKeyOf<MessageSchema>, plural: number): string

			// Firma 3: Traduzione con numero plurale e opzioni aggiuntive
			(key: RecursiveKeyOf<MessageSchema>, plural: number, options: TranslateOptions<Locales>): string

			// Firma 4: Traduzione con messaggio predefinito
			(key: RecursiveKeyOf<MessageSchema>, defaultMsg: string): string

			// Firma 5: Traduzione con messaggio predefinito e opzioni aggiuntive
			(key: RecursiveKeyOf<MessageSchema>, defaultMsg: string, options: TranslateOptions<Locales>): string

			// Firma 6: Traduzione con interpolazione di lista
			(key: RecursiveKeyOf<MessageSchema>, list: unknown[]): string

			// Firma 7: Traduzione con interpolazione di lista e numero plurale
			(key: RecursiveKeyOf<MessageSchema>, list: unknown[], plural: number): string

			// Firma 8: Traduzione con interpolazione di lista e messaggio predefinito
			(key: RecursiveKeyOf<MessageSchema>, list: unknown[], defaultMsg: string): string

			// Firma 9: Traduzione con interpolazione di lista e opzioni aggiuntive
			(key: RecursiveKeyOf<MessageSchema>, list: unknown[], options: TranslateOptions<Locales>): string

			// Firma 10: Traduzione con interpolazione nominale
			(key: RecursiveKeyOf<MessageSchema>, named: NamedValue): string

			// Firma 11: Traduzione con interpolazione nominale e numero plurale
			(key: RecursiveKeyOf<MessageSchema>, named: NamedValue, plural: number): string

			// Firma 12: Traduzione con interpolazione nominale e messaggio predefinito
			(key: RecursiveKeyOf<MessageSchema>, named: NamedValue, defaultMsg: string): string

			// Firma 13: Traduzione con interpolazione nominale e opzioni aggiuntive
			(key: RecursiveKeyOf<MessageSchema>, named: NamedValue, options: TranslateOptions<Locales>): string
		}
	}
}

declare module "vue-i18n" {
	export interface DefineLocaleMessage extends MessageSchema {}
}

=== src/main.ts
import App from "@/App.vue";
import i18n from "@/lang";
import router from "@/router";
import VueGoogleMaps from "@fawmi/vue-google-maps";
import { createPinia } from "pinia";
import { createPersistedState } from "pinia-plugin-persistedstate";
import { createApp } from "vue";
import VueApexCharts from "vue3-apexcharts";

import VueVectorMap from "vuevectormap";
import "jsvectormap/src/scss/jsvectormap.scss";
import "jsvectormap/dist/maps/world-merc";

import "@/assets/scss/index.scss"; // Import global styles
import "./tailwind.css"; // Import Tailwind

const meta = document.createElement("meta");
meta.name = "naive-ui-style";
document.head.appendChild(meta);

const pinia = createPinia();
pinia.use(
	createPersistedState({
		key: id => `__persisted__${id}`
	})
);

const app = createApp(App);
app.use(pinia);
app.use(i18n);
app.use(router);
app.use(VueApexCharts);
// app.use(VueGoogleMaps, { // Commented out as it might require API key setup
// 	load: {}
// });
app.use(VueVectorMap);

app.mount("#app");

=== src/router-env.d.ts
import type { RouteMetaAuth } from "@/types/auth.d";
import type { Layout } from "@/types/theme.d";
import "vue-router";

// To ensure it is treated as a module, add at least one `export` statement
export {};

declare module "vue-router" {
	interface RouteMeta extends RouteMetaAuth { // Include auth properties
		title?: string;
		theme?: {
			layout?: Layout;
			boxed?: { enabled?: boolean };
			padded?: { enabled?: boolean };
		};
		skipPin?: boolean;
	}
}

=== src/tailwind.css
@import "tailwindcss";
@config '../tailwind.config.js';

@custom-variant dark (&:where(.theme-dark, .theme-dark *));

@theme inline {
	--font-*: initial;
	--font-sans: var(--font-family), sans-serif;
	--font-serif: var(--font-family-display), serif;
	--font-display: var(--font-family-display), serif;
	--font-mono: var(--font-family-mono), monospace;

	--color-primary: var(--primary-color);
	--color-success: var(--success-color);
	--color-error: var(--error-color);
	--color-warning: var(--warning-color);
	--color-info: var(--info-color);
	--color-border: var(--border-color);
	--color-hover: var(--hover-color);

	--color-extra-1: var(--extra1-color);
	--color-extra-2: var(--extra2-color);
	--color-extra-3: var(--extra3-color);
	--color-extra-4: var(--extra4-color);

	--border-color-default: var(--border-color);

	--background-color-default: var(--bg-default-color);
	--background-color-secondary: var(--bg-secondary-color);
	--background-color-body: var(--bg-body-color);
	--background-color-sidebar: var(--bg-sidebar-color);

	--text-color-default: var(--fg-default-color);
	--text-color-secondary: var(--fg-secondary-color);
	--text-color-tertiary: var(--fg-tertiary-color);

	--breakpoint-xs: 460px;

	--animate-fade: fade 0.3s forwards;
	@keyframes fade {
		from {
			opacity: 0;
		}
		to {
			opacity: 1;
		}
	}
}

@layer base {
	/*
	The default border color has changed to `currentColor` in Tailwind CSS v4,
	so we've added these compatibility styles to make sure everything still
	looks the same as it did with Tailwind CSS v3.

	If we ever want to remove these styles, we need to add an explicit border
	color utility to any element that depends on these defaults.
	*/
	*,
	::after,
	::before,
	::backdrop,
	::file-selector-button {
		border-color: var(--color-gray-200, currentColor);
	}

	html,
	body {
		text-size-adjust: 100%;
		-webkit-tap-highlight-color: transparent;
		-webkit-touch-callout: none;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
		text-rendering: optimizeLegibility;
		line-height: var(--line-height);
		font-size: var(--font-size);
		text-wrap: pretty;

		margin: 0;
		padding: 0;
		box-sizing: border-box;

		width: 100vw;
		height: 100vh;
		height: 100svh;
		overflow: hidden;

		&:focus {
			outline: none;
		}
	}

	[v-cloak] {
		display: none !important;
	}

	::view-transition-old(root),
	::view-transition-new(root) {
		animation: none;
		mix-blend-mode: normal;
	}
	::view-transition-old(root) {
		z-index: 1;
	}
	::view-transition-new(root) {
		z-index: 99999;
	}

	::selection {
		background-color: rgba(var(--primary-color-rgb) / 0.2);
	}

	#app {
		width: 100vw;
		height: 100vh;
		overflow: auto;
	}

	svg {
		touch-action: initial !important;
	}

	input {
		accent-color: var(--primary-color);
	}

	p {
		color: var(--fg-secondary-color);
	}

	code,
	kbd,
	samp,
	pre {
		font-family: var(--font-family-mono);
	}

	code {
		padding: 1px 6px;
		border-radius: var(--border-radius-small);
		background-color: rgba(var(--hover-color-rgb) / 0.4);
		font-size: 13px;
	}
	pre {
		border-radius: var(--border-radius-small);

		code {
			padding: 12px;
			display: block;
			overflow-x: auto;
		}
	}

	a {
		text-decoration: underline;
		text-decoration-color: var(--primary-color);
		color: var(--primary-color);
	}

	blockquote {
		display: block;
		padding-left: 1em;
		border-left: 4px solid var(--border-color);
	}

	dl {
		dt {
			font-weight: bold;
			margin-bottom: 2px;
		}

		& > dd:not(:last-child) {
			margin-bottom: 10px;
		}
	}

	ul {
		display: block;
		list-style-type: disc;
		padding-left: 20px;
		line-height: 1.6;

		ul {
			list-style-type: circle;
			margin-top: 3px;
			margin-bottom: 6px;
		}
	}

	ol {
		display: block;
		list-style-type: decimal;
		padding-left: 20px;
		line-height: 1.6;

		ol {
			list-style-type: decimal;
			margin-top: 3px;
			margin-bottom: 6px;
		}
	}

	mark {
		padding: 2px 0px;
		border-radius: var(--border-radius-small);
		background-color: rgba(var(--primary-color-rgb) / 0.3);
		color: var(--fg-default-color);
	}
}

=== src/vite-env.d.ts
/// <reference types="vite/client" />

declare module "*.vue" {
	import type { DefineComponent } from "vue";

	const component: DefineComponent<Record<string, unknown>, Record<string, unknown>, any>;
	export default component;
}

declare module "*.svg" {
	import type { DefineComponent } from "vue";

	const component: DefineComponent;
	export default component;
}

// Add declaration for vuevectormap if not automatically picked up
declare module 'vuevectormap';

// Add declaration for vue-google-maps if needed
declare module '@fawmi/vue-google-maps';


// --- Include all other files from previous responses ---
// (stores/*, services/*, router/*, lang/*, theme/*, types/*, utils/*, composables/*, components/*, views/*, app-layouts/*, assets/*)
// ... (Make sure all previously provided files are included here)


