import type { FormType } from "@/components/auth/types.d";
import { Layout, PageName } from "@/types/generated"; // Assuming PageName is generated
import { authCheck } from "@/utils/auth"; // Corrected import path
import { createRouter, createWebHistory } from "vue-router";

// Helper to map backend PageName to frontend route names/paths
// Adjust paths/names as needed to match your preferred frontend routing structure
const pageNameToRoute = {
	[PageName.LOGIN_PAGE]: { name: "Login", path: "/login" },
	[PageName.HOME_PAGE]: { name: "Home", path: "/" }, // Map to dashboard or appropriate home
	[PageName.USER_PAGE]: { name: "UserManagement", path: "/admin/users" },
	[PageName.COMPANY_PAGE]: { name: "CompanyManagement", path: "/admin/companies" }, // Assuming a dedicated page
	[PageName.SESSION_PAGE]: { name: "SessionManagement", path: "/admin/sessions" },
	[PageName.DE_SETUP_PAGE]: { name: "DeAuctionSetup", path: "/de/setup" }, // Can be combined with edit using :id?
	[PageName.DE_AUCTIONEER_PAGE]: { name: "DeAuctioneer", path: "/de/auction/:id/auctioneer" },
	[PageName.DE_TRADER_PAGE]: { name: "DeTrader", path: "/de/auction/:id/trader" },
	// Add mappings for other PageNames (CREDITOR, BH, MR, TE, TO) if they exist and have UIs
	[PageName.CREDITOR_AUCTIONEER_PAGE]: { name: "CreditorAuctioneer", path: "/creditor/auction/:id/auctioneer" },
	[PageName.CREDITOR_TRADER_PAGE]: { name: "CreditorTrader", path: "/creditor/auction/:id/trader" },
	// ... add others like BH_AUCTIONEER_PAGE etc. if needed
};

const router = createRouter({
	history: createWebHistory(import.meta.env.BASE_URL),
	routes: [
		// Redirect root to a default dashboard or home based on role later
		{
			path: "/",
			name: pageNameToRoute[PageName.HOME_PAGE].name, // Use mapping
			component: () => import("@/views/Dashboard/Analytics.vue"), // Use created placeholder
			meta: { title: "Dashboard", auth: true, roles: "all" },
		},

		// --- Authentication ---
		{
			path: pageNameToRoute[PageName.LOGIN_PAGE].path,
			name: pageNameToRoute[PageName.LOGIN_PAGE].name,
			component: () => import("@/views/Auth/Login.vue"), // Use created placeholder
			meta: {
				title: "Login",
				theme: { layout: Layout.Blank, boxed: { enabled: false }, padded: { enabled: false } },
				checkAuth: true, // Redirect if already logged in
				skipPin: true,
			},
		},
		// Add Register and ForgotPassword if needed, mapping them appropriately
		{
			path: "/register", // No direct PageName mapping, assuming standard flow
			name: "Register",
			component: () => import("@/views/Auth/Login.vue"), // Reuse Login view
			props: { formType: "signup" as FormType },
			meta: {
				title: "Register",
				theme: { layout: Layout.Blank, boxed: { enabled: false }, padded: { enabled: false } },
				checkAuth: true,
				skipPin: true,
			},
		},
		{
			path: "/forgot-password", // No direct PageName mapping
			name: "ForgotPassword",
			component: () => import("@/views/Auth/Login.vue"), // Reuse Login view
			props: { formType: "forgotpassword" as FormType },
			meta: {
				title: "Forgot Password",
				theme: { layout: Layout.Blank, boxed: { enabled: false }, padded: { enabled: false } },
				checkAuth: true,
				skipPin: true,
			},
		},
		{
			path: "/logout",
			name: "Logout",
			redirect: pageNameToRoute[PageName.LOGIN_PAGE].path, // Redirect to login after logout logic
		},

		// --- Admin / Auctioneer Specific ---
		{
			path: "/admin", // Group admin routes
			redirect: "/admin/users",
			meta: { auth: true, roles: ["AUCTIONEER"] }, // Role guard
			children: [
				{
					path: "users",
					name: pageNameToRoute[PageName.USER_PAGE].name,
					component: () => import("@/views/Admin/UserManagement.vue"), // Use created view
					meta: { title: "User Management" },
				},
				{
					path: "companies",
					name: pageNameToRoute[PageName.COMPANY_PAGE].name,
					component: () => import("@/views/Admin/CompanyManagement.vue"), // Use created view
					meta: { title: "Company Management" },
				},
				{
					path: "sessions",
					name: pageNameToRoute[PageName.SESSION_PAGE].name,
					component: () => import("@/views/Admin/SessionManagement.vue"), // Use created view
					meta: { title: "Session Management" },
				},
			],
		},

		// --- DE Auction ---
		{
			path: "/de", // Group DE routes
			redirect: pageNameToRoute[PageName.HOME_PAGE].path, // Redirect to home/dashboard
			meta: { auth: true, roles: "all" }, // Allow both roles, specific views handle finer control
			children: [
				{
					path: "setup", // Route for creating a new auction
					name: pageNameToRoute[PageName.DE_SETUP_PAGE].name + "Create", // Differentiate create/edit
					component: () => import("@/views/Auction/DeAuctionSetup.vue"), // Use created view
					meta: { title: "Create DE Auction", roles: ["AUCTIONEER"] },
				},
				{
					path: "setup/:id", // Route for editing an existing auction
					name: pageNameToRoute[PageName.DE_SETUP_PAGE].name + "Edit",
					component: () => import("@/views/Auction/DeAuctionSetup.vue"), // Reuse the setup view
					props: true, // Pass route params as props
					meta: { title: "Edit DE Auction", roles: ["AUCTIONEER"] },
				},
				{
					path: "auction/:id/auctioneer",
					name: pageNameToRoute[PageName.DE_AUCTIONEER_PAGE].name,
					component: () => import("@/views/Auction/DeAuctioneer.vue"), // Use created view
					props: true,
					meta: { title: "DE Auctioneer View", roles: ["AUCTIONEER"] },
				},
				{
					path: "auction/:id/trader",
					name: pageNameToRoute[PageName.DE_TRADER_PAGE].name,
					component: () => import("@/views/Auction/DeTrader.vue"), // Use created view
					props: true,
					meta: { title: "DE Trader View", roles: ["TRADER"] },
				},
			],
		},

		// --- Add routes for other auction types (Creditor, BH, MR, TE, TO) similarly ---
		// Example for Creditor
		{
			path: "/creditor",
			redirect: pageNameToRoute[PageName.HOME_PAGE].path, // Or a specific creditor dashboard/list
			meta: { auth: true, roles: "all" },
			children: [
				// Add setup/edit routes if applicable
				{
					path: "auction/:id/auctioneer",
					name: pageNameToRoute[PageName.CREDITOR_AUCTIONEER_PAGE].name,
					component: () => import("@/views/Auction/CreditorAuctioneer.vue"), // Use created placeholder
					props: true,
					meta: { title: "Creditor Auctioneer View", roles: ["AUCTIONEER"] },
				},
				{
					path: "auction/:id/trader",
					name: pageNameToRoute[PageName.CREDITOR_TRADER_PAGE].name,
					component: () => import("@/views/Auction/CreditorTrader.vue"), // Use created placeholder
					props: true,
					meta: { title: "Creditor Trader View", roles: ["TRADER"] },
				},
			]
		},


		// --- Catchall ---
		{
			path: "/:pathMatch(.*)*",
			name: "NotFound",
			component: () => import("@/views/NotFound.vue"),
			meta: {
				theme: { layout: Layout.Blank, boxed: { enabled: false }, padded: { enabled: false } },
				skipPin: true,
			},
		},
	],
});

router.beforeEach(route => {
	return authCheck(route); // Use Pinx's auth check utility
});

export default router;