<template>
	<div class="page-auth">
		<!-- Pinx Settings component might be removed if not needed -->
		<!-- <Settings v-if="!isLogged" v-model:align="align" v-model:active-color="activeColor" /> -->

		<div class="wrapper flex justify-center">
			<!-- Optional Image Box -->
			<div v-if="align === 'right'" class="image-box basis-2/3" />

			<div class="form-box flex basis-1/3 items-center justify-center" :class="{ centered: align === 'center' }">
				<!-- Use Pinx's AuthForm, passing our specific type -->
				<!-- Removed @change-view as we use router now -->
				<AuthForm :type="type" :use-only-router="true" />
			</div>

			<div v-if="align === 'left'" class="image-box basis-2/3" />
		</div>
	</div>
</template>

<script lang="ts" setup>
import type { Align } from "@/components/auth/Settings.vue"; // If using settings
import type { FormType } from "@/components/auth/types.d";
import AuthForm from "@/components/auth/AuthForm.vue";
// import Settings from "@/components/auth/Settings.vue"; // Optional
import { useAuthStore } from "@/stores/auth";
import { computed, onBeforeMount, ref } from "vue";
import { useRoute } from "vue-router";
import "./main.scss"; // Import Pinx's auth styles

const props = defineProps<{
	formType?: FormType;
}>();

const route = useRoute();
const align = ref<Align>("left"); // Default alignment from Pinx
const activeColor = ref(""); // Default color from Pinx
const type = ref<FormType>(props.formType || "signin"); // Default to signin
const authStore = useAuthStore();
const isLogged = computed(() => authStore.isLogged);

onBeforeMount(() => {
	// Check route query params on initial load (like Pinx might do)
	// Or determine type based on route name if using separate routes
	if (route.name === 'Register') {
		type.value = 'signup';
	} else if (route.name === 'ForgotPassword') {
		type.value = 'forgotpassword';
	} else {
		type.value = 'signin';
	}

	// Handle query params if still needed for specific flows
	if (route.query.step) {
		const step = route.query.step as FormType;
		if (["signin", "signup", "forgotpassword"].includes(step)) {
			type.value = step;
		}
	}
});
</script>

<!-- Pinx's auth styles are imported via main.scss -->