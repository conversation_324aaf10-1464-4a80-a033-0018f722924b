import {
	AutopilotMode,
	DeAuctioneerState,
	type DeAuctioneerStatusValue,
	DeFlowControlType,
	DeTimeState,
	OrderType
} from "au25-connector"

export const demo_controls: { [key in DeFlowControlType]: boolean } = {
	//ENGAGE_AUTO_PILOT: false,
	//DISENGAGE_AUTO_PILOT: false,
	HEARTBEAT: false,
	SET_STARTING_PRICE: false,
	ANNOUNCE_STARTING_PRICE: false,
	START_AUCTION: false,
	CLOSE_ROUND: false,
	REOPEN_ROUND: false,
	NEXT_ROUND: false,
	AWARD_AUCTION: false
}

export function createDemo__DeAuctioneerStatusValue(): DeAuctioneerStatusValue {
	return {
		//info_level: DeAuctioneerInfoLevel.NORMAL,
		announced: false,
		auctioneer_state: DeAuctioneerState.STARTING_PRICE_ANNOUNCED,
		auctioneer_state_text: "auctioneer state string",
		autopilot: AutopilotMode.DISENGAGED,
		awardable: false,
		controls: demo_controls,
		excess_side: OrderType.SELL,
		excess_level: "5+",
		price_has_overshot: false,
		round_open_min_secs: 10,
		//round_state: DeRoundState.GREEN,
		starting_price: "100.000",
		time_state: DeTimeState.AUCTION_HAS_STARTED
	}
}
