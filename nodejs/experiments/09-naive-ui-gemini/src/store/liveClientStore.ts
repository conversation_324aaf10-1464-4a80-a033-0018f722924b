import {acceptHMRUpdate, defineStore} from "pinia"
import {AuUserRole, type LiveClientS<PERSON>} from "au25-connector";
import {PageName} from "au25-connector";
import {duration_ms_str} from "../utils";

export const piniaInternalProps = [
    "/$id",
    "/$state",
    "/$patch",
    "/$reset",
    "/$subscribe",
    "/$onAction",
    "/$dispose",
    "/$getters",
    "/$actions",
    "/_p",
    "/_s",
    "/_getters",
    "/_isOptionsAPI",
    "/_hotUpdate",
    "/_hmrPayload"
]

export const liveClientStore =
    defineStore('LiveClientStore', {
        state: (): LiveClientStore => ({
            auction_rows: [],
            companies: [],
            counterparty_credits: [],
            de_auction: null,
            seconds_since_last_message_received: 0,
            session_user: null,
            time: null,
            users: []
        }),
        getters: {
            isLoggedIn: state => state.session_user?.role != null,
            isAuctioneerOrAdmin: state => state.session_user != null, //isAuctioneerOrAdmin(state.session_user),
            isTrader:state => state.session_user?.role == AuUserRole.TRADER,
            username: state => state.session_user?.username ?? '',
            companyId: state => state.session_user?.company_id ?? '',
            companyShortName: state => state.session_user?.company_shortname ?? '',
            companyLongName: state => state.session_user?.company_longname ?? '',
            currentPage: state => state.session_user?.current_page ?? PageName.LOGIN_PAGE,
            currentAuctionId: state => state.session_user?.current_auction_id ?? null,
            currentAuctionName: state => state.de_auction?.settings?.auction_name ?? '',
            sessionId: state => state.session_user?.session_id ?? ''
        },
        actions: {
            apply_patch(new_store: Partial<LiveClientStore>) {
                const start = performance.now()
                console.log(new_store)
                //this.$patch(new_store)
                console.log(`LiveClientStore patching with $patch took: : ${duration_ms_str(start)}`)
            },

            setPage(page: PageName, id?: string) {
                if (this.session_user) {
                    this.session_user.current_page = page
                    if (id && [PageName.DE_AUCTIONEER_PAGE, PageName.DE_TRADER_PAGE].includes(page)) {
                        this.session_user.current_auction_id = id
                    }
                }
            },

            logout(){
                alert("logout")
            }

        }
    })

if (import.meta.hot) {
    import.meta.hot.accept(acceptHMRUpdate(liveClientStore, import.meta.hot))
    // import.meta.hot.accept(() => {
    //     // not sure we need to do anything
    // })
}
