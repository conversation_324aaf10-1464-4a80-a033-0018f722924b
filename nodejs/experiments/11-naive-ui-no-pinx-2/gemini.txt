=== src/components/layout/NavBar.vue
<template>
  <n-space justify="start" align="center" style="padding: 5px 0;">
    <!-- Always Visible -->
    <n-button text @click="setPage(PageName.LOGIN_PAGE)" :type="isActive(PageName.LOGIN_PAGE)">
      Login
    </n-button>

    <!-- Logged In Users -->
    <template v-if="authStore.isLoggedIn">
      <n-divider vertical />
      <n-button text @click="setPage(PageName.HOME_PAGE)" :type="isActive(PageName.HOME_PAGE)">
        Home
      </n-button>
    </template>

    <!-- Auctioneer Only -->
    <template v-if="authStore.isAuctioneer">
       <n-divider vertical />
       <n-button text @click="setPage(PageName.USER_PAGE)" :type="isActive(PageName.USER_PAGE)">
         Users/Companies
       </n-button>
       <n-divider vertical />
       <n-button text @click="setPage(PageName.SESSION_PAGE)" :type="isActive(PageName.SESSION_PAGE)">
         Sessions
       </n-button>
       <n-divider vertical />
       <n-button text @click="setPage(PageName.DE_SETUP_PAGE)" :type="isActive(PageName.DE_SETUP_PAGE)" :disabled="!canAccessSetup">
         DE Setup
       </n-button>
       <n-divider vertical />
       <n-button text @click="setPage(PageName.DE_AUCTIONEER_PAGE)" :type="isActive(PageName.DE_AUCTIONEER_PAGE)" :disabled="!canAccessAuctioneerPage">
         DE Auctioneer
       </n-button>
       <!-- Add buttons for other auctioneer pages (BH, MR, TE, TO, CREDITOR) -->
    </template>

    <!-- Trader Only -->
     <template v-if="authStore.isTrader">
       <n-divider vertical />
       <n-button text @click="setPage(PageName.DE_TRADER_PAGE)" :type="isActive(PageName.DE_TRADER_PAGE)" :disabled="!canAccessTraderPage">
         DE Trader
       </n-button>
        <!-- Add buttons for other trader pages (BH, MR, TE, TO, CREDITOR) -->
     </template>

  </n-space>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { NSpace, NButton, NDivider } from 'naive-ui';
import { useAuthStore } from '@/stores/auth';
import { useWebsocketStore } from '@/stores/websocket';
import { PageName } from 'au25-connector';
import { page_set_command } from 'au25-connector';
import type { PageSetCommand } from 'au25-connector';

const authStore = useAuthStore();
const websocketStore = useWebsocketStore();

function setPage(page: PageName) {
  // Only send command if not already on the target page
  if (authStore.currentPage !== page) {
    console.log(`NavBar: Requesting page change to ${page}`);
    const commandPayload: PageSetCommand = { page };
    Connector.publish(page_set_command(commandPayload));
  } else {
    console.log(`NavBar: Already on page ${page}`);
  }
}

function isActive(page: PageName): 'primary' | 'default' {
    return authStore.currentPage === page ? 'primary' : 'default';
}

// Add computed properties for disabling buttons based on context if needed
const canAccessSetup = computed(() => {
    // Can access setup if auctioneer and maybe if no auction is selected or current auction is editable?
    // For now, just check if auctioneer.
    return authStore.isAuctioneer;
});

const canAccessAuctioneerPage = computed(() => {
    // Can access auctioneer page only if an auction is selected
    return authStore.isAuctioneer && !!authStore.currentAuctionId;
});

const canAccessTraderPage = computed(() => {
    // Can access trader page only if an auction is selected
    return authStore.isTrader && !!authStore.currentAuctionId;
});


</script>

<style scoped>
.n-button {
  font-weight: 500;
}
.n-divider {
    margin: 0 8px;
}
</style>
