# Product Context

This file describes the purpose and user experience goals of the project.

## Purpose:
To provide a web-based user interface for interacting with an auction system. It aims to facilitate auction management for auctioneers and participation for traders.

## Problems Solved:
- Provides a structured interface for auctioneers to set up and manage auctions, users, companies, and sessions.
- Offers traders a clear view of available auctions and a way to participate.
- Centralizes the interaction point with the `au25-connector` backend.

## How it Works:
The project is a single-page application built with Vue.js. It uses Vue Router for navigation between different views (e.g., auctioneer pages, trader pages, login). State management is handled by Pinia stores. The UI components are built using Naive UI. Communication with the backend is likely handled through the `au25-connector` dependency. The application serves different interfaces based on user roles (auctioneer or trader).

## User Experience Goals:
- Provide a clear and intuitive interface for both auctioneers and traders.
- Ensure efficient navigation between different sections of the application.
- Display real-time or near real-time updates related to auctions and sessions.
- Offer a responsive design that works across different devices (though specific requirements are unknown).
