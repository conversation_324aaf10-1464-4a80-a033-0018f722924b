# System Patterns

This file describes the system architecture, key technical decisions, and design patterns.

## System Architecture:
The project follows a client-server architecture where the frontend (this project) acts as the client and interacts with a backend system via the `au25-connector`. The frontend is structured as a single-page application (SPA).

## Key Technical Decisions:
- Use of Vue 3 with the Composition API.
- State management handled by Pinia.
- Routing managed by Vue Router.
- UI components provided by Naive UI.
- TypeScript for type safety and improved code maintainability.
- Vite as the build tool for fast development and builds.

## Design Patterns:
- Component-based architecture (Vue components).
- Centralized state management (Pinia stores).
- Router-based navigation.

## Component Relationships:
- The main `App.vue` likely serves as the entry point, utilizing `vue-router` to render different views.
- Views (e.g., `AuctioneerHome.vue`, `TraderAuctionList.vue`, `LoginPage.vue`) are composed of smaller, reusable components (e.g., components in `src/components/common/`, `src/components/layout/`).
- Pinia stores are used to manage shared state across components.

## Critical Implementation Paths:
- User authentication and authorization (likely handled through the `au25-connector` and managed in a Pinia store like `auth.ts.txt`).
- Real-time data updates from the backend (suggested by `useWebSocket.old.ts.txt` and `websocket.old.ts.txt`).
- Displaying and interacting with auction data for both auctioneers and traders.
- Handling different user roles and permissions to display appropriate interfaces and functionality.
