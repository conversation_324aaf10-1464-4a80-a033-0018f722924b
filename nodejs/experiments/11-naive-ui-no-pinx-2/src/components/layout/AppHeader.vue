<template>
    <n-page-header>
        <template #title>
            <a href="#"
               @click.prevent="goHome"
               style="text-decoration: none; color: inherit;">
                Auction System
            </a>
            <n-tag>isLoggedIn: {{    liveStore.isLoggedIn}} </n-tag>
            <n-tag>currentPage: {{    liveStore.currentPage}} </n-tag>
            <n-tag v-if="liveStore.isLoggedIn && liveStore.currentAuctionName"
                   type="info"
                   size="small"
                   :bordered="false"
                   style="margin-left: 15px;">
                {{ liveStore.currentAuctionName }} ({{ liveStore.currentPage }})
            </n-tag>
        </template>
        <!-- NavBar removed from here -->
        <template #extra>
            <n-space align="center">
                <n-tooltip trigger="hover">
                    <template #trigger>
                        <n-icon size="20"
                                :color="connectionColor">
                            <WifiIcon/>
                        </n-icon>
                    </template>
                    {{ connectionStatusText }}
                </n-tooltip>
                <span v-if="liveStore.time">{{ formattedTime }}</span>
                <span v-if="liveStore.isLoggedIn">| Welcome, {{ liveStore.session_user?.username }}</span>
                <span v-if="liveStore.session_user?.company_shortname">({{
                        liveStore.session_user?.company_shortname
                    }})</span>
                <n-button v-if="liveStore.isLoggedIn"
                          @click="logout"
                          size="small"
                          quaternary>Logout
                </n-button>
            </n-space>
        </template>
    </n-page-header>
</template>

<script setup
        lang="ts">
import {NButton, NIcon, NPageHeader, NSpace, NTag, NTooltip} from 'naive-ui'; // Keep existing imports
import {WifiOutline as WifiIcon} from '@vicons/ionicons5';
import {computed} from 'vue';
import {format} from 'date-fns';
import {liveClientStore} from "@/stores/liveClientStore";
import {
    Connector, page_set_command, PageName, type PageSetCommand,
    session_terminate_command,
    type SessionTerminateCommand,
    SessionTerminationReason
} from "au25-connector";
// NavBar import removed

const liveStore = liveClientStore()
const connector = Connector.getInstance()

const formattedTime = computed(() => {
    const timeVal = liveStore.time
    if (!timeVal) return ''
    try {
        const date = new Date(
                timeVal.date_time.year,
                timeVal.date_time.month,
                timeVal.date_time.day_of_month,
                timeVal.date_time.hour,
                timeVal.date_time.minutes,
                timeVal.date_time.seconds
        );
        return format(date, 'MMM dd, yyyy HH:mm:ss')
    } catch (e) {
        console.error("Error formatting time:", e);
        return "Invalid Time";
    }
})

const connectionColor = computed(() => {
    if (connector.isConnected()) return '#63e2b7'
    if (connector.isConnecting()) return '#f0a020'
    return '#d03050'
})

const connectionStatusText = computed(() => {
    return connector.isConnected() ? 'Connected' :
            connector.isConnecting() ? 'Connecting' : 'Disconnected'
})

function logout() {
    const commandPayload: SessionTerminateCommand = {reason: SessionTerminationReason.SIGNED_OFF};
    Connector.publish(session_terminate_command(commandPayload));
    liveStore.logout()
}

function goHome() {
    if (liveStore.isLoggedIn && liveStore.currentPage !== PageName.HOME_PAGE) {
        const commandPayload: PageSetCommand = {page: PageName.HOME_PAGE};
        Connector.publish(page_set_command(commandPayload));
    }
}
</script>

<style scoped>
.n-page-header {
    align-items: center;
}

a {
    cursor: pointer;
}

/* Removed the flex-direction: column styles */
.n-page-header > .n-page-header__main {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.n-page-header > .n-page-header__main > .n-page-header__extra {
    white-space: nowrap;
}
</style>
