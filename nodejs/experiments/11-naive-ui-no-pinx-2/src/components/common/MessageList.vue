<template>
  <n-list hoverable clickable bordered style="max-height: 300px; overflow-y: auto;">
    <template #header>
      Messages
    </template>
    <n-list-item v-for="msg in messages" :key="msg.id">
       <n-thing>
           <template #avatar>
             <n-icon size="20" :color="getIconColor(msg.message_type)">
               <component :is="getIcon(msg.message_type)" />
             </n-icon>
           </template>
           <template #header>
             <n-text :type="getTextType(msg.message_type)" style="font-size: 0.9em;">
                {{ msg.from }} <n-icon size="12"><ArrowForwardIcon /></n-icon> {{ msg.to }}
             </n-text>
           </template>
            <template #header-extra>
                <n-time :time="msg.timestamp" format="HH:mm:ss" />
            </template>
           {{ msg.message }}
       </n-thing>
    </n-list-item>
     <n-list-item v-if="messages.length === 0">
        <n-empty description="No messages yet." size="small"/>
     </n-list-item>
  </n-list>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { NList, NListItem, NThing, NIcon, NText, NTime, NEmpty } from 'naive-ui';
import {
    ChatboxEllipsesOutline as AuctioneerIcon,
    PersonOutline as TraderIcon,
    BuildOutline as SystemIcon,
    ArrowForwardOutline as ArrowForwardIcon,
    InformationCircleOutline as InfoIcon, // Default/Info
    CheckmarkCircleOutline as SuccessIcon,
    AlertCircleOutline as WarningIcon,
    CloseCircleOutline as ErrorIcon,
    SyncCircleOutline as LoadingIcon
} from '@vicons/ionicons5';
import type { MessageElement } from 'au25-connector';
import { AuMessageType } from 'au25-connector';

const props = defineProps<{
  messages: MessageElement[]
}>();

const getIcon = (type: AuMessageType) => {
    switch (type) {
        case AuMessageType.AUCTIONEER_BROADCAST:
        case AuMessageType.AUCTIONEER_TO_TRADER:
            return AuctioneerIcon;
        case AuMessageType.TRADER_TO_AUCTIONEER:
            return TraderIcon;
        case AuMessageType.SYSTEM_BROADCAST:
        case AuMessageType.SYSTEM_TO_AUCTIONEER:
        case AuMessageType.SYSTEM_TO_TRADER:
            return SystemIcon;
        default:
            return InfoIcon; // Default icon
    }
};

const getIconColor = (type: AuMessageType) => {
     // You can customize colors based on message type if needed
     switch (type) {
         case AuMessageType.TRADER_TO_AUCTIONEER: return '#63e2b7'; // Example: Green for trader messages
         default: return undefined; // Use default theme color
     }
};

const getTextType = (type: AuMessageType) => {
     switch (type) {
         case AuMessageType.SYSTEM_TO_AUCTIONEER:
         case AuMessageType.SYSTEM_TO_TRADER:
             return 'warning'; // Example: Highlight system messages
         default:
             return 'default';
     }
}

</script>

<style scoped>
.n-list-item {
  padding: 8px 12px; /* Adjust padding */
}
.n-thing {
    margin-bottom: 0px; /* Reduce space between items */
}
</style>
