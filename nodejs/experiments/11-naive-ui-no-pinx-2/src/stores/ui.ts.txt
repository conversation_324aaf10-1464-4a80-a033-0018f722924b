import { defineStore, type Store } from 'pinia'
import { ref } from 'vue'
import type { } from 'naive-ui'

// NB: THIS IS THE OLD API!!
// Define the shape of the injected $naive property
interface NaiveApi {
  message: MessageApiInjection
  notification: NotificationApiInjection
  dialog: DialogApiInjection
  loadingBar: LoadingBarApiInjection
}

export const useUiStore = defineStore('ui', function(this: Store<'ui'> & { $naive?: NaiveApi }) {
  // --- State ---
  const showGlobalLoading = ref(false)
  const lastCommandSuccess = ref<boolean | null>(null) // null = unknown, true = success, false = fail (though alerts handle fails)
  const isNetworkOnline = ref(navigator.onLine) // Initial network status

  // --- Actions ---
  function setGlobalLoading(isLoading: boolean) {
    showGlobalLoading.value = isLoading
  }

  function setLastCommandSuccess(success: boolean | null) {
    lastCommandSuccess.value = success
  }

   function setNetworkStatus(online: boolean) {
       isNetworkOnline.value = online;
   }

  // --- Methods using Naive UI Discrete API ---
  // Access the injected $naive property
  const getNaiveApi = () => this.$naive;

  function showSuccess(content: string, duration = 3000) {
    const naiveApi = getNaiveApi();
    if (naiveApi?.message) {
      naiveApi.message.success(content, { duration })
    } else {
      console.warn('Naive UI message API not available in UI store.')
      alert(`SUCCESS: ${content}`) // Fallback
    }
  }

  function showInfo(content: string, duration = 3000) {
     const naiveApi = getNaiveApi();
     if (naiveApi?.message) {
       naiveApi.message.info(content, { duration })
     } else {
       console.warn('Naive UI message API not available in UI store.')
       alert(`INFO: ${content}`)
     }
  }

  function showWarning(content: string, duration = 5000) {
     const naiveApi = getNaiveApi();
     if (naiveApi?.message) {
       naiveApi.message.warning(content, { duration })
     } else {
       console.warn('Naive UI message API not available in UI store.')
       alert(`WARNING: ${content}`)
     }
  }

  function showError(content: string, duration = 5000) {
     const naiveApi = getNaiveApi();
     if (naiveApi?.message) {
       naiveApi.message.error(content, { duration, closable: true })
     } else {
       console.warn('Naive UI message API not available in UI store.')
       alert(`ERROR: ${content}`)
     }
  }

   function showDialogError(title: string, content: string) {
       const naiveApi = getNaiveApi();
       if (naiveApi?.dialog) {
           naiveApi.dialog.error({
               title: title,
               content: content,
               positiveText: 'OK'
           });
       } else {
           console.warn('Naive UI dialog API not available in UI store.');
           alert(`ERROR: ${title}\n\n${content}`);
       }
   }

   // --- Network Status Listener ---
   // It's better to handle listeners outside the store setup function
   // to avoid potential memory leaks if the store is recreated.
   // Consider placing these in App.vue or a dedicated composable.
   // For simplicity here, we'll keep them, but be aware.
   if (typeof window !== 'undefined') {
       window.addEventListener('online', () => setNetworkStatus(true));
       window.addEventListener('offline', () => setNetworkStatus(false));
   }


  return {
    showGlobalLoading,
    lastCommandSuccess,
    isNetworkOnline,
    setGlobalLoading,
    setLastCommandSuccess,
    setNetworkStatus,
    showSuccess,
    showInfo,
    showWarning,
    showError,
    showDialogError,
  }
})
