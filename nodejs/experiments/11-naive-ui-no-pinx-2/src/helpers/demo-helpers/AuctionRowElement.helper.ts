import type { AuctionRowElement } from "au25-connector"
import {random_bool} from "@/utils";

export function createDemo__AuctionRowElement(is_auctioneer: boolean, id = 1): AuctionRowElement {
	return {
		auction_design: "",
		auction_id: id + "",
		auction_name: `Auction ${id}`,
		id: id + "",
		isClosed: random_bool(),
		isHidden: random_bool(),
		starting_time_text: "starting time label",
		// common_state_text: is_auctioneer ?
		// 	"" :
		// 	"Auction round 5 open"
		common_state_text: "Round 5 open"
	}
}
