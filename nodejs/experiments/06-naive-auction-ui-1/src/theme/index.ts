import type { GlobalThemeOverrides, ThemeCommonVars } from "naive-ui"
// *** Ensure this import line is present and correct ***
import { Layout, RouterTransition, ThemeNameEnum } from "@/types/theme.d"
import type { ColorAction, ColorKey, ThemeColor } from "@/types/theme.d"
import tokens from "@/design-tokens.json"
import { colorToArray, expandPattern, getThemeColors, getTypeValue } from "@/utils/theme"
import { useOsTheme } from "naive-ui"
import _get from "lodash/get"
import _set from "lodash/set"

// Define the structure based on Pinx's getDefaultState
// Ensure this interface also imports Layout correctly if needed elsewhere,
// but the direct import above is the key for getDefaultState
interface ThemeState {
	layout: Layout
	themeName: ThemeNameEnum
	routerTransition: RouterTransition
	routerTransitionDuration: number
	rtl: boolean
	boxed: {
		enabled: boolean
		toolbar: boolean
		width: number
	}
	sidebar: {
		autoClose: boolean
		collapsed: boolean
		autoCloseBreakpoint: number
		animEase: string
		animDuration: number
		openWidth: number
		closeWidth: number
	}
	footer: {
		show: boolean
	}
	responsive: {
		breakpoint: number
		override: {
			viewPadding: { desk: number; mobile: number }
			toolbarHeight: { desk: number; mobile: number }
		}
	}
	toolbarHeight: number
	viewPadding: number
	headerBarHeight: number
	colors: typeof tokens.colors
	borderRadius: typeof tokens.borderRadius
	lineHeight: typeof tokens.lineHeight
	fontSize: typeof tokens.fontSize
	fontWeight: typeof tokens.fontWeight
	fontFamily: typeof tokens.fontFamily
	typography: typeof tokens.typography
    // *** REMOVED style: { [key: string]: string } from state definition ***
}

// Define the structure for getters used within getCssVars
interface ThemeGetters {
	naiveCommon: ThemeCommonVars
    // Add other getters if needed by getCssVars
}


const osTheme = useOsTheme()

export function getDefaultState(): ThemeState {
	return {
		layout: Layout.VerticalNav, // *** Using the imported Layout enum ***
		themeName: osTheme.value === "dark" ? ThemeNameEnum.Dark : ThemeNameEnum.Light,
		routerTransition: RouterTransition.FadeUp,
		routerTransitionDuration: 0.3,
		rtl: false,
		boxed: {
			enabled: true,
			toolbar: true,
			width: 1600
		},
		sidebar: {
			autoClose: true,
			collapsed: false,
			autoCloseBreakpoint: 1000,
			animEase: "ease-in-out",
			animDuration: 0.3,
			openWidth: 240,
			closeWidth: 64
		},
		footer: {
			show: true
		},
		responsive: {
			breakpoint: 700,
			override: {
				viewPadding: {
					desk: 24,
					mobile: 16
				},
				toolbarHeight: {
					desk: 60,
					mobile: 56
				}
			}
		},
		toolbarHeight: 60,
		viewPadding: 24,
		headerBarHeight: 60,
		colors: tokens.colors,
		borderRadius: tokens.borderRadius,
		lineHeight: tokens.lineHeight,
		fontSize: tokens.fontSize,
		fontWeight: tokens.fontWeight,
		fontFamily: tokens.fontFamily,
		typography: tokens.typography
        // *** REMOVED style: {} initialization here ***
	}
}

export function getThemeOverrides(state: ThemeState): GlobalThemeOverrides {
	const {
		primary,
		success,
		warning,
		error,
		info,
		background,
		backgroundSecondary,
		bodyBackground,
		text,
		textTertiary,
		border,
		hover
	} = state.colors[state.themeName]

	const themeColors = getThemeColors({ primary, success, warning, error, info })

	const lineHeight = getTypeValue(state, state.lineHeight.default)
	const borderRadius = getTypeValue(state, state.borderRadius.default)
	const borderRadiusSmall = getTypeValue(state, state.borderRadius.small)
	const borderColor = border
	const hoverColor = hover

	return {
		common: {
			...themeColors,
			textColorBase: text,
			textColor1: text,
			textColor2: text,
			textColor3: textTertiary,
			bodyColor: bodyBackground,
			baseColor: background,
			popoverColor: background,
			cardColor: background,
			modalColor: background,
			lineHeight: lineHeight,
			borderRadius: borderRadius,
			borderRadiusSmall: borderRadiusSmall,
			fontSize: getTypeValue(state, state.fontSize.default),
			fontWeight: getTypeValue(state, state.fontWeight.default),
			fontWeightStrong: getTypeValue(state, state.fontWeight.strong),
			fontFamily: getTypeValue(state, state.fontFamily.default),
			fontFamilyMono: getTypeValue(state, state.fontFamily.mono),
			borderColor: borderColor,
			hoverColor: hoverColor,
			dividerColor: borderColor
		},
		Card: {
			color: background,
			colorEmbedded: backgroundSecondary,
			titleFontSizeSmall: getTypeValue(state, state.fontSize.cardTitle),
			titleFontSizeMedium: getTypeValue(state, state.fontSize.cardTitle),
			titleFontSizeLarge: getTypeValue(state, state.fontSize.cardTitle),
			titleFontSizeHuge: getTypeValue(state, state.fontSize.cardTitle),
			titleFontWeight: getTypeValue(state, state.fontWeight.cardTitle)
		},
		LoadingBar: {
			colorLoading: primary
		},
		Tag: {
			colorBordered: "rgba(0, 0, 0, 0.1)"
		},
		Typography: {
			headerFontSize1: getTypeValue(state, state.typography.h1.fontSize),
			headerFontSize2: getTypeValue(state, state.typography.h2.fontSize),
			headerFontSize3: getTypeValue(state, state.typography.h3.fontSize),
			headerFontSize4: getTypeValue(state, state.typography.h4.fontSize),
			headerFontSize5: getTypeValue(state, state.typography.h5.fontSize),
			headerFontSize6: getTypeValue(state, state.typography.h6.fontSize),
            headerLineHeight1: getTypeValue(state, state.typography.h1.lineHeight),
            headerLineHeight2: getTypeValue(state, state.typography.h2.lineHeight),
            headerLineHeight3: getTypeValue(state, state.typography.h3.lineHeight),
            headerLineHeight4: getTypeValue(state, state.typography.h4.lineHeight),
            headerLineHeight5: getTypeValue(state, state.typography.h5.lineHeight),
            headerLineHeight6: getTypeValue(state, state.typography.h6.lineHeight),
		}
	}
}

export function getCssVars(state: ThemeState, getters: ThemeGetters): { [key: string]: string } {
	const naive = getters.naiveCommon

	const bgColor = naive.baseColor
	const bgSecondaryColor = state.colors[state.themeName].backgroundSecondary
	const fgColor = state.colors[state.themeName].text
	const fgSecondaryColor = state.colors[state.themeName].textSecondary
	const fgTertiaryColor = state.colors[state.themeName].textTertiary

	const bgSidebar = state.colors[state.themeName].sidebarBackground
	const bgBody = state.colors[state.themeName].bodyBackground

	const boxedWidth = state.boxed.width
	const routerTransitionDuration = state.routerTransitionDuration
	const sidebarAnimEase = state.sidebar.animEase
	const sidebarAnimDuration = state.sidebar.animDuration
	const sidebarOpenWidth = state.sidebar.openWidth
	const sidebarCloseWidth = state.sidebar.closeWidth
	const toolbarHeight = state.toolbarHeight
	const viewPadding = state.viewPadding
	const headerBarHeight = state.headerBarHeight
	const fontFamily = getTypeValue(state, state.fontFamily.default)
	const fontFamilyDisplay = getTypeValue(state, state.fontFamily.display)
	const fontFamilyMono = getTypeValue(state, state.fontFamily.mono)
	const fontSize = getTypeValue(state, state.fontSize.default)
	const lineHeight = getTypeValue(state, state.lineHeight.default)

	const borderRadius = getTypeValue(state, state.borderRadius.default)
	const borderRadiusSmall = getTypeValue(state, state.borderRadius.small)

	const bezierEase = naive.cubicBezierEaseInOut

	const styleObject: Record<string, string> = {
		"bg-body-color": `${bgBody}`,
		"bg-sidebar-color": `${bgSidebar}`,
		"fg-default-color": `${fgColor}`,
		"fg-secondary-color": `${fgSecondaryColor}`,
		"fg-tertiary-color": `${fgTertiaryColor}`,
		"bg-default-color": `${bgColor}`,
		"bg-secondary-color": `${bgSecondaryColor}`,
		"bezier-ease": `${bezierEase}`,
		"router-transition-duration": `${routerTransitionDuration}s`,
		"sidebar-anim-ease": `${sidebarAnimEase}`,
		"sidebar-anim-duration": `${sidebarAnimDuration}s`,
		"sidebar-open-width": `${sidebarOpenWidth}px`,
		"sidebar-close-width": `${sidebarCloseWidth}px`,
		"boxed-width": `${boxedWidth}px`,
		"toolbar-height": `${toolbarHeight}px`,
		"header-bar-height": `${headerBarHeight}px`,
		"view-padding": `${viewPadding}px`,
		"border-radius": `${borderRadius}`,
		"border-radius-small": `${borderRadiusSmall}`,
		"font-family": `${fontFamily}`,
		"font-family-display": `${fontFamilyDisplay}`,
		"font-family-mono": `${fontFamilyMono}`,
		"font-size": `${fontSize}`,
		"line-height": `${lineHeight}`
	}

	// import colors by patterns
	for (const pattern of ["extra(1|2|3|4)", "primary", "success", "warning", "error", "info", "border", "hover"]) {
		const keys = expandPattern(pattern)

		for (const key of keys) {
			styleObject[`${key}-color`] = (state.colors[state.themeName] as Record<string, string>)[key]
		}
	}

	// add RGB values variant
	for (const [key, value] of Object.entries(styleObject)) {
		if (key.endsWith("-color") && value) {
			try {
				styleObject[`${key}-rgb`] = colorToArray(value, "rgb").join(" ")
			} catch (e) {
				console.warn(`Could not parse color for CSS var ${key}: ${value}`, e);
			}
		}
	}

	return styleObject
}