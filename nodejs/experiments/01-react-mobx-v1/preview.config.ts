// preview.config.js
import { defineConfig } from '@previewjs/config';
import tailwindcss from 'tailwindcss';
import autoprefixer from 'autoprefixer';

export default defineConfig({
    wrapper: {
        path: "src/PreviewWrapper.tsx",
        componentName: "PreviewWrapper",
    },
    // Add PostCSS configuration for Tailwind
    postcss: {
        plugins: [
            tailwindcss,
            autoprefixer,
        ],
    },
    // Optional: Add vite configuration if needed
    // vite: {
    //     css: {
    //         postcss: {
    //             plugins: [
    //                 tailwindcss,
    //                 autoprefixer,
    //             ],
    //         },
    //     },
    // }
});
