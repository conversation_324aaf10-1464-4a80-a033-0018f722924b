// AuOrderConstraintsBar.tsx
import React, { useState, useEffect, useMemo } from 'react';
import chroma from 'chroma-js';
import { range } from 'lodash';
import {auColors} from "@/styles/AuColors.ts";
import './AuConstraintsBar.css'


// Types and Enums
enum OrderType {
    BUY = 'BUY',
    SELL = 'SELL',
    NONE = 'NONE'
}

interface DeBidConstraints {
    max_buy_quantity: number;
    max_sell_quantity: number;
    min_buy_quantity: number;
    min_sell_quantity: number;
}

interface AuOrderConstraintsBarProps {
    constraints: DeBidConstraints | null
    height?: number
    max_quantity?: number
    order_quantity: number | null
    order_type: OrderType | null
    show_labels?: boolean
    tick_font_size?: number
    tick_step?: number
    width?: number
    price_change?: 'up' | 'down' | null
}

export const AuOrderConstraintsBar: React.FC<AuOrderConstraintsBarProps> = ({
                                                                                constraints = null,
                                                                                height = 300,
                                                                                max_quantity = 50,
                                                                                order_quantity = null,
                                                                                order_type = null,
                                                                                show_labels = true,
                                                                                tick_font_size = 12,
                                                                                tick_step = 10,
                                                                                width = 120,
                                                                                price_change = null
                                                                            }) => {
    const [isAnimating, setIsAnimating] = useState(false)
    const [previousConstraints, setPreviousConstraints] = useState<DeBidConstraints | null>(null)

    // Memoized calculations
    const barWidth = width
    const midX = barWidth / 2
    const pixelsPerVol = (0.9 * width) / (max_quantity * 2)
    const toPixels = (vol: number) => vol * pixelsPerVol
    const tickWidth = tick_step * pixelsPerVol

    const buyDimmed = useMemo(() => chroma(auColors.au_buy()).alpha(0.1).toString(), [])
    const sellDimmed = useMemo(() => chroma(auColors.au_sell()).alpha(0.1).toString(), [])
    const orderColor = useMemo(() => auColors.order_bright(order_type), [order_type])

    // Constraint calculations
    const maxBuyQuantity = constraints?.max_buy_quantity ?? 0
    const minBuyQuantity = constraints?.min_buy_quantity ?? 0
    const maxSellQuantity = constraints?.max_sell_quantity ?? 0
    const minSellQuantity = constraints?.min_sell_quantity ?? 0

    const buyConstraintWidth = toPixels(maxBuyQuantity - minBuyQuantity)
    const buyConstraintX = midX - toPixels(maxBuyQuantity)

    const sellConstraintWidth = Math.max(0, toPixels(maxSellQuantity - minSellQuantity))
    const sellConstraintX = midX + toPixels(minSellQuantity)

    // Tick calculations
    const tickXArr = useMemo(() => {
        const ticks = [midX]
        const count = Math.floor(max_quantity / tick_step)

        for (let i = 1; i <= count; i++) {
            const offset = i * tick_step * pixelsPerVol
            ticks.push(midX - offset)
            ticks.push(midX + offset)
        }

        return ticks
    }, [midX, max_quantity, tick_step, pixelsPerVol])

    const buyTicks = useMemo(() => {
        return range(tick_step, max_quantity + 1, tick_step)
            .reverse()
            .map(vol => ({
                vol,
                x: midX - toPixels(vol)
            }))
    }, [midX, max_quantity, tick_step, toPixels])

    const sellTicks = useMemo(() => {
        return range(tick_step, max_quantity + 1, tick_step)
            .map(vol => ({
                vol,
                x: midX + toPixels(vol)
            }))
    }, [midX, max_quantity, tick_step, toPixels])

    // Order indicator
    const orderWidth = 3
    const orderX = useMemo(() => {
        switch (order_type) {
            case OrderType.BUY:
                return midX - orderWidth / 2 - toPixels(Math.min(max_quantity, order_quantity || 0))
            case OrderType.SELL:
                return midX - orderWidth / 2 + toPixels(Math.min(max_quantity, order_quantity || 0))
            default:
                return midX - orderWidth / 2
        }
    }, [order_type, midX, orderWidth, toPixels, max_quantity, order_quantity])

    // Animation effect
    useEffect(() => {
        if (constraints && previousConstraints && price_change) {
            setIsAnimating(true)
            setPreviousConstraints(constraints)

            const timer = setTimeout(() => {
                setIsAnimating(false)
                setPreviousConstraints(null)
            }, 1000)

            return () => clearTimeout(timer)
        }
    }, [constraints, price_change])

    return (
        <div
            style={{
                width: `${width}px`,
                height: `${height}px`,
                position: 'relative',
                margin: 0,
                padding: 0,
                textAlign: 'center'
            }}
            className="AuOrderConstraintsBar"
        >
            {show_labels && (
                <div
                    style={{
                        position: 'absolute',
                        fontSize: `${tick_font_size}px`,
                        top: `${height - 2 + tick_font_size / 2}px`
                    }}
                >
                    <div
                        style={{
                            color: 'white',
                            position: 'absolute',
                            left: `${midX}px`,
                            transform: 'translateX(-50%)',
                            textAlign: 'center'
                        }}
                        className="tick-label"
                    >
                        0
                    </div>

                    {buyTicks.map((tick, i) => (
                        <div
                            key={`buy-${i}`}
                            style={{
                                left: `${midX - toPixels(tick.vol)}px`,
                                color: auColors.au_buy(),
                                width: `${tickWidth}px`,
                                textAlign: 'center',
                                transform: 'translateX(-50%)',
                                position: 'absolute'
                            }}
                            className="tick-label"
                        >
                            {tick.vol}
                        </div>
                    ))}

                    {sellTicks.map((tick, i) => (
                        <div
                            key={`sell-${i}`}
                            style={{
                                left: `${midX + toPixels(tick.vol)}px`,
                                color: auColors.au_sell(),
                                width: `${tickWidth}px`,
                                textAlign: 'center',
                                transform: 'translateX(-50%)',
                                position: 'absolute'
                            }}
                            className="tick-label"
                        >
                            {tick.vol}
                        </div>
                    ))}
                </div>
            )}

            <svg
                height={height}
                viewBox={`0 0 ${barWidth} ${height}`}
                width={barWidth}
                xmlns="http://www.w3.org/2000/svg"
            >
                {/* Buy side base */}
                <rect
                    fill={buyDimmed}
                    height={height - 6}
                    width={barWidth / 2}
                    x={0}
                    y={3}
                    className="base-rect"
                />

                {/* Buy constraints */}
                <rect
                    fill={auColors.au_buy_dimmed()}
                    height={height - 6}
                    width={buyConstraintWidth}
                    x={buyConstraintX}
                    y={3}
                    className={`constraint-rect buy-constraint ${
                        isAnimating && price_change === 'up' ? 'animating-increase' : ''
                    } ${isAnimating && price_change === 'down' ? 'animating-decrease' : ''}`}
                />

                {/* Sell side base */}
                <rect
                    fill={sellDimmed}
                    height={height - 6}
                    width={barWidth / 2}
                    x={barWidth / 2}
                    y={3}
                    className="base-rect"
                />

                {/* Sell constraints */}
                <rect
                    fill={auColors.au_sell_dim()}
                    height={height - 6}
                    width={sellConstraintWidth}
                    x={sellConstraintX}
                    y={3}
                    className={`constraint-rect sell-constraint ${
                        isAnimating && price_change === 'down' ? 'animating' : ''
                    } ${isAnimating && price_change === 'up' ? 'animating-increase' : ''}`}
                />

                {/* Tick marks */}
                {tickXArr.map((x, i) => (
                    <rect
                        key={i}
                        height={height}
                        width={0.25}
                        x={x}
                        fill="white"
                    />
                ))}

                {/* Order indicator */}
                {order_type && (
                    <rect
                        fill={orderColor}
                        height={height}
                        width={orderWidth}
                        x={orderX}
                        className="order-indicator"
                    />
                )}
            </svg>
        </div>
    )
}

export default AuOrderConstraintsBar;
