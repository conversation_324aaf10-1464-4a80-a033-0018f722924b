import { describe, it, expect, beforeEach, beforeAll, vi } from 'vitest';
import { deflateSync } from 'fflate'; // Correct import

// Mock threads/worker
vi.mock('threads/worker', () => ({
    expose: vi.fn((fn) => {
        (global as any).__workerFunction = fn;
    }),
    Transfer: vi.fn((val) => val)
}));

// Import worker to trigger expose
import '../connector/decompress.fflate.worker';

describe('WebSocket Decompression Worker', () => {
    let decompressFunction: (buffer: ArrayBuffer | null | undefined) => Promise<any>;

    beforeAll(() => {
        decompressFunction = (global as any).__workerFunction;
    });

    beforeEach(() => {
        vi.clearAllMocks();
    });

    it('should decompress valid binary data', async () => {
        const testData = { message: 'test', value: 123 };
        const jsonString = JSON.stringify(testData);
        const encoded = new TextEncoder().encode(jsonString);
        const compressed = deflateSync(encoded); // Use deflateSync

        const result = await decompressFunction(compressed.buffer);
        expect(result).toEqual(testData);
    });

    it('should handle invalid binary data', async () => {
        const invalidBuffer = new ArrayBuffer(8);
        await expect(decompressFunction(invalidBuffer))
            .rejects
            .toThrow('unexpected EOF'); // Updated error message
    });

    it('should handle null input', async () => {
        await expect(decompressFunction(null))
            .rejects
            .toThrow('No buffer provided');
    });

    it('should handle undefined input', async () => {
        await expect(decompressFunction(undefined))
            .rejects
            .toThrow('No buffer provided');
    });

    it('should handle non-ArrayBuffer input', async () => {
        // @ts-ignore - Testing invalid input
        await expect(decompressFunction('not a buffer'))
            .rejects
            .toThrow('Input must be an ArrayBuffer');
    });

    it('should handle empty ArrayBuffer', async () => {
        const emptyBuffer = new ArrayBuffer(0);
        await expect(decompressFunction(emptyBuffer))
            .rejects
            .toThrow('Empty buffer provided');
    });

    it('should handle 10MB payload', async () => {
        const largeData = {
            array: Array.from({ length: 200000 }, (_, i) => ({ id: i, value: `test${i}` })),
            timestamp: Date.now(),
            metadata: { source: 'test', version: '1.0.0' }
        };

        const jsonString = JSON.stringify(largeData);
        const encoded = new TextEncoder().encode(jsonString);
        const compressed = deflateSync(encoded); // Use deflateSync

        const start = performance.now();
        const result = await decompressFunction(compressed.buffer);
        const end = performance.now();

        console.log(`Decompression Time: ${(end - start).toFixed(2)} ms`);

        expect(result).toEqual(largeData);
    });
});
