This file is a merged representation of a subset of the codebase, containing specifically included files and files not matching ignore patterns, combined into a single document by Repomix.
The content has been processed where content has been compressed (code blocks are separated by ⋮---- delimiter).

================================================================
Directory Structure
================================================================
src/
  components/
    MockLiveClientStoreBuilder.ts
  store/
    domainStore.ts
    metricsStore.ts
  vite-env.d.ts
package.json
tsconfig.app.json
tsconfig.json
tsconfig.node.json
vite.config.ts

================================================================
Files
================================================================

================
File: src/components/MockLiveClientStoreBuilder.ts
================
// --- Mock LiveClientStoreBuilder ---
// This needs to be extremely thorough to match your actual types.
import {
    createRandom__CompanyElement,
    createTest__AuctionRowElement,
    createTest__CompanyElement,
    createTest__DeAuctionValue,
    createTest__DeCommonStatusValue,
    createTest__SessionUserValue,
    createTest__TimeValue,
    createTest__UserElement,
    random_int_inclusive
} from "au25-helpers";
import {
    AuUserRole,
    type DeAuctionValue,
    type DeCommonStatusValue,
    type LiveClientStore,
    type SessionUserValue,
    type UserElement
} from "au25-connector";
⋮----
export class MockLiveClientStoreBuilder
⋮----
constructor()
⋮----
// Initialize with deep defaults to ensure all paths exist
⋮----
private _getValidDeCommonStatus(partial?: Partial<DeCommonStatusValue> | null): DeCommonStatusValue
⋮----
private _getValidUserElement(partial?: Partial<UserElement>): UserElement
⋮----
private _getValidSessionUser(partial?: Partial<SessionUserValue>): SessionUserValue
⋮----
private _ensureFullState(partialState: Partial<LiveClientStore>): LiveClientStore
⋮----
if (partialState.de_auction) { // If de_auction was provided, ensure it's complete
⋮----
// Add ALL other required fields for CompanyElement
⋮----
// Add ALL other required fields for AuctionRowElement
⋮----
// Add ALL required fields for CounterpartyCreditElement
//id: '', // Example
⋮----
// withScenario(_s: string) { return this; }
withCompanies(n: number)
withTraders(n: number)
withAuctioneers(n: number)
withLoggedInUser(id: string)
withActiveAuction(id: string)
⋮----
this._state.de_auction = { auction_id: id } as DeAuctionValue; // Cast for partial init
⋮----
// Ensure common_status exists after setting auction_id
⋮----
withGenericAuctionRows(n: number)
//withCredits(_c: string | number) { return this; }
build(): LiveClientStore

================
File: src/store/domainStore.ts
================
import { proxy, snapshot } from 'valtio';
import { devtools } from 'valtio/utils';
⋮----
import type { LiveClientStore, TimeValue } from 'au25-connector';
⋮----
import { metricsStore } from './metricsStore';
import {createTest__TimeValue} from "au25-helpers";
⋮----
interface DomainStoreActions {
    setStateDirectly: (newState: LiveClientStore) => void;
    applyPatchFastJson: (newState: LiveClientStore) => void;
    applyPatchJsonDiffPatch: (newState: LiveClientStore) => void;
}
⋮----
type DomainStore = LiveClientStore & DomainStoreActions;
⋮----
interface ObjectWithId {
    id: string;
}
⋮----
// Define a complete default for TimeValue based on your actual type
⋮----
time: defaultTimeValue, // Use the defined default
⋮----
setStateDirectly(this: DomainStore, newState: LiveClientStore)
⋮----
// eslint-disable-next-line @typescript-eslint/no-explicit-any
⋮----
applyPatchFastJson(this: DomainStore, newState: LiveClientStore)
⋮----
// eslint-disable-next-line @typescript-eslint/no-explicit-any
⋮----
applyPatchJsonDiffPatch(this: DomainStore, newState: LiveClientStore)
⋮----
// eslint-disable-next-line @typescript-eslint/no-explicit-any

================
File: src/store/metricsStore.ts
================
import {proxy} from 'valtio';
import {devtools} from 'valtio/utils';
⋮----
// Define the state shape for metrics
interface PerformanceMetricsState {
    lastOpType: string | null;
    lastDiffTimeMs: number | null;
    lastPatchTimeMs: number | null;
    lastTotalTimeMs: number | null;
}
⋮----
// Define the store interface including actions
interface MetricsStore extends PerformanceMetricsState {
    setMetrics: (metrics: PerformanceMetricsState) => void;
    resetMetrics: () => void;
}
⋮----
// Initial state for metrics
⋮----
// Create the metrics store proxy
⋮----
// Action to update all metrics at once
setMetrics(metrics)
⋮----
// Action to reset metrics
resetMetrics()
⋮----
// Devtools integration

================
File: src/vite-env.d.ts
================
/// <reference types="vite/client" />

================
File: package.json
================
{
  "name": "dm25-test-valtio",
  "private": true,
  "version": "0.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc -b && vite build",
    "lint": "eslint .",
    "preview": "vite preview"
  },
  "dependencies": {
    "au25-connector": "link:../../packages/au25-connector",
    "au25-helpers": "link: ../../packages/au25-helpers",
    "react-router-dom": "7.5.3",
    "fast-json-patch": "^3.1.1",
    "jsondiffpatch": "^0.7.3",
    "react": "^19.1.0",
    "react-dom": "^19.1.0",
    "valtio": "^2.1.4"
  },
  "devDependencies": {
    "@eslint/js": "^9.25.0",
    "@types/react": "^19.1.2",
    "@types/react-dom": "^19.1.2",
    "@vitejs/plugin-react": "^4.4.1",
    "eslint": "^9.25.0",
    "eslint-plugin-react-hooks": "^5.2.0",
    "eslint-plugin-react-refresh": "^0.4.19",
    "globals": "^16.0.0",
    "typescript": "~5.8.3",
    "typescript-eslint": "^8.30.1",
    "vite": "^6.3.5"
  }
}

================
File: tsconfig.app.json
================
{
  "compilerOptions": {
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "verbatimModuleSyntax": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "erasableSyntaxOnly": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true,

    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "au25-connector": ["../../packages/au25-connector"], // Map to the directory
      "au25-connector/*": ["../../packages/au25-connector/*"], // Map sub-paths relative to the directory
      "au25-helpers": ["../../packages/au25-helpers"], // Map to the directory
      "au25-helpers/*": ["../../packages/au25-helpers/*"] // Map sub-paths relative to the directory
    }
  },
  "include": ["src"]
}

================
File: tsconfig.json
================
{
  "files": [],
  "references": [
    { "path": "./tsconfig.app.json" },
    { "path": "./tsconfig.node.json" }
  ]
}

================
File: tsconfig.node.json
================
{
  "compilerOptions": {
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.node.tsbuildinfo",
    "target": "ES2022",
    "lib": ["ES2023"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "verbatimModuleSyntax": true,
    "moduleDetection": "force",
    "noEmit": true,

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "erasableSyntaxOnly": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true
  },
  "include": ["vite.config.ts"]
}

================
File: vite.config.ts
================
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path' // <-- Import the 'path' module
⋮----
// https://vite.dev/config/



================================================================
End of Codebase
================================================================
