import React from 'react';
import {domainStore} from '../store/domainStore';
import type {LiveClientStore} from "au25-connector";

interface ControlsProps {
    initialState: LiveClientStore;
    bigChangeState: LiveClientStore;
    smallChangeState: LiveClientStore;
}

export const Controls: React.FC<ControlsProps> = ({
    initialState,
    bigChangeState,
    smallChangeState
}) => {
    console.log('Rendering Controls'); // Should only render once unless props change

    const handleReset = () => domainStore.setStateDirectly(initialState);
    const handleBigDirect = () => domainStore.setStateDirectly(bigChangeState);
    const handleSmallDirect = () => domainStore.setStateDirectly(smallChangeState);
    const handleBigFJP = () => domainStore.applyPatchFastJson(bigChangeState);
    const handleSmallFJP = () => domainStore.applyPatchFastJson(smallChangeState);
    const handleBigJDP = () => domainStore.applyPatchJsonDiffPatch(bigChangeState);
    const handleSmallJDP = () => domainStore.applyPatchJsonDiffPatch(smallChangeState);

    const buttonStyle = { margin: '5px' };

    return (
        <div style={{ border: '1px solid blue', padding: '10px', width: '300px' }}>
            <h3 style={{ margin: '0 0 10px 0', fontSize: '1em' }}>Trigger Updates</h3>
            <button style={buttonStyle} onClick={handleReset}>Reset to Initial State</button>
            <hr style={{ margin: '10px 0' }}/>
            <h4 style={{ margin: '10px 0 5px 0', fontSize: '0.9em' }}>Direct Assignment</h4>
            <button style={buttonStyle} onClick={handleBigDirect}>Apply Big Change (Direct)</button>
            <button style={buttonStyle} onClick={handleSmallDirect}>Apply Small Change (Direct)</button>
            <hr style={{ margin: '10px 0' }}/>
            <h4 style={{ margin: '10px 0 5px 0', fontSize: '0.9em' }}>Fast-JSON-Patch</h4>
            <button style={buttonStyle} onClick={handleBigFJP}>Apply Big Change (FJP)</button>
            <button style={buttonStyle} onClick={handleSmallFJP}>Apply Small Change (FJP)</button>
            <hr style={{ margin: '10px 0' }}/>
            <h4 style={{ margin: '10px 0 5px 0', fontSize: '0.9em' }}>jsondiffpatch</h4>
            <button style={buttonStyle} onClick={handleBigJDP}>Apply Big Change (JDP)</button>
            <button style={buttonStyle} onClick={handleSmallJDP}>Apply Small Change (JDP)</button>
        </div>
    );
};
