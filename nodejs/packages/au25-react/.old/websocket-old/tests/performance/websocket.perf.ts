// src/websocket/tests/performance/websocket.perf.ts
import { performance } from 'perf_hooks';
import { WebSocketConnector } from '../../connector/WebSocketConnector.ts';
import {login_command} from "@/types/generated.ts";

async function runPerformanceTests() {
    const connector = new WebSocketConnector({
        url: 'ws://localhost:8080',
        enableLogging: false
    });

    const messageCount = 1000;
    const messageSize = 1024; // 1KB
    const results: number[] = [];

    // Generate test data
    //const testData = Buffer.alloc(messageSize).fill('A').toString();

    // Wait for connection
    await new Promise<void>((resolve) => {
        connector.onStatus((status) => {
            if (status.state === 'OPEN') resolve();
        });
    });

    // Message handling
    connector.onMessage(() => {
        results.push(performance.now());
    });

    // Send messages
    const start = performance.now();
    for (let i = 0; i < messageCount; i++) {
        connector.publish(login_command({username: 'a1', password: '1'}));
    }

    // Wait for all responses
    await new Promise<void>((resolve) => {
        const interval = setInterval(() => {
            if (results.length === messageCount) {
                clearInterval(interval);
                resolve();
            }
        }, 100);
    });

    // Calculate metrics
    const end = performance.now();
    const totalTime = end - start;
    const averageLatency = results.reduce((a, b) => a + b, 0) / results.length;
    const throughput = messageCount / (totalTime / 1000);

    console.log({
        totalTime,
        averageLatency,
        throughput,
        messagesSent: messageCount,
        messageSizeBytes: messageSize
    });

    connector.disconnect();
}

runPerformanceTests().catch(console.error);
