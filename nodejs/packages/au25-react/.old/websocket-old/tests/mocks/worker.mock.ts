// src/websocket/tests/mocks/worker.mock.ts
export class <PERSON>ck<PERSON>orker {
    private static instance: MockWorker | null = null;
    public onmessage: ((event: MessageEvent) => void) | null = null;
    public onerror: ((event: ErrorEvent) => void) | null = null;

    static getInstance(): MockWorker {
        if (!MockWorker.instance) {
            MockWorker.instance = new MockWorker();
        }
        return MockWorker.instance;
    }

    static clearInstance() {
        MockWorker.instance = null;
    }

    public postMessage(data: any) {
        // Simulate worker processing
        setTimeout(() => {
            if (this.onmessage) {
                this.onmessage(new MessageEvent('message', {
                    data: {
                        type: 'success',
                        data: { decompressed: true, original: data }
                    }
                }));
            }
        }, 0);
    }

    public terminate() {
        // Cleanup
    }

    public simulateError(error: string) {
        if (this.onerror) {
            this.onerror(new ErrorEvent('error', { message: error }));
        }
    }
}

// Mock Worker global
(global as any).Worker = function() {
    return MockWorker.getInstance();
};
