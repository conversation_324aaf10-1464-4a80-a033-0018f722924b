# Edge Cases in Server-Controlled Navigation

Reference: See SPA-router-requiremnts.2.md for core requirements.

## The Only Edge Case

Login Page Reload:
- When user attempts to go "back" to login page while logged in
- Must break pure display model to show confirmation dialog
- Must handle full page reload if confirmed
- Only case where client does more than display current_page

This is the only true edge case because it's the only time we break the fundamental rule that the client just displays whatever current_page value the server sets in the store.

Everything else that might seem like an edge case isn't:
- URLs just send messages
- Bookmarks just send messages
- Browser buttons just send messages
- Messages either get sent or don't
- Server decides what to display
- Client shows what server decides
