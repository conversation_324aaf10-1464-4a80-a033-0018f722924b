import {random_bool, random_enum, random_number_old, random_string} from "@/au24/utils"
import {AuUserRole, ClientSocketState, type UserElement} from "@/au24/types/generated.js"

// changing this because our random number is creating duplicate ids !!
export function createDemo__UserElement_for_trader(
	// companyId: string | number = random_number_old({ rand: 1000, mult: 10 }),
	// userId: string | number = random_number_old({ rand: 1000, mult: 10 })
	id: number
): UserElement {
	const userId = id
	const companyId = id + 10000
	return {
		company_id: companyId + "",
		company_longname: companyId + " long name",
		company_shortname: companyId + "_short",
		current_auction_id: "1",
		email: "<EMAIL>",
		has_connection_problem: false,
		id: id + "", // random_number_old({ rand: 1000, mult: 10 }).toString(),
		isAuctioneer: random_bool(),
		isObserver: random_bool(),
		isOnline: random_bool(),
		isTester: random_bool(),
		password: random_number_old({ rand: 10, mult: 10 }) + "",
		phone: "(*************",
		role: random_enum(AuUserRole) as AuUserRole,
		socket_state: ClientSocketState.OPENED,
		socket_state_last_closed: null,
		termination_reason: null,
		user_id: userId + "",
		username: `${random_string(4)}_user_${userId}`
	}
}
