import React, { useState } from 'react';
import DeRoundTable from '../components/DeRoundTable';
import { DeRoundElement, TraderMap, DeRoundTraderElement, OrderType, OnlineStatus } from '../components/de-round-table-ag-helpers';

// Mock data generation - replace with actual data fetching
const generateMockData = () => {
  const rounds: DeRoundElement[] = Array.from({ length: 5 }, (_, i) => ({
    round_number: i + 1,
    price: 100 + i * 10,
    price_direction: i % 2 === 0 ? 'up' : 'down',
    excess_demand_indicator: i % 3 === 0 ? 'high' : (i % 3 === 1 ? 'low' : 'balanced'),
    total_buy_quantity: 1000 + i * 100,
    total_sell_quantity: 800 + i * 80,
  }));

  const traders: TraderMap = {
    'trader1': { trader_id: 'trader1', trader_name: 'Trader Alpha', trader_short_name: 'TA', online_status: OnlineStatus.ONLINE, is_local_trader: true },
    'trader2': { trader_id: 'trader2', trader_name: 'Trader Beta', trader_short_name: 'TB', online_status: OnlineStatus.OFFLINE, is_local_trader: false },
    'trader3': { trader_id: 'trader3', trader_name: 'Trader Gamma', trader_short_name: 'TG', online_status: OnlineStatus.ONLINE, is_local_trader: false },
  };

  const round_traders: DeRoundTraderElement[] = [];
  Object.keys(traders).forEach(trader_id => {
    rounds.forEach(round => {
      if (Math.random() > 0.3) { // Simulate some traders not bidding in all rounds
        round_traders.push({
          trader_id,
          round_number: round.round_number,
          quantity_int: Math.floor(Math.random() * 100) + 10,
          order_type: Math.random() > 0.5 ? OrderType.BUY : OrderType.SELL,
          constraints: {
            min_quantity: Math.floor(Math.random() * 50),
            max_quantity: Math.floor(Math.random() * 50) + 100,
          },
          // Add other fields if necessary based on DeRoundTraderElement definition
        });
      }
    });
  });

  return {
    rounds,
    traders,
    round_traders,
    buyMax: 1500,
    sellMax: 1200,
    maxValue: 2000, // For footer chart
  };
};

const DeRoundTableView: React.FC = () => {
  const [mockBlotterData] = useState(() => generateMockData());
  const [selectedRound, setSelectedRound] = useState<number>(1);

  const handleRoundChange = (roundNumber: number) => {
    setSelectedRound(roundNumber);
    // Potentially fetch new data or update state based on the selected round
    console.log(`Selected round changed to: ${roundNumber}`);
  };

  return (
    <div style={{ padding: '20px' }}>
      <h1>Auction Round Table</h1>
      {/* Basic round selector for testing */}
      <div style={{ marginBottom: '10px' }}>
        <label htmlFor="round-selector">Select Round: </label>
        <select 
          id="round-selector" 
          value={selectedRound} 
          onChange={(e) => handleRoundChange(parseInt(e.target.value, 10))}
        >
          {mockBlotterData.rounds.map(r => (
            <option key={r.round_number} value={r.round_number}>
              Round {r.round_number}
            </option>
          ))}
        </select>
      </div>
      <DeRoundTable 
        blotterData={mockBlotterData} 
        height="70vh" 
        selectedRoundNumber={selectedRound}
        onSelectedRoundChange={handleRoundChange} // Pass down if DeRoundTable needs to change it
      />
    </div>
  );
};

export default DeRoundTableView;