# Order Form Specification

Based on DeTraderInfoValue and auction rules, here's the detailed order form specification.

## 1. Form Structure

```typescript
interface OrderFormState {
    order_type: OrderType;
    quantity: string;
    validation_errors: string[];
}

interface OrderFormProps {
    // From DeTraderInfoValue
    bid_constraints: DeBidConstraints;
    price_label: string;
    quantity_label: string;
    round_price: string;
    
    // From DeCommonStatusValue
    price_direction: PriceDirection;
    price_has_reversed: boolean;
    
    onSubmit: (order: DeOrderSubmitCommand) => void;
}
```

## 2. Layout Design

```
+------------------------------------------+
|              Price Display                |
|  Current Price: $X.XX    Direction: ↑/↓  |
+------------------------------------------+
|              Order Type                   |
|     [Buy Button]    [Sell But<PERSON>]        |
+------------------------------------------+
|              Quantity                     |
|     [Input Field with Constraints]        |
|     Min: X  Max: Y  Step: Z              |
+------------------------------------------+
|           Validation Messages             |
|     [Error/Warning Messages Area]         |
+------------------------------------------+
|             Submit Order                  |
|     [Submit Button with State]            |
+------------------------------------------+
```

## 3. Validation Rules

### A. Order Type Constraints
```typescript
function validateOrderType(
    order_type: OrderType,
    price_direction: PriceDirection,
    price_has_reversed: boolean,
    previous_order: DeTraderHistoryRowElement | null
): string[] {
    const errors = [];
    
    // From auction rules
    if (price_direction === 'UP' && previous_order) {
        if (order_type === 'BUY' && 
            previous_order.order_type === 'BUY' && 
            previous_order.quantity > quantity) {
            errors.push("Cannot decrease buy quantity at higher prices");
        }
    }
    
    if (price_direction === 'DOWN' && previous_order) {
        if (order_type === 'SELL' && 
            previous_order.order_type === 'SELL' && 
            previous_order.quantity > quantity) {
            errors.push("Cannot decrease sell quantity at lower prices");
        }
    }
    
    return errors;
}
```

### B. Quantity Constraints
```typescript
function validateQuantity(
    quantity: number,
    constraints: DeBidConstraints,
    order_type: OrderType
): string[] {
    const errors = [];
    
    if (order_type === 'BUY') {
        if (quantity < constraints.min_buy_quantity) {
            errors.push(`Minimum buy quantity is ${constraints.min_buy_quantity}`);
        }
        if (quantity > constraints.max_buy_quantity) {
            errors.push(`Maximum buy quantity is ${constraints.max_buy_quantity}`);
        }
    } else {
        if (quantity < constraints.min_sell_quantity) {
            errors.push(`Minimum sell quantity is ${constraints.min_sell_quantity}`);
        }
        if (quantity > constraints.max_sell_quantity) {
            errors.push(`Maximum sell quantity is ${constraints.max_sell_quantity}`);
        }
    }
    
    return errors;
}
```

## 4. Component States

### A. Price Display
```typescript
interface PriceDisplayProps {
    price: string;
    direction: PriceDirection;
    has_reversed: boolean;
}

const PriceDisplay: React.FC<PriceDisplayProps> = ({
    price,
    direction,
    has_reversed
}) => (
    <div className={`price-display ${has_reversed ? 'reversed' : ''}`}>
        <span className="price">{price}</span>
        <DirectionIndicator direction={direction} />
    </div>
);
```

### B. Order Type Selection
```typescript
interface OrderTypeSelectProps {
    value: OrderType;
    onChange: (type: OrderType) => void;
    disabled?: boolean;
}

const OrderTypeSelect: React.FC<OrderTypeSelectProps> = ({
    value,
    onChange,
    disabled
}) => (
    <div className="order-type-select">
        <Button 
            selected={value === 'BUY'}
            onClick={() => onChange('BUY')}
            disabled={disabled}
        >
            Buy
        </Button>
        <Button 
            selected={value === 'SELL'}
            onClick={() => onChange('SELL')}
            disabled={disabled}
        >
            Sell
        </Button>
    </div>
);
```

### C. Quantity Input
```typescript
interface QuantityInputProps {
    value: string;
    onChange: (value: string) => void;
    constraints: DeBidConstraints;
    order_type: OrderType;
    disabled?: boolean;
}

const QuantityInput: React.FC<QuantityInputProps> = ({
    value,
    onChange,
    constraints,
    order_type,
    disabled
}) => {
    const { min, max } = order_type === 'BUY' 
        ? { min: constraints.min_buy_quantity, max: constraints.max_buy_quantity }
        : { min: constraints.min_sell_quantity, max: constraints.max_sell_quantity };
        
    return (
        <div className="quantity-input">
            <input
                type="number"
                value={value}
                onChange={e => onChange(e.target.value)}
                min={min}
                max={max}
                disabled={disabled}
            />
            <div className="constraints">
                Min: {min} Max: {max}
            </div>
        </div>
    );
};
```

## 5. Interaction States

### A. Form States
```typescript
type FormState = 
    | 'INITIAL'      // No input yet
    | 'EDITING'      // User is entering data
    | 'VALIDATING'   // Checking constraints
    | 'SUBMITTING'   // Sending order
    | 'ERROR'        // Validation failed
    | 'SUCCESS';     // Order submitted
```

### B. Button States
```typescript
interface SubmitButtonProps {
    formState: FormState;
    hasErrors: boolean;
    onClick: () => void;
}

const getButtonText = (state: FormState) => {
    switch (state) {
        case 'INITIAL': return 'Submit Order';
        case 'EDITING': return 'Submit Order';
        case 'VALIDATING': return 'Checking...';
        case 'SUBMITTING': return 'Submitting...';
        case 'ERROR': return 'Try Again';
        case 'SUCCESS': return 'Order Sent';
    }
};
```

## 6. Implementation Notes

1. Use controlled components for all inputs
2. Implement real-time validation
3. Show constraints inline with inputs
4. Use clear visual indicators for:
   - Price direction
   - Form state
   - Validation errors
   - Submission status

## 7. Styling Guidelines

1. Color Coding:
   - Buy actions: Green theme
   - Sell actions: Red theme
   - Validation errors: Red
   - Success states: Green
   - Disabled states: Gray

2. Typography:
   - Price display: Large, bold
   - Labels: Medium, regular
   - Error messages: Small, bold
   - Constraints: Small, regular

3. Layout:
   - Vertical stacking
   - Clear grouping
   - Consistent spacing
   - Mobile responsive

## 8. Accessibility

1. ARIA Labels:
   - Price changes
   - Validation errors
   - Button states

2. Keyboard Navigation:
   - Tab order
   - Enter to submit
   - Space to select order type

3. Screen Reader:
   - Price direction announcements
   - Error message readings
   - State changes