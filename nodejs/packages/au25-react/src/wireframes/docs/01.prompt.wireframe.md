This prompt file fully encapsulates our current design state and the next actionable steps.

Our goal is to create a wireframe to figure out User Flows and components for a React/ShadCN application that implements the rules of a unique auction.

1) For the auction rules, see:
   - **Auction Rules:** [A.auction-rules.readonly.md](./requirements/A.auction-rules.readonly.md)

2) The view is 100% driven by a Valtio store that is populated by the server.
   - **Valtio Store:** [nodejs/packages/au25-react/src/stores/domain-store.valtio.ts](../../stores/domain-store.valtio.ts )  
     *All UI components derive their state exclusively from this store.*

3) This application publishes EngineCommands to the backend Auction engine and subscribes to ClientCommands.
   - **API Types:** [nodejs/packages/au25-react/src/types/generated.ts](../../types/generated.ts)  
   - These types define:
     - ClientCommands: commands sent from server to client
     - EngineCommands: commands sent from client to server
     - Valtio store Value and Element types
     - Enumerations

4) The immutable files mentioned above (auction rules, API types, and the Valtio store) are the hard constraints for this wireframe and must not be modified. They serve as the authoritative references for both the frontend design and its integration with the existing backend.
This prompt file fully encapsulates our current design state and the next actionable steps.
5) Project Directory Structure:
   - **Root Directory for the wireframes:** [nodejs/packages/au25-react/docs/wireframes](.)  
     Contains all wireframe-related documents and subdirectories:
       • **requirements/** – Core requirement documents (user-flows.md, components.md).
       • **design/** – The consolidated design plan [ConsolidatedDesignPlan.md](./design/ConsolidatedDesignPlan.md) and other high-level design documents.
       • **reference/** – Supplemental detailed design files (e.g., auction-flows.md, auctioneer-panel.md, matrix-view.md, message-panel.md, order-form.md, component-hierarchy.md).
       • **archive/** – For deprecated files, retained for historical reference.
       • **[wireframe-prompt.md](./wireframe-prompt.md)** – This file, which serves as the running prompt and guide for this wireframe project.

6) **Strict Collaboration Rules:**
   - **Define requirements first, agree on design second, and then write code.**
   - **No code may be written unless the design is fully agreed upon.**
   - **Clarifying questions must be asked to ensure complete understanding before design decisions are finalized.**
   - You always have approval to read files, no need to ask.
   - The auction rules, API types, and Valtio store files are immutable and provide the essential constraints for the frontend.
   - All UI components must subscribe solely to the Valtio store and dispatch EngineCommands as defined in the API types.
   - This prompt file will serve as the basis for all future work. New chat sessions should begin with reference to this current state.

## Next Steps:
- all of the documents mentioned in this prompt, including all of the documents in folders mentioned in this prompt.
- Develop prototypes and wireframe sketches in the ./lib folder, based on the Consolidated Design Plan.
- Use the file ./lib/AppWireframe.tsx as the app file.
- this file will be called by src/main.tsx
YOU ARE NEVER PERMITTED TO EDIT ANY FILES OUTSIDE OF src/wireframes

