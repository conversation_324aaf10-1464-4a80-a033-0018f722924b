import { useSnapshot } from 'valtio'
import { getDomainStore } from '@/stores/domain-store.valtio'
import { auction_select_command, AuctionRowElement, PageName } from '@/types/generated'
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ColDef, GridOptions } from 'ag-grid-community'
import { useEngineCommands } from '../services/engineCommands'
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { BaseAgGrid } from './shared/BaseAgGrid'

interface CellRendererProps {
  data: AuctionRowElement;
}

export const HomePage = () => {
  const store = getDomainStore()
  const { auction_rows, session_user } = useSnapshot(store)
  const engineCommands = useEngineCommands()

  const columnDefs: ColDef[] = [
    {
      field: 'auction_name',
      headerName: 'Auction',
      flex: 1
    },
    {
      field: 'auction_design',
      headerName: 'Type',
      width: 120
    },
    {
      field: 'starting_time_text',
      headerName: 'Start Time',
      width: 180
    },
    {
      field: 'common_state_text',
      headerName: 'Status',
      width: 150,
      cellRenderer: (props: CellRendererProps) => {
        const { common_state_text } = props.data

        // Determine badge color based on status
        let variant = "default";
        if (common_state_text.includes('Open')) variant = "success";
        if (common_state_text.includes('Closed')) variant = "destructive";
        if (common_state_text.includes('Pending')) variant = "warning";

        return (
          <Badge variant={variant as any}>
            {common_state_text}
          </Badge>
        )
      }
    },
    {
      headerName: 'Actions',
      width: 120,
      cellRenderer: (props: CellRendererProps) => {
        const { isHidden, isClosed, auction_id } = props.data

        return (
          <Button
            size="sm"
            variant={isHidden ? "outline" : "default"}
            disabled={isClosed}
            onClick={() => handleSelectAuction(auction_id)}
          >
            {isHidden ? 'Hidden' : 'Select'}
          </Button>
        )
      }
    }
  ]

  const handleSelectAuction = async (auction_id: string) => {
    try {
      // Create auction select command
      const cmd = auction_select_command({ auction_id })

      // Dispatch command through engineCommands publisher
      await engineCommands.publish(cmd)

      // Navigate to appropriate page based on user role
      if (session_user?.role === 'TRADER') {
        store.setCurrentPage(PageName.DE_TRADER_PAGE)
      } else if (session_user?.role === 'AUCTIONEER') {
        store.setCurrentPage(PageName.DE_AUCTIONEER_PAGE)
      }
    } catch (error) {
      console.error('Failed to select auction:', error)
    }
  }

  // Filter auctions based on status for tabs
  const upcomingAuctions = auction_rows.filter(a =>
    a.common_state_text.includes('Pending') || a.common_state_text.includes('Scheduled'))
  const activeAuctions = auction_rows.filter(a =>
    a.common_state_text.includes('Open') || a.common_state_text.includes('Active'))
  const closedAuctions = auction_rows.filter(a =>
    a.common_state_text.includes('Closed') || a.common_state_text.includes('Completed'))

  // Common grid options
  const gridOptions: GridOptions = {
    rowSelection: { mode: 'singleRow' }
  };

  return (
    <Card className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Auctions</h2>
        {session_user?.role === 'AUCTIONEER' && (
          <Button onClick={() => store.setCurrentPage(PageName.DE_SETUP_PAGE)}>
            Create New Auction
          </Button>
        )}
      </div>

      <Tabs defaultValue="all" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="all">
            All <Badge variant="outline" className="ml-1">{auction_rows.length}</Badge>
          </TabsTrigger>
          <TabsTrigger value="upcoming">
            Upcoming <Badge variant="outline" className="ml-1">{upcomingAuctions.length}</Badge>
          </TabsTrigger>
          <TabsTrigger value="active">
            Active <Badge variant="outline" className="ml-1">{activeAuctions.length}</Badge>
          </TabsTrigger>
          <TabsTrigger value="closed">
            Closed <Badge variant="outline" className="ml-1">{closedAuctions.length}</Badge>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all">
          <BaseAgGrid
            rowData={[...auction_rows]}
            columnDefs={columnDefs}
            height="500px"
            gridOptions={gridOptions}
          />
        </TabsContent>

        <TabsContent value="upcoming">
          <BaseAgGrid
            rowData={[...upcomingAuctions]}
            columnDefs={columnDefs}
            height="500px"
            gridOptions={gridOptions}
          />
        </TabsContent>

        <TabsContent value="active">
          <BaseAgGrid
            rowData={[...activeAuctions]}
            columnDefs={columnDefs}
            height="500px"
            gridOptions={gridOptions}
          />
        </TabsContent>

        <TabsContent value="closed">
          <BaseAgGrid
            rowData={[...closedAuctions]}
            columnDefs={columnDefs}
            height="500px"
            gridOptions={gridOptions}
          />
        </TabsContent>
      </Tabs>
    </Card>
  )
}
