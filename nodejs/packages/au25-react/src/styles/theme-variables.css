:root {
  /* Primary colors */
  --primary-50: hsl(220, 100%, 97%);
  --primary-100: hsl(220, 100%, 92%);
  --primary-200: hsl(220, 100%, 85%);
  --primary-300: hsl(220, 100%, 75%);
  --primary-400: hsl(220, 100%, 65%);
  --primary-500: hsl(220, 100%, 55%);
  --primary-600: hsl(220, 100%, 45%);
  --primary-700: hsl(220, 100%, 35%);
  --primary-800: hsl(220, 100%, 25%);
  --primary-900: hsl(220, 100%, 15%);
  
  /* Secondary colors */
  --secondary-50: hsl(250, 70%, 97%);
  --secondary-100: hsl(250, 70%, 92%);
  --secondary-200: hsl(250, 70%, 85%);
  --secondary-300: hsl(250, 70%, 75%);
  --secondary-400: hsl(250, 70%, 65%);
  --secondary-500: hsl(250, 70%, 55%);
  --secondary-600: hsl(250, 70%, 45%);
  --secondary-700: hsl(250, 70%, 35%);
  --secondary-800: hsl(250, 70%, 25%);
  --secondary-900: hsl(250, 70%, 15%);
  
  /* Accent colors */
  --accent-50: hsl(280, 80%, 97%);
  --accent-100: hsl(280, 80%, 92%);
  --accent-200: hsl(280, 80%, 85%);
  --accent-300: hsl(280, 80%, 75%);
  --accent-400: hsl(280, 80%, 65%);
  --accent-500: hsl(280, 80%, 55%);
  --accent-600: hsl(280, 80%, 45%);
  --accent-700: hsl(280, 80%, 35%);
  --accent-800: hsl(280, 80%, 25%);
  --accent-900: hsl(280, 80%, 15%);
  
  /* Neutral colors */
  --neutral-50: hsl(220, 15%, 98%);
  --neutral-100: hsl(220, 15%, 95%);
  --neutral-200: hsl(220, 15%, 90%);
  --neutral-300: hsl(220, 15%, 80%);
  --neutral-400: hsl(220, 15%, 65%);
  --neutral-500: hsl(220, 15%, 50%);
  --neutral-600: hsl(220, 15%, 35%);
  --neutral-700: hsl(220, 15%, 25%);
  --neutral-800: hsl(220, 15%, 15%);
  --neutral-900: hsl(220, 15%, 10%);
  
  /* Status colors */
  --status-success: hsl(145, 80%, 40%);
  --status-warning: hsl(40, 100%, 50%);
  --status-error: hsl(0, 100%, 45%);
  --status-info: hsl(200, 100%, 45%);
  
  /* Auction-specific colors */
  --auction-active: hsl(145, 80%, 40%);
  --auction-pending: hsl(40, 100%, 50%);
  --auction-closed: hsl(0, 100%, 45%);
  --auction-time-normal: hsl(145, 80%, 40%);
  --auction-time-warning: hsl(40, 100%, 50%);
  --auction-time-critical: hsl(0, 100%, 45%);
  --auction-bid-accepted: hsl(145, 80%, 40%);
  --auction-bid-pending: hsl(40, 100%, 50%);
  --auction-bid-rejected: hsl(0, 100%, 45%);
  --auction-matrix-match: hsl(145, 80%, 40%);
  --auction-matrix-limit: hsl(0, 100%, 45%);
  --auction-matrix-warning: hsl(40, 100%, 50%);
  
  /* Convert to the format expected by tailwind.config.js */
  --primary: 220 100% 55%;
  --primary-foreground: 0 0% 100%;
  --secondary: 250 70% 55%;
  --secondary-foreground: 0 0% 100%;
  --accent: 280 80% 55%;
  --accent-foreground: 0 0% 100%;
  --background: 220 15% 98%;
  --foreground: 220 15% 15%;
  --card: 0 0% 100%;
  --card-foreground: 220 15% 15%;
  --popover: 0 0% 100%;
  --popover-foreground: 220 15% 15%;
  --muted: 220 15% 95%;
  --muted-foreground: 220 15% 35%;
  --destructive: 0 100% 45%;
  --destructive-foreground: 0 0% 100%;
  --border: 220 15% 90%;
  --input: 220 15% 90%;
  --ring: 220 100% 55%;
  
  /* Chart colors */
  --chart-1: 220 100% 55%;
  --chart-2: 250 70% 55%;
  --chart-3: 280 80% 55%;
  --chart-4: 145 80% 40%;
  --chart-5: 40 100% 50%;
  
  /* Sidebar colors */
  --sidebar-background: 220 15% 15%;
  --sidebar-foreground: 0 0% 100%;
  --sidebar-primary: 220 100% 55%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 280 80% 55%;
  --sidebar-accent-foreground: 0 0% 100%;
  --sidebar-border: 220 15% 25%;
  --sidebar-ring: 220 100% 55%;
  
  /* Border radius */
  --radius: 0.5rem;
}

.dark {
  /* Dark mode overrides */
  --primary: 220 100% 45%;
  --primary-foreground: 0 0% 100%;
  --secondary: 250 70% 45%;
  --secondary-foreground: 0 0% 100%;
  --accent: 280 80% 45%;
  --accent-foreground: 0 0% 100%;
  --background: 220 15% 10%;
  --foreground: 220 15% 90%;
  --card: 220 15% 15%;
  --card-foreground: 220 15% 90%;
  --popover: 220 15% 15%;
  --popover-foreground: 220 15% 90%;
  --muted: 220 15% 15%;
  --muted-foreground: 220 15% 65%;
  --destructive: 0 100% 45%;
  --destructive-foreground: 0 0% 100%;
  --border: 220 15% 25%;
  --input: 220 15% 25%;
  --ring: 220 100% 45%;
}