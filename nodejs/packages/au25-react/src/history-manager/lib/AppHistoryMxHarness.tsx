import React, { useEffect } from "react";
import { useSnapshot } from "valtio";
import { setNextPage, login, store, sendPageUnload } from "./HistoryManager";

// Debug Controls Component
const DebugControls: React.FC = () => {
  const snap = useSnapshot(store);

  // app.tsx
  useEffect(() => {
    // Initial load: Force replace to /
    console.log("load");
    if (window.location.pathname !== "/") {
      window.location.pathname = "/";
    }

    // Unload handler
    const unloadHandler = () => sendPageUnload();
    window.addEventListener("beforeunload", unloadHandler);
    return () => window.removeEventListener("beforeunload", unloadHandler);
  }, []);

  return (
    <div
      style={{
        marginBottom: "20px",
        padding: "10px",
        border: "1px solid #ccc",
      }}
      data-testid="debug-controls"
    >
      <h3>Debug Controls</h3>
      <div>
        <label>
          <input
            type="checkbox"
            checked={snap.isLoggedIn}
            onChange={(e) => {
              e.preventDefault();
            }}
            data-testid="login-checkbox"
          />
          Logged In
        </label>
      </div>
      <div style={{ marginTop: "10px" }}>
        <label>
          <input
            type="checkbox"
            checked={snap.auctionOneAccess}
            onChange={(e) => {
              store.auctionOneAccess = e.target.checked;
            }}
            data-testid="auction-one-access"
          />
          Auction One Access
        </label>
      </div>
      <div>
        <label>
          <input
            type="checkbox"
            checked={snap.auctionTwoAccess}
            onChange={(e) => {
              store.auctionTwoAccess = e.target.checked;
            }}
            data-testid="auction-two-access"
          />
          Auction Two Access
        </label>
      </div>
      <div style={{ marginTop: "20px" }}>
        <h4>Store State:</h4>
        <pre style={{ background: "#f5f5f5", padding: "10px" }}>
          {JSON.stringify(snap, null, 2)}
        </pre>
      </div>
    </div>
  );
};

// Navbar Component
const Navbar: React.FC = () => {
  const snap = useSnapshot(store);

  if (!snap.isLoggedIn) return null;

  return (
    <nav style={{ marginBottom: "20px" }}>
      <button onClick={() => setNextPage("/home", "")}>Home</button>
      <button onClick={() => setNextPage("/user", "")}>User</button>
      <button
        onClick={() => {
          window.location.reload();
        }}
      >
        Logout
      </button>
    </nav>
  );
};

// Page Components
const LoginPage: React.FC = () => {
  return (
    <div data-testid="current-page">
      <h2>LoginPage</h2>
      <button
        onClick={() => login()}
        data-testid="login-button"
      >
        Login
      </button>
    </div>
  );
};

const HomePage: React.FC = () => {
  const snap = useSnapshot(store);
  return (
    <div data-testid="current-page">
      <h2>HomePage</h2>
      <div style={{ marginTop: "20px" }}>
        <div>
          <button
            onClick={() => setNextPage("/auction", "1")}
            disabled={!snap.auctionOneAccess}
            style={{ marginRight: "10px" }}
          >
            Auction One {!snap.auctionOneAccess && "(No Access)"}
          </button>
          <span>
            Status: {snap.auctionOneAccess ? "Access Granted" : "No Access"}
          </span>
        </div>
        <div style={{ marginTop: "10px" }}>
          <button
            onClick={() => setNextPage("/auction", "2")}
            disabled={!snap.auctionTwoAccess}
            style={{ marginRight: "10px" }}
          >
            Auction Two {!snap.auctionTwoAccess && "(No Access)"}
          </button>
          <span>
            Status: {snap.auctionTwoAccess ? "Access Granted" : "No Access"}
          </span>
        </div>
      </div>
    </div>
  );
};

const AuctionPage: React.FC = () => {
  const snap = useSnapshot(store);
  return (
    <div data-testid="current-page">
      <h2>AuctionPage</h2>
      <div>ID: {snap.auction_id}</div>
      <div>
        Access:{" "}
        {(snap.auction_id === "1" && snap.auctionOneAccess) ||
        (snap.auction_id === "2" && snap.auctionTwoAccess)
          ? "Granted"
          : "Denied"}
      </div>
    </div>
  );
};

const UserPage: React.FC = () => {
  return <div data-testid="current-page">UserPage</div>;
};

// Main App Component
const App: React.FC = () => {
  const snap = useSnapshot(store);

  const renderPage = () => {
    switch (snap.current_page) {
      case "/home":
        return <HomePage />;
      case "/auction":
        return <AuctionPage />;
      case "/user":
        return <UserPage />;
      case "/":
      default:
        return <LoginPage />;
    }
  };

  return (
    <div>
      <DebugControls />
      <Navbar />
      {renderPage()}
    </div>
  );
};

export default App;
