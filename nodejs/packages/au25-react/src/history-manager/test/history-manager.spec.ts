import { test, expect, Page, Dialog } from '@playwright/test';

let page: Page;

test.describe('History Manager', () => {
  let baseUrl: string;

  test.beforeEach(async ({ browser }) => {
    page = await browser.newPage();
    // Try port 3000 first, fallback to 3001
    try {
      await page.goto('http://localhost:3000/');
      baseUrl = 'http://localhost:3000';
    } catch {
      await page.goto('http://localhost:3001/');
      baseUrl = 'http://localhost:3001';
    }
  });

  test.describe('Basic Navigation Flow', () => {
    test('regular navigation sequence', async () => {
      // Test steps from 1.1 in test plan
      await test.step('initial load', async () => {
        expect(page.url()).toMatch(/\/$/);
      });

      await test.step('login', async () => {
        await page.click('[data-testid="login-button"]');
        await expect(page).toHaveURL('http://localhost:3000/home');
        // Wait for navigation to complete
        await page.waitForSelector('[data-testid="current-page"]', { timeout: 1000 });
      });

      await test.step('navigate to user', async () => {
        await page.click('button:has-text("User")');
        await page.waitForURL('**/user', { timeout: 1000 });
        await expect(page).toHaveURL('http://localhost:3000/user');
        await expect(page.locator('[data-testid="current-page"]')).toContainText('UserPage');
      });

      await test.step('back navigation', async () => {
        await page.goBack();
        await page.waitForURL('**/home', { timeout: 1000 });
        await expect(page).toHaveURL('http://localhost:3000/home');
        await expect(page.locator('[data-testid="current-page"]')).toContainText('HomePage');
      });

      await test.step('forward navigation', async () => {
        await page.goForward();
        await page.waitForURL('**/user', { timeout: 1000 });
        await expect(page).toHaveURL('http://localhost:3000/user');
        await expect(page.locator('[data-testid="current-page"]')).toContainText('UserPage');
      });
    });

    test('multiple navigation sequence', async () => {
      // Login first
      await page.click('[data-testid="login-button"]');
      await page.waitForURL('**/home', { timeout: 1000 });
      await expect(page).toHaveURL('http://localhost:3000/home');
      await expect(page.locator('[data-testid="current-page"]')).toContainText('HomePage');
      
      // Navigate through multiple pages
      await page.click('button:has-text("User")');
      await page.waitForURL('**/user');
      await expect(page).toHaveURL('http://localhost:3000/user');
      await expect(page.locator('[data-testid="current-page"]')).toContainText('UserPage');
      
      // Wait for UserPage to be visible before checking auction access
      await expect(page.locator('[data-testid="current-page"]')).toContainText('UserPage');
      
      // Go back to HomePage to access auction
      await page.goBack();
      await page.waitForURL('**/home');
      await expect(page).toHaveURL('http://localhost:3000/home');
      await expect(page.locator('[data-testid="current-page"]')).toContainText('HomePage');
      
      // Ensure auction access is enabled
      const auctionOneAccess = page.locator('[data-testid="auction-one-access"]');
      await expect(auctionOneAccess).toBeChecked();
      
      // Find and click the enabled Auction One button
      const auctionOneButton = page.locator('button', { hasText: 'Auction One' });
      await expect(auctionOneButton).toBeEnabled();
      await auctionOneButton.click();
      await page.waitForURL('**/auction/1');
      await expect(page).toHaveURL('http://localhost:3000/auction/1');
      await expect(page.locator('[data-testid="current-page"]')).toContainText('AuctionPage');
      
      await page.click('button:has-text("User")');
      await page.waitForURL('**/user');
      await expect(page).toHaveURL('http://localhost:3000/user');
      await expect(page.locator('[data-testid="current-page"]')).toContainText('UserPage');

      // Test back navigation
      await page.goBack();
      await page.waitForURL('**/auction/1');
      await expect(page).toHaveURL('http://localhost:3000/auction/1');
      await expect(page.locator('[data-testid="current-page"]')).toContainText('AuctionPage');
      
      // Go back to HomePage
      await page.goBack();
      await page.waitForLoadState('networkidle', { timeout: 1000 });
      await page.waitForSelector('[data-testid="current-page"]');
      await expect(page).toHaveURL('http://localhost:3000/home');
      await expect(page.locator('[data-testid="current-page"]')).toContainText('HomePage');
      
      // Navigate to auction from HomePage
      await expect(page.locator('[data-testid="auction-one-access"]')).toBeChecked();
      await page.click('button:has-text("Auction One")');
      await page.waitForURL('**/auction/1');
      await expect(page).toHaveURL('http://localhost:3000/auction/1');
      await expect(page.locator('[data-testid="current-page"]')).toContainText('AuctionPage');
      
      await page.goBack();
      await page.waitForURL('**/home');
      await expect(page).toHaveURL('http://localhost:3000/home');
      await expect(page.locator('[data-testid="current-page"]')).toContainText('HomePage');

      // Test back navigation is enough to verify history
      await expect(page).toHaveURL('http://localhost:3000/home');
      await expect(page.locator('[data-testid="current-page"]')).toContainText('HomePage');
    });
  });

  test.describe('Access Control', () => {
    test('auction access revocation while viewing', async () => {
      // Login and navigate to auction 1
      await page.click('[data-testid="login-button"]');
      await page.waitForURL('**/home');
      await expect(page).toHaveURL('http://localhost:3000/home');
      
      await page.click('button:has-text("Auction One")');
      await page.waitForURL('**/auction/1');
      await expect(page).toHaveURL('http://localhost:3000/auction/1');
      await expect(page.locator('[data-testid="current-page"]')).toContainText('AuctionPage');

      // Revoke access
      await page.click('[data-testid="auction-one-access"]');
      
      // Should be redirected to home
      await page.waitForURL('**/home');
      await expect(page).toHaveURL('http://localhost:3000/home');
      await expect(page.locator('[data-testid="current-page"]')).toContainText('HomePage');

      // Try going back
      await page.goBack();
      await expect(page).toHaveURL('http://localhost:3000/home'); // Should still be home
      await expect(page.locator('[data-testid="current-page"]')).toContainText('HomePage');

      // No forward history should exist
      await page.goForward();
      await expect(page).toHaveURL('http://localhost:3000/home');
      await expect(page.locator('[data-testid="current-page"]')).toContainText('HomePage');
    });

    test('auction access prevention', async () => {
      // Login
      await page.click('[data-testid="login-button"]');
      await page.waitForURL('**/home');
      await expect(page).toHaveURL('http://localhost:3000/home');
      await expect(page.locator('[data-testid="current-page"]')).toContainText('HomePage');
      
      // Verify auction 2 access is initially disabled
      await expect(page.locator('[data-testid="auction-two-access"]')).not.toBeChecked();
      
      // Wait for HomePage to be visible
      await expect(page.locator('[data-testid="current-page"]')).toContainText('HomePage');
      
      // Verify auction 2 button is disabled
      const auctionTwoButton = page.locator('button:has-text("Auction Two")');
      await expect(auctionTwoButton).toBeDisabled();
      
      // Should stay on home page
      await expect(page).toHaveURL('http://localhost:3000/home');
      await expect(page.locator('[data-testid="current-page"]')).toContainText('HomePage');
    });
  });

  // for now not working
  test.skip('Authentication Handling', () => {
    test('logout protection', async () => {
      // Login and navigate to user page
      await page.click('[data-testid="login-button"]');
      await page.waitForURL('**/home');
      await expect(page).toHaveURL('http://localhost:3000/home');
      
      await page.click('button:has-text("User")');
      await page.waitForURL('**/user');
      await expect(page).toHaveURL('http://localhost:3000/user');
      
      // Go back to home first
      await page.goBack();
      await page.waitForURL('**/home');
      await expect(page.locator('[data-testid="current-page"]')).toContainText('HomePage');

      // Test repeated back navigation - should show dialog each time
      for (let i = 0; i < 4; i++) {
        const dialogPromise = page.waitForEvent('dialog');
        await page.goBack(); // This will show the dialog
        const dialog = await dialogPromise;
        expect(dialog.message()).toBe('Log out?');
        await dialog.dismiss(); // Cancel navigation
        await expect(page).toHaveURL(`${baseUrl}/home`);
        await expect(page.locator('[data-testid="current-page"]')).toContainText('HomePage');
      }

      // Test rapid back navigation bypass by directly manipulating history
      await page.evaluate(() => {
        // Simulate rapid back clicks by directly calling history.back()
        window.history.back();
        window.history.back();
      });

      // Wait for the reload that should happen
      await page.waitForNavigation({ waitUntil: 'load' });
      
      // Should be at login page
      await expect(page).toHaveURL(`${baseUrl}/`);
      await expect(page.locator('[data-testid="current-page"]')).toContainText('LoginPage');

      // Try going forward - should stay at login
      await page.goForward();
      await expect(page).toHaveURL(`${baseUrl}/`);
      await expect(page.locator('[data-testid="current-page"]')).toContainText('LoginPage');

      // Login again and try forward navigation
      await page.click('[data-testid="login-button"]');
      await page.waitForURL('**/home');
      await expect(page).toHaveURL('http://localhost:3000/home');
      await page.click('button:has-text("User")');
      await page.waitForURL('**/user');
      await expect(page).toHaveURL('http://localhost:3000/user');

      // Go back twice with cancel
      await page.goBack();
      await expect(page).toHaveURL('http://localhost:3000/home');
      const secondDismissDialogPromise = page.waitForEvent('dialog');
      await page.goBack();
      const secondDismissDialog = await secondDismissDialogPromise;
      expect(secondDismissDialog.message()).toBe('Log out?');
      await secondDismissDialog.dismiss();
      await expect(page).toHaveURL('http://localhost:3000/home');

      // Go forward - should go to /user
      await page.goForward();
      await expect(page).toHaveURL('http://localhost:3000/user');

      // Finally accept logout
      const acceptDialogPromise = page.waitForEvent('dialog');
      await page.goBack();
      await expect(page).toHaveURL('http://localhost:3000/home');
      await page.goBack();
      const acceptDialog = await acceptDialogPromise;
      expect(acceptDialog.message()).toBe('Log out?');
      await acceptDialog.accept();
      await page.waitForURL('**/');
      await expect(page).toHaveURL('http://localhost:3000/');
      await expect(page.locator('[data-testid="current-page"]')).toContainText('LoginPage');
    });

    test('direct URL access while logged in', async () => {
      // Login first
      await page.click('[data-testid="login-button"]');
      await page.waitForURL('**/home');
      await expect(page).toHaveURL('http://localhost:3000/home');
      
      // Try accessing URL directly
      await page.goto('http://localhost:3000/user');
      await page.waitForURL('**/');
      await expect(page).toHaveURL('http://localhost:3000/');
      await expect(page.locator('[data-testid="current-page"]')).toContainText('LoginPage');
    });
  });

  test.describe('Invalid URL Handling', () => {
    test('invalid URLs trigger reload to login', async () => {
      const testUrls = [
        '/nonexistent',
        '/invalid/path',
        '/auction/999',
        '/random-text',
      ];

      for (const url of testUrls) {
        await test.step(`testing invalid URL: ${url}`, async () => {
          const formattedUrl = url.startsWith('/') ? url : `/${url}`;
          await page.goto(`http://localhost:3000${formattedUrl}`);
          await page.waitForURL('**/');
          await expect(page).toHaveURL('http://localhost:3000/');
          await expect(page.locator('[data-testid="current-page"]')).toContainText('LoginPage');
        });
      }
    });

    test('invalid URLs while logged in trigger reload', async () => {
      // Login first
      await page.click('[data-testid="login-button"]');
      await page.waitForURL('**/home');
      await expect(page).toHaveURL('http://localhost:3000/home');
      await expect(page.locator('[data-testid="current-page"]')).toContainText('HomePage');
      
      const testUrls = [
        '/nonexistent',
        '/invalid/path',
        '/auction/999',
        '/random-text',
      ];

      for (const url of testUrls) {
        await test.step(`testing invalid URL while logged in: ${url}`, async () => {
          const formattedUrl = url.startsWith('/') ? url : `/${url}`;
          await page.goto(`http://localhost:3000${formattedUrl}`);
          await page.waitForURL('**/');
          await expect(page).toHaveURL('http://localhost:3000/');
          await expect(page.locator('[data-testid="current-page"]')).toContainText('LoginPage');
        });
      }
    });
  });

  test.describe('Browser Specific', () => {
    test('history state preserved after refresh', async () => {
      // Build up some history
      await page.click('[data-testid="login-button"]');
      await page.waitForURL('**/home');
      await expect(page).toHaveURL('http://localhost:3000/home');
      
      await page.click('button:has-text("User")');
      await page.waitForURL('**/user');
      await expect(page).toHaveURL('http://localhost:3000/user');
      
      // Wait for UserPage to be visible before navigating to auction
      await page.waitForSelector('[data-testid="current-page"]');
      await expect(page.locator('[data-testid="current-page"]')).toContainText('UserPage');
      
      // Go back to HomePage to access auction
      await page.goBack();
      await page.waitForLoadState('networkidle');
      await page.waitForSelector('[data-testid="current-page"]');
      await expect(page.locator('[data-testid="current-page"]')).toContainText('HomePage');
      
      // Verify auction access and click button
      const auctionOneAccess = page.locator('[data-testid="auction-one-access"]');
      await expect(auctionOneAccess).toBeChecked();
      
      const auctionOneButton = page.locator('button', { hasText: 'Auction One' });
      await expect(auctionOneButton).toBeEnabled();
      await auctionOneButton.click();
      
      await page.waitForURL('**/auction/1');
      await expect(page).toHaveURL('http://localhost:3000/auction/1');
      await expect(page.locator('[data-testid="current-page"]')).toContainText('AuctionPage');
      
      // Refresh the page
      await page.reload();
      await page.waitForURL('**/');
      
      // Should be at login
      await expect(page).toHaveURL('http://localhost:3000/');
      await expect(page.locator('[data-testid="current-page"]')).toContainText('LoginPage');
      
      // Login again and verify we can't access previous history
      await page.click('[data-testid="login-button"]');
      await page.waitForURL('**/home');
      await expect(page).toHaveURL('http://localhost:3000/home');
      await expect(page.locator('[data-testid="current-page"]')).toContainText('HomePage');
      
      await page.goBack();
      await page.waitForURL('**/home');
      await expect(page).toHaveURL('http://localhost:3000/home');
      await expect(page.locator('[data-testid="current-page"]')).toContainText('HomePage');
    });
  });
});