import {makeAutoObservable} from 'mobx';
import {
    AuctionRowElement,
    CompanyElement,
    CounterpartyCreditElement,
    DeAuctionValue,
    PageName,
    SessionUserValue,
    TimeValue,
    UserElement
} from '../types/generated';

interface DomainStore {
    auction_rows: AuctionRowElement[]
    companies: CompanyElement[]
    counterparty_credits: CounterpartyCreditElement[]
    de_auction: DeAuctionValue | null
    session_user: SessionUserValue | null
    time: TimeValue | null
    users: UserElement[]
    seconds_since_last_message_received: number
}

export const domainStore = makeAutoObservable({
    auction_rows: [] as AuctionRowElement[],
    companies: [] as CompanyElement[],
    counterparty_credits: [] as CounterpartyCreditElement[],
    de_auction: null as DeAuctionValue | null,
    session_user: null as SessionUserValue | null,
    time: null as TimeValue | null,
    users: [] as UserElement[],
    seconds_since_last_message_received: 0,

    setAuctionRows(rows: AuctionRowElement[]) {
        this.auction_rows = rows;
    },

    setCompanies(companies: CompanyElement[]) {
        this.companies = companies;
    },

    setCounterpartyCreditElements(credits: CounterpartyCreditElement[]) {
        this.counterparty_credits = credits;
    },

    setDeAuction(auction: DeAuctionValue | null) {
        this.de_auction = auction;
    },

    setSessionUser(user: SessionUserValue | null) {
        this.session_user = user;
    },

    setCurrentPage(page: PageName) {
        if(this.session_user)
           this.session_user.current_page = page;
    },

    setTime(time: TimeValue | null) {
        this.time = time;
    },

    setUsers(users: UserElement[]) {
        this.users = users;
    },

    reset() {
        this.auction_rows = [];
        this.companies = [];
        this.counterparty_credits = [];
        this.de_auction = null;
        this.session_user = null;
        this.time = null;
        this.users = [];
        this.seconds_since_last_message_received = 0;
    },

    patch(updates: Partial<IDomainState>) {
        Object.assign(this, updates);
    }

} satisfies DomainStore & Record<string, any>);
