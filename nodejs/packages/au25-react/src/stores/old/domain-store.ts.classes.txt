import { makeAutoObservable } from 'mobx';
import { 
    AuctionRowElement, 
    CompanyElement, 
    CounterpartyCreditElement, 
    DeAuctionValue, 
    SessionUserValue, 
    TimeValue, 
    UserElement 
} from '../types/generated';

export class DomainStore {
    auction_rows: AuctionRowElement[] = [];
    companies: CompanyElement[] = [];
    counterparty_credits: CounterpartyCreditElement[] = [];
    de_auction: DeAuctionValue | null = null;
    session_user: SessionUserValue | null = null;
    time: TimeValue | null = null;
    users: UserElement[] = [];
    seconds_since_last_message_received: number = 0;

    constructor() {
        makeAutoObservable(this);
    }

    setAuctionRows(rows: AuctionRowElement[]) {
        this.auction_rows = rows;
    }

    setCompanies(companies: CompanyElement[]) {
        this.companies = companies;
    }

    setCounterpartyCreditElements(credits: CounterpartyCreditElement[]) {
        this.counterparty_credits = credits;
    }

    setDeAuction(auction: DeAuctionValue | null) {
        this.de_auction = auction;
    }

    setSessionUser(user: SessionUserValue | null) {
        this.session_user = user;
    }

    setTime(time: TimeValue | null) {
        this.time = time;
    }

    setUsers(users: UserElement[]) {
        this.users = users;
    }

    reset() {
        this.auction_rows = [];
        this.companies = [];
        this.counterparty_credits = [];
        this.de_auction = null;
        this.session_user = null;
        this.time = null;
        this.users = [];
        this.seconds_since_last_message_received = 0;
    }
}

export const domainStore = new DomainStore();
