// noinspection JSUnusedGlobalSymbols

import { proxy } from 'valtio';
import { devtools } from 'valtio/utils';
import type {} from '@redux-devtools/extension';
import {
    AuctionRowElement, AuUserRole,
    CompanyElement,
    CounterpartyCreditElement,
    DeAuctionValue,
    PageName,
    SessionUserValue,
    TimeValue,
    UserElement
} from '@/types/generated';

interface DomainStore {
    auction_rows: AuctionRowElement[]
    companies: CompanyElement[]
    counterparty_credits: CounterpartyCreditElement[]
    de_auction: DeAuctionValue | null
    session_user: SessionUserValue | null
    time: TimeValue | null
    users: UserElement[]
    seconds_since_last_message_received: number

    // Methods
    setAuctionRows: (rows: AuctionRowElement[]) => void
    setCompanies: (companies: CompanyElement[]) => void
    setCounterpartyCreditElements: (credits: CounterpartyCreditElement[]) => void
    setDeAuction: (auction: DeAuctionValue | null) => void
    setSessionUser: (user: SessionUserValue | null) => void
    toggleSessionUserRole: () => void
    setCurrentPage: (page: PageName) => void
    setTime: (time: TimeValue | null) => void
    setUsers: (users: UserElement[]) => void
    reset: () => void
    batchUpdate: (updates: Partial<DomainStore>) => void
}

// Singleton instance
let storeInstance: DomainStore | null = null;

const createStore = (initialState: Partial<DomainStore> = {}) => {
    if (storeInstance) {
        return storeInstance;
    }

    const store = proxy<DomainStore>({
        // Default state
        auction_rows: [],
        companies: [],
        counterparty_credits: [],
        de_auction: null,
        session_user: null,
        time: null,
        users: [],
        seconds_since_last_message_received: 0,

        // Methods
        setAuctionRows(rows) {
            this.auction_rows = rows;
        },

        setCompanies(companies) {
            this.companies = companies;
        },

        setCounterpartyCreditElements(credits) {
            this.counterparty_credits = credits;
        },

        setDeAuction(auction) {
            this.de_auction = auction;
        },

        setSessionUser(user) {
            // Only set default user if explicitly requested
            this.session_user = user;
        },

        setCurrentPage(page) {
            if (this.session_user) {
                this.session_user.current_page = page;
            }
        },

        toggleSessionUserRole(){
            if (this.session_user) {
                this.session_user.isAuctioneer = !this.session_user.isAuctioneer;
                if(this.session_user.role == AuUserRole.AUCTIONEER){
                    this.session_user.role = AuUserRole.TRADER;
                } else {
                    this.session_user.role = AuUserRole.AUCTIONEER
                }
            }
        },

        setTime(time) {
            this.time = time;
        },

        setUsers(users) {
            this.users = users;
        },

        reset() {
            this.auction_rows = [];
            this.companies = [];
            this.counterparty_credits = [];
            this.de_auction = null;
            this.session_user = null;
            this.time = null;
            this.users = [];
            this.seconds_since_last_message_received = 0;
        },

        batchUpdate(updates) {
            // Use a transaction to batch multiple updates
            // Batch updates in next microtask to avoid cascading renders
            queueMicrotask(() => {
                if ('auction_rows' in updates && updates.auction_rows !== undefined) {
                    this.auction_rows = updates.auction_rows;
                }
                if ('companies' in updates && updates.companies !== undefined) {
                    this.companies = updates.companies;
                }
                if ('counterparty_credits' in updates && updates.counterparty_credits !== undefined) {
                    this.counterparty_credits = updates.counterparty_credits;
                }
                if ('de_auction' in updates) {
                    this.de_auction = updates.de_auction ?? null;
                }
                if ('session_user' in updates) {
                    this.session_user = updates.session_user ?? null;
                }
                if ('time' in updates) {
                    this.time = updates.time ?? null;
                }
                if ('users' in updates && updates.users !== undefined) {
                    this.users = updates.users;
                }
                if ('seconds_since_last_message_received' in updates &&
                    updates.seconds_since_last_message_received !== undefined) {
                    this.seconds_since_last_message_received = updates.seconds_since_last_message_received;
                }
            });
        },

        // Override with any initial state
        ...initialState
    });

    // Register devtools only once
    if (process.env.NODE_ENV === 'development') {
        devtools(store, { name: 'Domain Store' });
    }

    storeInstance = store;
    return store;
};

// Create the singleton store
const store = createStore();

// For testing purposes only
export const __resetStoreForTesting = () => {
    storeInstance = null;
};

export const getDomainStore = () => store;

// Export type
export type { DomainStore };
