import { useState } from 'react';

const AuctionTutorial1 = () => {
    const [currentSlide, setCurrentSlide] = useState(0);

    const slides = [
        {
            title: "Double-Sided Clock Auction",
            content: (
                <div className="space-y-4">
                    <h1 className="text-4xl font-bold mb-8">How the Auction Works</h1>
                    <p className="text-xl">Click through this tutorial to learn:</p>
                    <ul className="text-lg space-y-2">
                        <li>• Basic auction structure</li>
                        <li>• Price movement rules</li>
                        <li>• Bidding constraints</li>
                        <li>• How the auction ends</li>
                    </ul>
                </div>
            )
        },
        {
            title: "Round Structure",
            content: (
                <div className="space-y-6">
                    <div className="bg-blue-100 p-6 rounded-lg">
                        <h2 className="text-2xl font-semibold mb-4">Each Round:</h2>
                        <div className="space-y-4">
                            <div className="flex items-center">
                                <div className="w-8 h-8 rounded-full bg-blue-500 text-white flex items-center justify-center mr-3">1</div>
                                <p>Price is announced</p>
                            </div>
                            <div className="flex items-center">
                                <div className="w-8 h-8 rounded-full bg-blue-500 text-white flex items-center justify-center mr-3">2</div>
                                <p>Traders submit quantities to buy or sell</p>
                            </div>
                            <div className="flex items-center">
                                <div className="w-8 h-8 rounded-full bg-blue-500 text-white flex items-center justify-center mr-3">3</div>
                                <p>Total buy and sell volumes are compared</p>
                            </div>
                        </div>
                    </div>
                </div>
            )
        },
        {
            title: "Price Movement",
            content: (
                <div className="space-y-6">
                    <div className="grid grid-cols-2 gap-8">
                        <div className="bg-green-100 p-6 rounded-lg">
                            <h3 className="text-xl font-semibold mb-4">When Buy > Sell</h3>
                            <div className="flex items-center justify-center">
                                <svg className="w-24 h-24" viewBox="0 0 24 24">
                                    <path fill="currentColor" d="M4 12l1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8-8 8z"/>
                                </svg>
                            </div>
                            <p className="mt-4 text-center">Price increases next round</p>
                        </div>
                        <div className="bg-red-100 p-6 rounded-lg">
                            <h3 className="text-xl font-semibold mb-4">When Sell > Buy</h3>
                            <div className="flex items-center justify-center">
                                <svg className="w-24 h-24" viewBox="0 0 24 24">
                                    <path fill="currentColor" d="M20 12l-1.41-1.41L13 16.17V4h-2v12.17l-5.58-5.59L4 12l8 8 8-8z"/>
                                </svg>
                            </div>
                            <p className="mt-4 text-center">Price decreases next round</p>
                        </div>
                    </div>
                </div>
            )
        },
        {
            title: "Bidding Rules",
            content: (
                <div className="space-y-6">
                    <div className="grid grid-cols-2 gap-8">
                        <div className="bg-blue-50 p-6 rounded-lg">
                            <h3 className="text-xl font-semibold mb-4">Buy Orders</h3>
                            <ul className="space-y-3">
                                <li>❌ Cannot increase quantity at higher prices</li>
                                <li>❌ Cannot decrease quantity at lower prices</li>
                            </ul>
                        </div>
                        <div className="bg-blue-50 p-6 rounded-lg">
                            <h3 className="text-xl font-semibold mb-4">Sell Orders</h3>
                            <ul className="space-y-3">
                                <li>❌ Cannot increase quantity at lower prices</li>
                                <li>❌ Cannot decrease quantity at higher prices</li>
                            </ul>
                        </div>
                    </div>
                </div>
            )
        },
        {
            title: "Auction Ending",
            content: (
                <div className="space-y-6">
                    <div className="grid grid-cols-2 gap-8">
                        <div className="bg-green-50 p-6 rounded-lg">
                            <h3 className="text-xl font-semibold mb-4">Equal Activity</h3>
                            <p>Auction ends when buy quantity equals sell quantity</p>
                            <div className="mt-4 flex justify-center">
                                <div className="w-32 h-32 relative">
                                    <div className="absolute inset-0 flex items-center justify-center">
                                        <div className="text-4xl">=</div>
                                    </div>
                                    <div className="absolute top-0 left-0 w-16 h-16 bg-blue-200 rounded flex items-center justify-center">Buy</div>
                                    <div className="absolute bottom-0 right-0 w-16 h-16 bg-blue-200 rounded flex items-center justify-center">Sell</div>
                                </div>
                            </div>
                        </div>
                        <div className="bg-yellow-50 p-6 rounded-lg">
                            <h3 className="text-xl font-semibold mb-4">Price Reversal</h3>
                            <p>Auction ends when activity ratio inverts (e.g., buy > sell becomes buy < sell)</p>
                            <div className="mt-4 flex justify-center">
                                <div className="w-32 h-32 relative">
                                    <div className="absolute inset-0 flex items-center justify-center">
                                        <svg className="w-12 h-12" viewBox="0 0 24 24">
                                            <path fill="currentColor" d="M16 17.01V10h-2v7.01h-3L15 21l4-3.99h-3zM9 3L5 6.99h3V14h2V6.99h3L9 3z"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )
        }
    ];

    return (
        <div className="min-h-screen bg-white p-8">
            <div className="max-w-4xl mx-auto">
                <div className="bg-white rounded-lg shadow-lg p-8 min-h-[600px]">
                    <div className="mb-8">
                        {slides[currentSlide].content}
                    </div>
                    <div className="flex justify-between items-center mt-8">
                        <button
                            onClick={() => setCurrentSlide(Math.max(0, currentSlide - 1))}
                            disabled={currentSlide === 0}
                            className="px-4 py-2 rounded bg-blue-500 text-white hover:bg-blue-600 disabled:bg-gray-200 disabled:text-gray-400"
                        >
                            Previous
                        </button>
                        <div className="text-sm text-gray-500">
                            {currentSlide + 1} / {slides.length}
                        </div>
                        <button
                            onClick={() => setCurrentSlide(Math.min(slides.length - 1, currentSlide + 1))}
                            disabled={currentSlide === slides.length - 1}
                            className="px-4 py-2 rounded bg-blue-500 text-white hover:bg-blue-600 disabled:bg-gray-200 disabled:text-gray-400"
                        >
                            Next
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AuctionTutorial1;
