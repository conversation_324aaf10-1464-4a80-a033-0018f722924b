import { 
  Responsive<PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  Line 
} from 'recharts';

// Generate sample data for the auction
const generateAuctionData = () => {
  return [
    { price: 20, buyVolume: 100, sellVolume: 20 },
    { price: 22, buyVolume: 90, sellVolume: 40 },
    { price: 24, buyVolume: 75, sellVolume: 60 },
    { price: 26, buyVolume: 55, sellVolume: 75 },
    { price: 28, buyVolume: 40, sellVolume: 90 },
    { price: 30, buyVolume: 25, sellVolume: 110 },
    { price: 32, buyVolume: 15, sellVolume: 125 },
    { price: 34, buyVolume: 8, sellVolume: 140 },
    { price: 36, buyVolume: 4, sellVolume: 155 },
    { price: 38, buyVolume: 2, sellVolume: 170 },
    { price: 40, buyVolume: 0, sellVolume: 180 }
  ];
};

const HaikuChart = () => {
  const auctionData = generateAuctionData();

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div 
          style={{ 
            background: 'white', 
            border: '1px solid #ccc', 
            padding: '10px' 
          }}
        >
          <p>{`Price: ${label} cpp`}</p>
          <p style={{ color: 'green' }}>
            {`Buy Volume: ${payload[0].value} MMlb`}
          </p>
          <p style={{ color: 'red' }}>
            {`Sell Volume: ${payload[1].value} MMlb`}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div style={{ width: '100%', height: 400 }}>
      <ResponsiveContainer>
        <LineChart
          data={auctionData}
          margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
        >
          <XAxis 
            dataKey="price" 
            label={{ 
              value: 'Price (cpp)', 
              position: 'insideBottom', 
              offset: -5 
            }} 
          />
          <YAxis 
            label={{ 
              value: 'Volume (MMlb)', 
              angle: -90, 
              position: 'insideLeft' 
            }} 
          />
          
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          
          <Line
            type="step"
            dataKey="buyVolume"
            stroke="green"
            strokeWidth={3}
            name="Buy Volume"
            dot={false}
          />
          
          <Line
            type="step"
            dataKey="sellVolume"
            stroke="red"
            strokeWidth={3}
            name="Sell Volume"
            dot={false}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

export default HaikuChart;
