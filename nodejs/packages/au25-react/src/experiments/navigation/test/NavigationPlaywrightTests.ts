import { test, expect, type Page, BrowserContext } from '@playwright/test';

// Helper to handle beforeunload dialogs
async function handleBeforeUnload(context: BrowserContext) {
    context.on('dialog', async dialog => {
        expect(dialog.type()).toBe('beforeunload');
        await dialog.accept();
    });
}

async function login(page: Page) {
    await page.getByRole('button', { name: 'Log In' }).click();
    await expect(page).toHaveURL('/home');
}

test.describe('Navigation Requirements', () => {
    test.beforeEach(async ({ page }) => {
        await page.goto('/');
    });

    test('enforces authentication for protected routes', async ({ page }) => {
        // Direct URL attempt
        await page.goto('/home');
        await expect(page).toHaveURL('/');

        // Login and verify access
        await login(page);
        await expect(page).toHaveURL('/home');

        // Logout and verify redirect
        await page.getByRole('button', { name: 'Log Out' }).click();
        await expect(page).toHaveURL('/');
    });

    test('handles browser back/forward buttons', async ({ page }) => {
        // Navigate forward
        await login(page);
        await page.getByRole('button', { name: 'Go to About' }).click();
        await expect(page).toHaveURL('/about');

        // Go back
        await page.goBack();
        await expect(page).toHaveURL('/home');

        // Go forward
        await page.goForward();
        await expect(page).toHaveURL('/about');
    });

    test('handles bookmark restoration', async ({ page, context }) => {
        // Create bookmark scenario
        await login(page);
        await page.goto('/about');
        const bookmarkUrl = page.url();

        // New session (simulating bookmark use)
        const newPage = await context.newPage();
        await newPage.goto(bookmarkUrl);
        await expect(newPage).toHaveURL('/'); // Should redirect to login

        // After login should not return to bookmarked page (server controls navigation)
        await login(newPage);
        await expect(newPage).toHaveURL('/home');
    });

    test('shows page exit warning', async ({ page, context }) => {
        await login(page);

        // Setup dialog handler
        await handleBeforeUnload(context);

        // Trigger beforeunload (simulating page close)
        await page.evaluate(() => window.dispatchEvent(new Event('beforeunload')));

        // Verify dialog was shown (handled by beforeEach handler)
    });

    test('handles navigation interruption', async ({ page }) => {
        await login(page);

        // Attempt to navigate while "server" rejects
        // This requires custom event to simulate server rejection
        await page.evaluate(() => {
            window.dispatchEvent(new CustomEvent('navigationRejected'));
        });

        // Should stay on current page
        await expect(page).toHaveURL('/home');
    });

    test('handles concurrent navigation attempts', async ({ page }) => {
        await login(page);

        // Simulate rapid navigation attempts
        await Promise.all([
            page.getByRole('button', { name: 'Go to About' }).click(),
            page.getByRole('button', { name: 'Back to Home' }).click(),
            page.getByRole('button', { name: 'Go to About' }).click(),
        ]);

        // Wait for navigation to settle
        await page.waitForTimeout(100);

        // Should be in a valid state (either /home or /about)
        const url = page.url();
        expect(['/home', '/about']).toContain(url);
    });

    test('handles page reload', async ({ page }) => {
        await login(page);
        await page.getByRole('button', { name: 'Go to About' }).click();

        // Reload page
        await page.reload();

        // Should redirect to login page as state is lost
        await expect(page).toHaveURL('/');
    });

    test('manages history stack correctly', async ({ page }) => {
        await login(page);

        // Create several history entries
        await page.getByRole('button', { name: 'Go to About' }).click();
        await page.getByRole('button', { name: 'Back to Home' }).click();
        await page.getByRole('button', { name: 'Go to About' }).click();

        // Go back multiple times
        await page.goBack();
        await expect(page).toHaveURL('/home');
        await page.goBack();
        await expect(page).toHaveURL('/about');

        // Forward should work
        await page.goForward();
        await expect(page).toHaveURL('/home');
    });

    test('handles external redirects', async ({ page }) => {
        await login(page);

        // Simulate server forcing navigation
        await page.evaluate(() => {
            window.dispatchEvent(new CustomEvent('forceNavigation', {
                detail: { path: '/home' }
            }));
        });

        await expect(page).toHaveURL('/home');
    });
});

// Additional tests for error cases
test.describe('Error Handling', () => {
    test('handles network errors during navigation', async ({ page }) => {
        await login(page);

        // Simulate offline state
        await page.route('**/*', route => route.abort());

        await page.getByRole('button', { name: 'Go to About' }).click();

        // Should stay on current page or show error state
        await expect(page).toHaveURL('/home');
    });

    test('handles invalid routes', async ({ page }) => {
        await login(page);

        // Try to navigate to non-existent route
        await page.goto('/invalid-route');

        // Should redirect to safe state
        await expect(page).toHaveURL('/home');
    });
});
