const isLoading = ref(false);
const connector = ref<any>(null);

onBeforeMount(() => {
    isLoading.value = true;
    connector.value = Connector.create({
        url: 'ws://localhost:4040/socket/',
    });

    connector.value.connect().then(() => {
        isLoading.value = false;
    }).catch((error: any) => {
        console.error('Connection error:', error);
        isLoading.value = false;
    });
});

// Replace your existing Spin component in the template with:
// <n-spin :show="isLoading" style="position: absolute; inset: 0; z-index: 10" />
