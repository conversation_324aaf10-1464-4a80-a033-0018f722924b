import type {
    CompanyElement, UserElement, SessionUserValue, DeAuctionValue,
    AuctionRowElement, CounterpartyCreditElement, TimeValue
} from "au25-connector";

export interface LiveClientStoreConfig {
    // High-level controls
    scenario?: 'empty' | 'trader_logged_in' | 'auctioneer_setup' | 'auction_in_progress' | 'auction_closed';
    numCompanies?: number;
    numTraders?: number; // Users with role TRADER
    numAuctioneers?: number; // Users with role AUCTIONEER
    loggedInUserId?: string; // ID of the user for session_user
    activeAuctionId?: string; // Which auction is currently selected/active

    // Specific Counts / Generation Flags
    numGenericAuctions?: number; // For auction_rows list
    generateCredits?: boolean | 'all_pairs'; // Generate counterparty credits?

    // Direct overrides (using Partial for flexibility)
    // Use arrays for collections to allow overriding specific items by index or matching ID
    companyOverrides?: Partial<CompanyElement>[];
    userOverrides?: Partial<UserElement>[];
    sessionUserOverrides?: Partial<SessionUserValue>;
    deAuctionOverrides?: Partial<DeAuctionValue>; // Allows deep overrides (handle with care in factory)
    auctionRowOverrides?: Partial<AuctionRowElement>[];
    counterpartyCreditOverrides?: Partial<CounterpartyCreditElement>[];
    timeOverride?: Partial<TimeValue>;

    // Configuration for nested generation within DeAuction
    deAuctionConfig?: {
        maxRound?: number; // Controls how many rounds are generated in the blotter
        // Add other DeAuction specific generation flags if needed
    }
}
