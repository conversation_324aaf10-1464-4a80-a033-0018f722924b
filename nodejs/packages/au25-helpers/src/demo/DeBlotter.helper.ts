import type { DeBlotter, DeRoundElement, DeRoundTraderElement, CompanyElement } from "au25-connector";
import { createTest__DeTraderElement } from './DeTraderElement.helper'; // Added imports for helpers
import { createTest__DeRoundElement } from './DeRoundElement.helper';
import { createTest__DeRoundTraderElement } from './DeRoundTraderElement.helper';


export function createTest__DeBlotter(
    companies: CompanyElement[],
    maxRound: number = 0, // The highest round number completed or in progress
    overrides: Partial<DeBlotter> = {}
): DeBlotter {

    const traders = companies.map(c => createTest__DeTraderElement(c));

    const rounds: DeRoundElement[] = [];
    const round_traders: DeRoundTraderElement[] = [];

    for (let r = 1; r <= maxRound; r++) {
        rounds.push(createTest__DeRoundElement(r));
        // Create placeholder round trader entries for each company in each past round
        companies.forEach(c => {
            round_traders.push(createTest__DeRoundTraderElement(r, c));
        });
    }

    const defaults: DeBlotter = {
        traders: traders,
        rounds: rounds,
        round_traders: round_traders,
    };

    // Simple merge - won't deep merge arrays correctly if overrides provide partial arrays
    return {
       traders: overrides.traders ?? defaults.traders,
       rounds: overrides.rounds ?? defaults.rounds,
       round_traders: overrides.round_traders ?? defaults.round_traders,
    };
}
