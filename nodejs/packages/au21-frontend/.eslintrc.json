{"root": true, "ignorePatterns": ["**/*"], "plugins": ["@nrwl/nx"], "overrides": [{"files": ["*.ts", "*.tsx", "*.js", "*.jsx"], "rules": {"@nrwl/nx/enforce-module-boundaries": ["error", {"enforceBuildableLibDependency": true, "allow": [], "depConstraints": [{"sourceTag": "*", "onlyDependOnLibsWithTags": ["*"]}]}]}}, {"files": ["*.ts", "*.tsx"], "extends": ["plugin:@nrwl/nx/typescript"], "parserOptions": {"project": "./tsconfig.*?.json"}, "rules": {}}, {"files": ["*.js", "*.jsx"], "extends": ["plugin:@nrwl/nx/javascript"], "rules": {}}]}