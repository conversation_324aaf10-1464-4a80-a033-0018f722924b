<template>
  <div class="DivTableCell" :style="computedStyle">
    <slot v-if="$parent"/>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'

@Component({})
export default class DivTableCell extends Vue {
  get width (): (column) => number {
    return (this.$parent as any).getWidthForSlot(this)
  }

  get height (): any[] {
    return (this.$parent as any).height
  }

  get computedStyle () {
    return {
      width: this.width + 'px',
      height: this.height + 'px',
    }
  }
}
</script>

<style lang="less">
.DivTableCell {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: right;
  transition: background-color ease .3s;
}
</style>
