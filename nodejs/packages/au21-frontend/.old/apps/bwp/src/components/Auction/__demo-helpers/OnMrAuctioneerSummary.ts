import { OnMrAuctioneerSummary } from '../../../_generated/server_outputs'

let counter = 0
export const createOnMrAuctioneerSummary = () => ({
  AUCTIONID: ++counter,
  BIDS_CURRENT_ROUND: '1',
  BIDS_FIRST_ROUND: '1',
  BIDS_PREV_ROUND: '',
  PAGE: 'MRAuctioneerPage',
  STOP_VOLUME_LABEL: '< 29',
  VOLUME_CURRENT_ROUND: '399',
  VOLUME_POTENTIAL: '300,000',
  VOLUME_PREVIOUS_ROUND: '',
  VOLUME_REDUCED: '0',
} as OnMrAuctioneerSummary)
