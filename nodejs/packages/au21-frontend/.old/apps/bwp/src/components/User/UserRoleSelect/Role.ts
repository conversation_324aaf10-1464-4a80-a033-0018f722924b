import { Role } from '../../../_generated/bwp-enums'

const roles: Role[] = [
  Role.TRADER,
  Role.INTERNAL_OBSERVER,
  Role.AUCTIONEER,
  Role.ADMIN,
]

const roleToOptionNameMap: { [R in Role]: string } = {
  [Role.TRADER]: 'Trader',
  [Role.INTERNAL_OBSERVER]: 'Internal observer',
  [Role.EXTERNAL_OBSERVER]: 'External observer',
  [Role.AUCTIONEER]: 'Auctioneer',
  [Role.ADMIN]: 'Administrator',
}

export const getRoleName = (role: Role) => roleToOptionNameMap[role]

export const getRoleOptions = () => roles.map(
  role => ({ value: role, name: getRoleName(role) }),
)

export const isAuctioneerOrAdmin = (role: Role) => [Role.AUCTIONEER, Role.ADMIN].includes(role)
