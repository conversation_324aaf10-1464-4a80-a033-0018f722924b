<template>
  <VbDemo>
    <VbCard class="au-page">
      <AuctionStopModeSelect style="width: 200px" v-model="value"/>
    </VbCard>
    <VbCard>
      {{ value }}
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import AuctionStopModeSelect from './AuctionStopModeSelect.vue'
import { MRStopMode } from '../../../_generated/bwp-enums'

export default {
  components: {
    AuctionStopModeSelect,
  },
  data () {
    return {
      value: 'LT' as MRStopMode,
    }
  },
}
</script>
