<template>
  <div class="AuctionTemplatePage form-block" style="overflow-y: auto">
    <div class="au-header">Templates</div>
    <AuctionTemplateTable
      :width="$auLocalStore.config.width_inner + 2"
      :height="templateListHeight"
      @onMrTemplateSelected="selectOnMrTemplate"
    />
    <div class="mt-1 mb-1" style="text-align: right">
      <a-button class="mr-1" size="small" @click="setSample()">Set sample</a-button>
      <a-button class="mr-1" size="small" @click="clear()">Clear</a-button>
      <a-button size="small" @click="createAuction()">Create auction</a-button>
    </div>
    <AuctionEdit
      :style="{height: auctionTemplateHeight + 'px'}"
      :onMrAuctionSettings="onMrAuctionSettings"
    />
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import AuctionTemplateTable
  from './AuctionTemplateTable/AuctionTemplateTable.vue'
import PageLayout from '../../PageLayout/PageLayout.vue'
import AuctionEdit from '../AuctionEdit.vue'
import { createEmptyOnMrAuctionSettings, createSampleAuction } from '../__demo-helpers/OnMrAuctionSettings'
import { bwp_create_auction } from '../../../services/bwp-connector/publisher'
import { OnMrTemplate } from '../../../_generated/server_outputs'
import { MAX_HEIGHT } from '../../../helpers/height_helper'

@Component({
  components: { AuctionEdit, PageLayout, AuctionTemplateTable },
})
export default class AuctionTemplatePage extends Vue {
  onMrAuctionSettings = createEmptyOnMrAuctionSettings()
  templateListMinHeight = 200
  createAuction () {
    bwp_create_auction(this.$auConnector, this.onMrAuctionSettings)
  }

  selectOnMrTemplate (onMrTemplate: OnMrTemplate) {
    this.onMrAuctionSettings = Object.assign(createEmptyOnMrAuctionSettings(), onMrTemplate)
  }

  setSample () {
    this.onMrAuctionSettings = createSampleAuction()
  }

  clear () {
    this.onMrAuctionSettings = createEmptyOnMrAuctionSettings()
  }
  
  get templateListHeight () {
    if (this.$auLocalStore.config.height_inner <= MAX_HEIGHT) {
      return (MAX_HEIGHT - 32) / 3
    }

    return (this.$auLocalStore.config.height_inner - 32) / 3
  }

  get auctionTemplateHeight () {
    if (this.$auLocalStore.config.height_inner <= MAX_HEIGHT) {
      return (MAX_HEIGHT - 32) / 3 * 2
    }

    return (this.$auLocalStore.config.height_inner - 32) / 3 * 2
  }
}
</script>

<style lang="less">
.AuctionTemplatePage {

}
</style>
