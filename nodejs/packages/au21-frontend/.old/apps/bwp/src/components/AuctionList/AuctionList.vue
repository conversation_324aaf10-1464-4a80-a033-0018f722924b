<template>
  <a-card
    :body-style="{padding: '0px', height: height+ 'px', 'overflow-y': 'scroll'}"
    :head-style="{padding: '0px'}"
    class="AuctionsList"
    type="inner"
  >
    <template slot="title">
      {{ title }}
    </template>
    <a-list
      :dataSource="sorted_auctions"
      itemLayout="horizontal"
    >
      <AuctionListItem
        slot="renderItem"
        slot-scope="auction, index"
        :onAuctionRow="auction"
        @select="$emit('select', auction)"
        @hide="$emit('hide', auction)"
        @unhide="$emit('unhide', auction)"
        @remove="$emit('remove', auction)"
      />
    </a-list>
  </a-card>
</template>

<script lang="ts">
import { OnAuctionRow } from '../../_generated/server_outputs'
import { Component, Prop, Vue } from 'vue-property-decorator'
import AuctionListItem from './AuctionListItem.vue'

@Component({
  components: { AuctionListItem },
})
export default class AuctionsList extends Vue {
  @Prop({ type: Number, required: true }) height: number
  @Prop({ type: String, required: true }) title!: string
  @Prop({ type: Array, required: true }) onAuctionRows: OnAuctionRow[]

  get sorted_auctions () {
    const timestamps = this.onAuctionRows.map(a => a.TIMESTAMP)
    
    return this.onAuctionRows.sort((a, b) => {
      // New comes first
      return a.TIMESTAMP < b.TIMESTAMP ? 1 : -1
    })
  }
}
</script>

<style lang="less">
.AuctionsList {
}
</style>
