<template>
  <a-radio-group
    :disabled="disabled"
    class="OperatorSelect"
    v-model="valueProxy"
  >
    <a-radio
      v-for="option in options"
      :key="option.value"
      :value="option.value"
    >
      {{ option.name }}
    </a-radio>
  </a-radio-group>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { Operator } from '../../../_generated/bwp-enums'
import { getOperatorOptions } from '../__demo-helpers/Operator'

@Component({})
export default class OperatorSelect extends Vue {
  @Prop({ type: String }) value: Operator
  @Prop({ type: Boolean }) disabled: boolean
  options = getOperatorOptions()

  get valueProxy (): Operator {
    return this.value
  }

  set valueProxy (value) {
    this.$emit('input', value)
  }
}
</script>

<style lang="less">
.OperatorSelect {

}
</style>
