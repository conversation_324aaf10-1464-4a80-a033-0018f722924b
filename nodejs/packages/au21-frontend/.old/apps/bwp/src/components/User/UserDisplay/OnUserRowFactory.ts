import { OnUserRow } from '../../../_generated/server_outputs'

let counter = 0
export const createOnUserRow = () => {
  return {
    _COUNT: ++counter,
    EVENT: 'createOnUserRow',
    COMPANY: 'Company name' + ++counter,
    DELETED: false,
    EMAIL: '<EMAIL>',
    FIRSTNAME: 'John',
    IS_ONLINE: false,
    LASTNAME: 'Doe',
    MOBILE: '+125363563634',
    PASSWORD: '123456',
    ROLE: 'ADMIN',
    SELECTED: false,
    USERID: ++counter,
    USERNAME: 'username' + ++counter,
    WORK: 'Work something something',

    roleLabel: '', // TODO Enum is needed here.
  } as OnUserRow
}

export const createCleanOnUserRow = () => {
  return {
    _COUNT: 0,
    EVENT: '',
    COMPANY: '',
    DELETED: false,
    EMAIL: '',
    FIRSTNAME: '',
    IS_ONLINE: false,
    LASTNAME: '',
    MO<PERSON><PERSON>: '',
    PASSWORD: '',
    ROLE: null,
    SELECTED: false,
    USERID: 0,
    USERNAME: '',
    WORK: '',

    roleLabel: '', // TODO Enum is needed here.
  } as OnUserRow
}
