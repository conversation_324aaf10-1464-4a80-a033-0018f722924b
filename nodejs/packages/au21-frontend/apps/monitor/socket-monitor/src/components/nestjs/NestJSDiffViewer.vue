<template>
  <div class="heading" style="text-align: left">
    <span class="pad_width"/>
    <span>Server:</span>
    <span class="pad_width"/>
    <span>{{ url }}
    <span class="pad_width"/>
    <span :style="{backgroundColor: isConnected ? 'green' : 'red', color: 'white'}">
        {{ isConnected ? "connected" : "disconnected" }}
      </span>
    </span>
    <div
      style="height: 300px;
         overflow-x: hidden;
         overflow-y: scroll;
        border: 1px solid black"
    >
      <table>
        <thead>
        <tr>
          <th style="width:150px">Id</th>
          <th>Name</th>
          <th>Command Parameters</th>
          <th>From Session</th>
          <th>Is Different</th>
        </tr>
        </thead>
        <tr
          v-for="command in commands"
          :style="{backgroundColor: command.isDifferent ? 'indianred' : 'white'}"
        >
          <!--        <td>{{ command }}</td>-->
          <td>{{ command.id }}</td>
          <td>{{ command.name }}</td>
          <td>{{ command.command_params }}</td>
          <td>{{ command.from_session }}</td>
          <td>{{ command.isDifferent }}</td>
        </tr>
      </table>
    </div>

  </div>
</template>

<script lang="ts">

import {Component, Vue} from 'vue-property-decorator';
import {subscribe_nestjs} from '../../model/nestjs-connector';
import {CommandDiff} from '../../../../monitor-backend/src/model/models';

@Component({
  components: {}
})
export default class NestJSDiffViewer extends Vue {
  isConnected = false;

  url = 'http://build2.auctionologies.com:8083';

  commands: CommandDiff[] = [];

  mounted() {

    subscribe_nestjs(this.url,
      (data) => {
        this.commands.push(data);
      },
      isConnected =>
        this.isConnected = isConnected);
  }
}
</script>

<style scoped>

table {
  width: 100%;
}

table, tr, td {
  border: 1px solid black;
}

td {
  padding: 5px;
  text-align: left;
  vertical-align: top;
}

.pad_width {
  padding: 0 10px;
}

.heading {
  font-size: 14px;
  font-weight: normal;
}
</style>
