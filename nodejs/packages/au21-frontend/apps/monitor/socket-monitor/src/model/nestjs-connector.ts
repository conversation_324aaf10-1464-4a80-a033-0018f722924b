import {uuid} from '@au21-frontend/utils';
import {CommandDiff} from '../../../monitor-backend/src/model/models';

const session_id = uuid();

export function subscribe_nestjs(
  url: string,
  data_cb: (data: CommandDiff) => void,
  connection_cb: (isConnected: boolean) => void) {

  const ws = new WebSocket(`ws://localhost:8100`);

  ws.onopen = () => {
    connection_cb(true);
    console.log('Connected to: ' + url);
    // ws.send(
    //   JSON.stringify({
    //     event: 'events',
    //     data: 'test',
    //   }),
    // );
    ws.send(
      JSON.stringify({
        event: 'connect',
        data: {
          session_id
        }
      })
    );
  };

  ws.onmessage = (e: MessageEvent) => {
    const data = JSON.parse(e.data);
    data_cb(data);
  };


  ws.onclose = function (e: CloseEvent) {
    connection_cb(false);
    console.log('Socket is closed. Reconnect will be attempted in 1 second.', e.reason);
    setTimeout(function () {
      subscribe_nestjs(url, data_cb, connection_cb);
    }, 1000);
  };

  ws.onerror = function (err: Event) {
    console.error('Socket encountered error: ', {err}, 'Closing socket');
    ws.close();
  };
}
