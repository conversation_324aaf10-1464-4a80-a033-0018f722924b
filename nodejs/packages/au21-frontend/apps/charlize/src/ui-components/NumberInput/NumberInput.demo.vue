<template>
  <VbDemo>
    <VbCard title="thousand separators string">
      <NumberInput style="width: 150px" v-model="valueDefault" />
      <p>{{ valueDefault }}</p>
    </VbCard>
    <VbCard title="decimal places">
      <NumberInput
        style="width: 150px"
        v-model="withDecimals"
        :decimalPlaces="2"
      />
      <NumberInput
        style="width: 150px"
        v-model="withDecimals"
        :decimalPlaces="4"
      />
      <p>{{ withDecimals }}</p>
    </VbCard>
    <VbCard title="emptyValue">
      <NumberInput style="width: 150px" v-model="emptyValue" />
      <p>{{ emptyValue }}</p>
    </VbCard>
    <VbCard title="disabled">
      <NumberInput style="width: 150px" value="123" disabled />
    </VbCard>
    <VbCard title="output">
      <NumberInput style="width: 150px" value="123" output />
    </VbCard>
    <VbCard title="events">
      <NumberInput
        style="width: 150px"
        v-model="eventsValue"
        @blur="$vb.log('blur')"
        @enter="$vb.log('enter')"
        @escape="$vb.log('escape')"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import NumberInput from './NumberInput.vue';
import {LogMixin} from '../../pages/common/LoginPage/logMixin';

@Component({
  components: { NumberInput },
  mixins: [LogMixin],
})
export default class NumberInputDemo extends Vue {
  valueDefault = '100000'
  withDecimals = '100000.10'
  emptyValue = ''
  nullValue = null
  eventsValue = ''
}
</script>
