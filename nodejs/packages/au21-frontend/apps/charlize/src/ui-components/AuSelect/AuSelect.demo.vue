<template>
  <VbDemo>
    <VbCard title="default">
      <AuSelect
        :options="options"
      />
      <div>
        {{ value }}
      </div>
    </VbCard>
    <VbCard title="existing value">
      <AuSelect
        v-model="value"
        :options="options"
      />
    </VbCard>
    <VbCard title="object options">
      <AuSelect
        v-model="value"
        :options="recordOptions"
        :valueBy="(value, key) => key"
      />
    </VbCard>
    <VbCard title="disabled">
      <AuSelect
        :value="value"
        :options="options"
        disabled
      />
    </VbCard>
    <VbCard title="output">
      <AuSelect
        :value="value"
        :options="options"
        output
      />
    </VbCard>
    <VbCard title="dense">
      <AuSelect
        :value="value"
        :options="options"
        dense
      />
    </VbCard>
    <VbCard title="object options">
      <AuSelect
        v-model="objectOptionsValue"
        :options="objectOptions"
        :textBy="item => item.name"
        :valueBy="item => item.id"
      />
      {{ objectOptionsValue }}
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import AuSelect from './AuSelect.vue';

@Component({
  components: { AuSelect },
})
export default class AuSelectDemo extends Vue {
  value = 'one'
  options = ['one', 'two', 'three']
  recordOptions = { one: 1, two: 2, three: 3 }

  objectOptionsValue = null
  objectOptions = [
    { id: 1, name: 'one' },
    { id: 2, name: 'two' },
    { id: 3, name: 'three' },
  ]
}
</script>
