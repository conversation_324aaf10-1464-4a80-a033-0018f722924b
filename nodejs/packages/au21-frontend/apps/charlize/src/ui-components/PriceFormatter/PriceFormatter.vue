<template>
  <div>
    <div
      :style="`display: inline-block; color: ${price_color}; font-size: ${integer_font_size}px; ${ integer_bold ? 'font-weight:bold' : ''}`">
      {{ price_integer }}
    </div>
    <div
      :style="`display: inline-block; color: ${price_color};  font-size: ${fraction_font_size}px; ${ fraction_bold ? 'font-weight:bold' : ''}`">
      {{ price_fraction }}
    </div>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {PriceDirection} from '@au21-frontend/client-connector';
import {AuColors} from '../../au-styles/AuColors';
import {Container} from 'typescript-ioc';


@Component({})
export default class PriceFormatter extends Vue {
  @Prop({ required: true }) price: string;
  @Prop({ required: true }) price_direction: PriceDirection;
  @Prop({ required: true }) integer_font_size: number;
  @Prop({ required: true }) integer_bold: boolean;
  @Prop({ required: true }) fraction_font_size: number;
  @Prop({ required: true }) fraction_bold: boolean;

  colors = Container.get(AuColors);

  get roundPriceSplit(): string[] {
    return this.price.split('.') || [];
  }

  get price_integer(): string {
    return this.roundPriceSplit.length == 0 ? '' : this.roundPriceSplit[0];
  }

  get price_fraction(): string {
    return this.roundPriceSplit.length > 1 ? `.${this.roundPriceSplit[1]}` : '';
  }

  get price_color(): string {
    switch (this.price_direction) {
      case PriceDirection.DOWN:
        return this.colors.au_sell();
      case PriceDirection.UP:
        return this.colors.au_buy();
      default:
        return '#ddd';
    }
  }

}
</script>

<style lang="less" scoped>
@import (reference) "../../au-styles/variables";

</style>
