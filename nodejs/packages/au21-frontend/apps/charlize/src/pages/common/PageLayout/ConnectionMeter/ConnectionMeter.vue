<template>
  <svg class="ConnectionMeter" width="40" height="26">
    <rect
      x="2"
      y="14"
      rx="1"
      ry="1"
      width="6"
      height="12"
      :style="barStyle(1)"
    />
    <rect
      x="10"
      y="11"
      rx="1"
      ry="1"
      width="6"
      height="15"
      :style="barStyle(2)"
    />
    <rect
      x="18"
      y="8"
      rx="1"
      ry="1"
      width="6"
      height="18"
      :style="barStyle(3)"
    />
    <rect
      x="26"
      y="5"
      rx="1"
      ry="1"
      width="6"
      height="21"
      :style="barStyle(4)"
    />
  </svg>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component, Prop} from 'vue-property-decorator';

@Component
export default class ConnectionMeter extends Vue {
  @Prop({ type: Number, required: true }) lastPingLatency: number

  get barCount() {
    if (this.lastPingLatency < 1) {
      return 4
    }
    if (this.lastPingLatency < 5) {
      return 3
    }
    if (this.lastPingLatency < 10) {
      return 2
    }
    return 1
  }

  barStyle(barNumber: number) {
    const fillsForBarCounts = [
      ['red', 'orange', 'green', 'green'],
      ['grey', 'orange', 'green', 'green'],
      ['grey', 'grey', 'green', 'green'],
      ['grey', 'grey', 'grey', 'green'],
    ]

    return {
      stroke: '#333',
      'stroke-width': '1',
      opacity: '1.0',
      fill: fillsForBarCounts[barNumber - 1][this.barCount - 1],
    }
  }
}
</script>

<style lang="less" scoped>

</style>
