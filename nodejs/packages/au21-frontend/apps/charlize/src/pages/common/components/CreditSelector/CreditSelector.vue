<template>
  <div class="CreditSelector">
    <div
      class="_container"
      :style="{width: width + 'px'}"
    >
      <NumberInput
        ref="input"
        v-if="!isNoLimit"
        :value="value"
        @input="onInput"
        @enter="blur"
        :decimalPlaces="decimalPlaces"
        :height="20"
      />
      <div
        v-else
        style="text-align: center"
      >
        <slot>
          no limit
        </slot>
      </div>
    </div>
    <ACheckbox
      style="margin-left: 5px"
      :checked="!isNoLimit"
      @input="onCheckboxClick"
    />
  </div>
</template>

<script lang="ts">
import {Component, Prop, Ref, Vue} from 'vue-property-decorator';
import NumberInput from '../../../../ui-components/NumberInput/NumberInput.vue';
import {sleep} from '@au21-frontend/utils';

@Component({
  name: 'CreditSelector',
  components: { NumberInput },
})
export default class CreditSelector extends Vue {
  @Prop({ type: String, default: 'no limit' }) value: 'no limit' | string;
  @Prop({ required: true }) width: number;
  @Prop({ required: true }) decimalPlaces: number;
  @Ref() input: NumberInput;

  onInput (value: string) {
    // console.log('value', value);
    this.$emit('input', value);
  }
  get isNoLimit (): boolean {
    return this.value === 'no limit';
  }
  async onCheckboxClick (checked: boolean) {
    if (checked) {
      this.$emit('input', '');
      await sleep()
      this.input?.focus()
    } else {
      this.$emit('input', 'no limit');
    }
  }
  async blur() {
    (this.$refs.input as NumberInput).blur()
  }
}
</script>

<style lang="less" scoped>
.CreditSelector {
  ._container {
    display: inline-block;
  }
}
</style>
