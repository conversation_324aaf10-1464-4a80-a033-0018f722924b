<template>
  <div class="TransfersTableSortableHeader" @click="onClick()">
    {{ display_name }}
    <a-icon
      style="width: 12px; height: 12px"
      :type="sortDirection === 'asc' ? 'down' : 'up'"
    />
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component} from 'vue-property-decorator';
import {TransferTableCellParams} from './TransfersTable.types';

@Component
export default class TransfersTableSortableHeader extends Vue {
  params = null

  get cell_params (): TransferTableCellParams {
    return this.params.column.colDef.cellRendererParams;
  }

  get display_name(): string {
    return this.params.displayName
  }

  get sortDirection () {
    return this.cell_params.getSort()
  }
  onClick() {
    this.cell_params?.setSort(this.sortDirection === 'asc' ? 'desc' : 'asc')
  }
}
</script>


<style lang="less" scoped>
.TransfersTableSortableHeader {
  width: 100%;
  text-align: center;
  white-space: normal;
  cursor: pointer;
}
</style>
