<template>
  <a-row
    class="TraderNavBar"
    type="flex"
    justify="space-between"
  >
    <a-col :span="1">
      <LogoFramed
        class="_logo"
        @click="goToHomePage()"
      />
    </a-col>
    <a-col :span="5" class="align-center au-label">
      <template v-if="time">
        <div class="_day_of_week">
          {{ dayOfWeekReadable }}
        </div>
        <span class="clock" style="font-size: x-large;">{{ timeReadable }}</span>
        <div class="_time_zone">
          {{ hourAmPm.amPm }} {{ time.city }}
        </div>
      </template>
    </a-col>

    <a-col :span="3" class="au-label">
      <span class="au-label _connection">
        Connection:
      </span>
      <ConnectionMeter
        style="display: inline-block"
        :lastPingLatency="lastPingLatency"
      />
    </a-col>

    <a-col :span="2" class="au-label" style="font-size: 10px">
      <div style="height: 16px">
        <div
          v-if="username"
          class="au-label"
        >
          User: {{ username }} ({{ company.company_shortname }})
        </div>
      </div>
      <div
        v-if="userSession && !username"
        style="position:relative; top: -5px;"
      >
        session id: {{ store.live_store.session_user && store.live_store.session_user.session_id }}
      </div>
      <div v-if="userSession && username">
        session id: {{ store.live_store.session_user && store.live_store.session_user.session_id }}
      </div>
    </a-col>

    <a-col :span="2" v-if="session_user">
      <div
        style="position: absolute; left: 40px; text-align: center; top: -2px"
        v-if="username"
      >
        <div class="au-label">Theme</div>
        <ASwitch
          class="_switch"
          default-checked
          size="small"
          @change="toggle_theme"
        />
      </div>
    </a-col>

    <a-col :span="6" push style="text-align: right">
      <template
        v-if="isLoggedIn"
      >
        <a-button-group>
          <a-button class="au-btn" type="primary" @click="goToHomePage()">Home</a-button>
          <a-button
            v-if="!use_counterparty_credits"
            class="au-btn"
            type="primary"
            @click="goToCreditPage()"
          >
            Credit
          </a-button>
          <a-button class="au-btn" type="primary" @click="signOut()">Sign out</a-button>
        </a-button-group>
        <!--        <a-button-group>-->
        <!--          <a class="text-link" @click="goToHomePage()">Home</a>-->
        <!--          <span class="ml-2 mr-2">|</span>-->
        <!--          &lt;!&ndash;          <template v-if="isAdmin">&ndash;&gt;-->
        <!--          &lt;!&ndash;            <SystemSettingsButton/>&ndash;&gt;-->
        <!--          &lt;!&ndash;            <span class="ml-2 mr-2">|</span>&ndash;&gt;-->
        <!--          &lt;!&ndash;          </template>&ndash;&gt;-->

        <!--          <a class="text-link" @click="goToCreditPage()">Credit</a>-->
        <!--          <span class="ml-2 mr-2">|</span>-->

        <!--          <template v-if="isAuctioneerOrAdmin">-->
        <!--            <a class="text-link" @click="goToUsersPage()">Users</a>-->
        <!--            <span class="ml-2 mr-2">|</span>-->
        <!--            &lt;!&ndash;            &nbsp; | &nbsp;&ndash;&gt;-->
        <!--            &lt;!&ndash;            <a class="text-link" @click="PageName.SESSION_PAGE">Sessions</a>&ndash;&gt;-->
        <!--            &lt;!&ndash;            &nbsp; | &nbsp;&ndash;&gt;-->
        <!--          </template>-->
        <!--          <a class="text-link" @click="signOut()">Sign out</a>-->
        <!--        </a-button-group>-->
        <p></p>
        <!--{{lastPingLatency}}-->
      </template>

      <CompanyCreditModal
        v-if="showCreditModal"
        @close="showCreditModal = null"
        :store="store"
        :seller_id="company.company_id"
        :seller_shortname="company.company_shortname"
        :seller_longname="company.company_longname"
      />
    </a-col>
  </a-row>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import ConnectionMeter from '../ConnectionMeter/ConnectionMeter.vue';
import LogoFramed from './LogoFramed.vue';
import {Container} from 'typescript-ioc';
import {
  AuUserRole,
  CompanyElement,
  page_set_command,
  PageName,
  session_terminate_command,
  SessionTerminationReason,
  SessionUserValue,
  SocketConnector
} from '@au21-frontend/client-connector';
import {CharlizeStore} from '../../../../services/connector/CharlizeStore';
import CompanyCreditModal from '../../../De/DeAuctioneer/credit_edit/CompanyCreditModal.vue';
import {AuColors} from "../../../../au-styles/AuColors";

@Component({
  components: {CompanyCreditModal, LogoFramed, ConnectionMeter}
})
export default class TraderNavBar extends Vue {
  @Prop({required:true}) store:CharlizeStore;

  connector = Container.get(SocketConnector);
  colors = Container.get(AuColors)

  showCreditModal = false;

  toggle_theme() {
    this.colors.switch_theme()
  }

  get userSession(): SessionUserValue {
    return this.store.live_store.session_user;
  }

  get lastPingLatency() {
    return this.store.live_store.seconds_since_last_message_received || 0;
  }

  get session_user(): SessionUserValue {
    return this.store.live_store.session_user;
  }

  get username(): string {
    return this.session_user?.username || '';
  }

  get company(): CompanyElement {
    return {
      company_id: this.session_user.company_id,
      company_shortname: this.session_user.company_shortname,
      company_longname: this.session_user.company_longname
    } as CompanyElement;
  }

  get roleLabel(): string {
    if (this.userSession.role === AuUserRole.AUCTIONEER)
      return 'auctioneer';
    else if (this.userSession.role === AuUserRole.TRADER)
      return 'trader';
    else
      return this.userSession.role;
  }

  get traderUser(): SessionUserValue {
    return this.store.live_store.session_user;
  }

  gotoPage(page: PageName) {
    this.connector.publish(page_set_command({page}));
  }

  goToHomePage() {
    this.gotoPage(PageName.HOME_PAGE);
  }

  goToCreditPage() {
    this.showCreditModal = true;
  }

  goToUsersPage() {
    this.gotoPage(PageName.USER_PAGE);
  }

  signOut() {
    this.connector.publish(session_terminate_command({
      reason: SessionTerminationReason.SIGNED_OFF
    }));
  }

  get time() {
    return this.store.live_store.time;
  }

  get timeReadable() {
    const seconds = Number(this.time.date_time.seconds);
    let secondsString = String(seconds);
    if (seconds < 10) {
      secondsString = '0' + secondsString;
    }

    const minutes = Number(this.time.date_time.minutes);
    let minutesString = String(minutes);
    if (minutes < 10) {
      minutesString = '0' + minutesString;
    }

    const hours = this.hourAmPm.hour;

    return `${hours}:${minutesString}:${secondsString}`;
  }

  get hourAmPm(): { hour: number, amPm: 'am' | 'pm' } {
    const hour = this.time.date_time.hour;
    if (hour > 12) {
      return {
        hour: hour - 12,
        amPm: 'pm'
      };
    } else {
      return {
        hour,
        amPm: 'am'
      };
    }
  }

  get dayOfWeekReadable(): string {
    const dayOfWeek = this.time.date_time.day_of_week;

    switch (dayOfWeek) {
      case 0 :
        return 'Sunday';
      case 1 :
        return 'Monday';
      case 2 :
        return 'Tuesday';
      case 3 :
        return 'Wednesday';
      case 4 :
        return 'Thursday';
      case 5 :
        return 'Friday';
      case 6 :
        return 'Saturday';
      default:
        return '';
    }
  }

  get isLoggedIn (): boolean {
    return !!(this.store.live_store.session_user && this.store.live_store.session_user.username)
  }

  get use_counterparty_credits (): boolean {
    // TODO Use global settings
    return true
  }
}
</script>

<style lang="less" scoped>
@import (reference) "../../../../au-styles/variables.less";

.TraderNavBar {
  background-color: @layout-color;
  //border: 1px solid red;
  padding: 4px 4px;
  font-size: 14px;
  height: 42px;

  ._logo {
    position: relative;
    top: -6px;
  }

  ._day_of_week {
    //  border: 1px solid red;
    display: inline-block;
    margin-right: 5px;
    position: relative;
    bottom: 4px;
  }

  ._time_zone {
    // border: 1px solid red;
    display: inline-block;
    margin-left: 6px;
    position: relative;
    bottom: 4px;
  }

  ._connection {
    // border: 1px solid red;
    display: inline-block;
    margin-left: 6px;
    position: relative;
    bottom: 4px;
  }

  ._session_id {
    // border: 1px solid red;
    display: inline-block;
    margin-left: 6px;
    position: relative;
    bottom: 4px;
  }

  ._switch{
    position: relative;
    top: -4px;
  }

}

</style>
