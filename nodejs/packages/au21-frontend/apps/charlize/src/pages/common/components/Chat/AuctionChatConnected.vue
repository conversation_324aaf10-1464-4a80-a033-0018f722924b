<template>
  <div class="AuctionChatConnected"
       :style="{height: height + 'px'}">
    <TableHeading>
      <div style='display: inline-block'>Messages</div>
    </TableHeading>
    <AuctionChat
      :messages="messages"
      @submitMessage="submitMessage"
      :is_auctioneer="is_auctioneer"
      :outer_height="height"
      :width="width"
    />
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import AuctionChat from './AuctionChat.vue';
import {Container} from 'typescript-ioc';
import {message_send_command, SocketConnector} from '@au21-frontend/client-connector';
import {CharlizeStore} from '../../../../services/connector/CharlizeStore';
import AuSectionHeader from '../../../../ui-components/AuSectionHeader.vue';
import TableHeading from '../TableHeading/TableHeading.vue';

@Component({
  name: 'AuctionChatConnected',
  components: {
    AuSectionHeader,
    AuctionChat,
    TableHeading,
  }
})
export default class AuctionChatConnected extends Vue {
  @Prop({required: true}) store: CharlizeStore;
  @Prop({required: true}) is_auctioneer: boolean;
  @Prop({required: true}) height: number;
  @Prop({required: true}) width: number;

  connector = Container.get(SocketConnector);

  get messages() {
    return this.store.live_store.de_auction.messages || [];
  }

  submitMessage(message: string) {
    this.connector.publish(message_send_command({
      auction_id: this.store.live_store.de_auction.auction_id,
      message
    }));
  }
}
</script>

<style lang="less" scoped>
.AuctionChatConnected {
}
</style>
