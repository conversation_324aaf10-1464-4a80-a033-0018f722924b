export let test_logins: TestLogin[] = []

interface TestLogin {
  username: string,
  password: string,
}

if (process.env.NODE_ENV === 'development') {
  const generatePassword = username => ({username, password: '1'})

  test_logins = [
    //  'admin',
    'a1',
    'a2',
    'a3',
    'b1',
    'b2',
    'b3',
    'b4',
  ].map(generatePassword)
}

if (process.env.VUE_APP_LOGINS) {
  test_logins = JSON.parse(process.env.VUE_APP_LOGINS)
}
