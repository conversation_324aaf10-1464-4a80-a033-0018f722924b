<template>
  <a-button
    class="AdminSettingsButton au-btn"
    type="primary"
    @click="editAdminSetting()"
  >
    Admin
    <AdminSettingsModal
      v-if="adminSettings"
      :adminSettings="adminSettings"
      @saveAdminSettings="saveAdminSettings()"
      @close="adminSettings = null"
    />
  </a-button>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import AdminSettingsModal from './AdminSettingsModal.vue';
import {CharlizeStore} from '../../../../../services/connector/CharlizeStore';
import {Container} from 'typescript-ioc';
import {SocketConnector} from '@au21-frontend/client-connector';

@Component({
  name: 'AdminSettingsButton',
  components: { AdminSettingsModal },
})
export default class AdminSettingsButton extends Vue {
  connector = Container.get(SocketConnector);

  @Prop({required: true}) store: CharlizeStore;

  adminSettings = null

  editAdminSetting (): void {
    // TODO Take admin settings from store.
    this.adminSettings = {
      use_counterparty_credits: false,
    }
  }

  saveAdminSettings () {
    // TODO Waiting for backend.
    console.log('saving')
    this.adminSettings = null
  }
}
</script>

<style lang="scss">

</style>
