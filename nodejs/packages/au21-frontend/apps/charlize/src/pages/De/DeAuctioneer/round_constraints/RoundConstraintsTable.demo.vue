<template>
  <VbDemo>
    <VbCard>
      <RoundConstraintsTable
        :round_trader_elements_for_round="traderElements"
        :height_offset="50"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import {createMultipleByClosure} from '@au21-frontend/utils';
import {createDemo__CompanyElement} from '../../../../demo-helpers/CompanyElement.helper';
import RoundConstraintsTable from './RoundConstraintsTable.vue';
import {createDemo__DeRoundTraderElement} from '../../../../demo-helpers/DeRoundTable.helper';

@Component({
  components: {RoundConstraintsTable},
})
export default class ConstraintsDemo extends Vue {
  companies = createMultipleByClosure(createDemo__CompanyElement, 20)
  traderElements = this.companies.map(company => createDemo__DeRoundTraderElement(1, company))
}
</script>
