<template>
  <span
    class="RoundTotalTableAgBodyCell"
  >
    {{ textComputed }}
  </span>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component} from 'vue-property-decorator';
import {DeRoundResultVM, DeTraderFlowVM, OrderType} from '@au21-frontend/client-connector';
import {DeRoundTotalTableCellParams, DeRoundTotalTableRow} from './RoundTotalTable.types';

//const colors = Container.get(AuColors);

// const classMap: Partial<Record<DeRoundTotalTableRow['id'], string>> = {
//   'PRICE': 'text-color-price',
//   'BUY_TOTAL': 'text-color-buy',
//   'SELL_TOTAL': 'text-color-sell',
//   'MATCH': 'text-color-match'
// };

@Component({
  name: 'RoundTotalTableAgBodyCell'
})
export default class RoundTotalTableAgBodyCell extends Vue {
  params = null;

  get row(): DeRoundTotalTableRow {
    return this.params.data;
  }

  get column(): DeRoundTotalTableCellParams {
    return this.params.colDef.cellRendererParams;
  }

  get roundResult(): DeRoundResultVM {
    return this.column!.roundResult;
  }

  // get classComputed() {
  //   return classMap[this.row.id];
  // }

  getTraderFlowString(companyId: string) {
    const flow: DeTraderFlowVM = this.roundResult.trader_flows.find(flow => flow.company_id === companyId);
    const flow_str = flow.order_type == OrderType.BUY ?
      'Buy' :
      flow.order_type == OrderType.SELL ?
        'Sell' :
        '';
    return `${flow_str} ${flow.quantity}`;
  }

  get textComputed() {
    switch (this.row.id) {
      case 'PRICE':
        return 'TODO';
      case 'BUY_TOTAL':
        return this.roundResult.buy_total;
      case 'SELL_TOTAL':
        return this.roundResult.sell_total;
      case 'MATCH':
        return this.roundResult.match_total;
      case 'TRADERS_ROW':
        return '';
      default:
        return this.getTraderFlowString(this.row.id);
    }
  }
}
</script>


<style lang="less" scoped>
@import (reference) "../../../../../au-styles/variables.less";

.RoundTotalTableAgBodyCell {
  //display: inline-block;
  font-size: 16px;
  //padding-right: 5px;
  //position: relative;
  //top: -2px;
  //width: 35px;
}

//.outer {
//  font-size: 13px;
//  height: 100%;
//  padding-top: 4px;
//  text-align: right;
//  width: 100%;
//}
</style>
