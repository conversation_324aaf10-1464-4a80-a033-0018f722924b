<template>
  <AuAgGrid
    class="CreditTable"
    :height="height"
    :width="width"
    :columnDefs="columnDefs"
    :rowData="counterpartyCreditsSorted"
    :getRowHeight="() => row_height"
    :gridOptions="gridOptions"
  />
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import AuAgGrid from '../../../../ui-components/AuAgGrid.vue';
import AuAgGridCenteredHeader from '../../../../ui-components/AuAgGridCenteredHeader.vue';
import {ColDef, GridOptions} from 'ag-grid-community';
import DeCreditHeader from './CreditHeader.vue';
import {CompanyElement, CounterpartyCreditElement} from '@au21-frontend/client-connector';
import CreditTableCell from './CreditTableCell.vue';
import {ICellRendererParams} from 'ag-grid-community/dist/lib/rendering/cellRenderers/iCellRenderer';
import {sortBy} from "lodash";

@Component({
  name: 'CreditTable',
  components: {AuAgGrid}
})
export default class CreditTable extends Vue {
  @Prop({required: true}) companies: CompanyElement[];
  @Prop({
    required: true,
    type: Array
  }) counterparty_credits: CounterpartyCreditElement[];
  @Prop({default: true, type: Boolean}) editable;
  @Prop({required: true, type: Number}) height;
  @Prop({required: true, type: Number}) width;

  first_col_width = 200;
  body_col_width = 100;
  row_height = 24;

  gridOptions: GridOptions = {
    headerHeight: 85,
    defaultColDef: {
      //
      headerComponentFramework: AuAgGridCenteredHeader,
      cellStyle: () => ({padding: '0', border: '0'})
    },
    getRowNodeId: (data) => data.companyId,
    suppressHorizontalScroll: false
  };

  get columnDefs(): ColDef[] {
    const leftFixedColumn = {
      headerName: 'Buyer rows vs Seller columns',
      headerComponentFramework: AuAgGridCenteredHeader,
      pinned: true,
      // We need 3 widths set for create_scenario_result_rows to stay consistent.
      width: this.first_col_width,
      minWidth: this.first_col_width,
      maxWidth: this.first_col_width,

      cellRenderer: (params: ICellRendererParams) => {
        const company = this.table_companies[params.data.companyId];
        return `<div style='padding-left: 5px;'>Buyer: ${company?.company_shortname}</div>`;
      }
    };

    const bodyColumns = this.companiesSorted.map((company: CompanyElement) => ({
      headerComponentFramework: DeCreditHeader,
      // We need 3 widths set for create_scenario_result_rows to stay consistent.
      width: this.body_col_width,
      minWidth: this.body_col_width,
      maxWidth: this.body_col_width,
      cellRendererFramework: CreditTableCell,
      cellRendererParams: {
        company,
        editable: this.editable
      }
    }));

    return [
      leftFixedColumn,
      ...bodyColumns
    ];
  }


  get companiesSorted(): CompanyElement[] {
    return sortBy(this.companies, 'company_shortname')
  }

  get counterpartyCreditsSorted(): { companyId: string, nodes: Record<string, Record<string, CounterpartyCreditElement>> }[] {
    const buyerToNodes: Record<string, Record<string, CounterpartyCreditElement>> = {};
    this.companiesSorted.forEach(company => {
      buyerToNodes[company.company_id] = {};
    });
    this.counterparty_credits.forEach(element => {
      buyerToNodes[element.buyer_id][element.seller_id] = element;
    });

    return this.companiesSorted.map(company => ({
      companyId: company.company_id,
      nodes: buyerToNodes
    }));
  }

  get table_companies(): Record<string, CompanyElement> {
    const table_companies: Record<string, CompanyElement> = {};
    this.counterparty_credits.forEach(credit => {
      if (!table_companies[credit.seller_id]) {
        table_companies[credit.seller_id] = {
          id: credit.seller_id,
          company_id: credit.seller_id,
          company_longname: credit.seller_longname,
          company_shortname: credit.seller_shortname
        };
      }
      if (!table_companies[credit.buyer_id]) {
        table_companies[credit.buyer_id] = {
          id: credit.buyer_id,
          company_id: credit.buyer_id,
          company_longname: credit.buyer_longname,
          company_shortname: credit.buyer_shortname
        };
      }
    });
    return table_companies;
  }


  onCompanySelect(company: CompanyElement) {
    this.$emit('companySelected', company);
  }

  /** TODO: empty rows and cols
   get empty_row_count(): number {
    const body_height = this.height - 85; // ie: - header_height
    const rows_height = this.companies.length * this.row_height;
    const empty_height = body_height - rows_height;
    return empty_height > 0 ?
      (empty_height / this.row_height)
      : 0;
  }

   get empty_col_count(): number {
    const body_width = this.width - this.first_col_width - 14; // slider
    const cols_width = this.companies.length * this.body_col_width;
    const empty_width = body_width - cols_width;
    return empty_width > 0 ?
      (empty_width / this.body_col_width)
      : 0;
  }
   */

}
</script>

<style lang="less" scoped>
.CreditTable {

}
</style>
