<template>
  <VbDemo>
    <VbCard title="setup">
      <DeTraderHeader
        :traderInfo="traderInfo"
        :commonStatus="commonStatusSetup"
        :settings="settings"
        :store="store"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import DeTraderHeader from './DeTraderHeader.vue';
import {createDemo__DeTraderInfoValue} from '../../../../demo-helpers/DeTrader.helper';
import {
  createDemo__DeCommonStatusValue,
  createDemo__DeCommonStatusValue_setup
} from '../../../../demo-helpers/DeCommonStatusValue.helper';
import {createDefault__DeSettingsValue} from '../../../../demo-helpers/DeSettingsValue.helper';
import {createDemo__store_for_auctioneer} from '../../../../demo-helpers/CharlizeStore.helper';

@Component({
  components: {DeTraderHeader},
})
export default class DeTraderHeaderDemo extends Vue {
  store = createDemo__store_for_auctioneer();
  traderInfo = createDemo__DeTraderInfoValue()
  commonStatus = createDemo__DeCommonStatusValue()
  commonStatusSetup = createDemo__DeCommonStatusValue_setup()
  settings = createDefault__DeSettingsValue()
}
</script>
