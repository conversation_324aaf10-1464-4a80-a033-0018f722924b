<template>
  <VbDemo>
    <VbCard>
      <DeCurrentOrder
        :common_status="commonStatus"
        :settings="settings"
        :trader_info="traderInfo"/>
      <button @click="changeSide">change side</button>
      <button @click="changeQuantity">change quantity</button>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import DeCurrentOrder from './DeCurrentOrder.vue';
import {createDemo__DeTraderInfoValue} from '../../../../../demo-helpers/DeTrader.helper';
import {createDemo__DeCommonStatusValue} from '../../../../../demo-helpers/DeCommonStatusValue.helper';
import {createDefault__DeSettingsValue} from '../../../../../demo-helpers/DeSettingsValue.helper';
import {OrderType} from '@au21-frontend/client-connector';
import {random_number} from '@au21-frontend/utils';

@Component({
  name: 'DeCurrentOrderDemo',
  components: {DeCurrentOrder},
})
export default class DeCurrentOrderDemo extends Vue {
  traderInfo = createDemo__DeTraderInfoValue();
  commonStatus = createDemo__DeCommonStatusValue()
  settings = createDefault__DeSettingsValue()

  changeSide() {
    this.traderInfo.order_type = this.traderInfo.order_type === OrderType.BUY ? OrderType.SELL : OrderType.BUY
  }

  changeQuantity() {

    this.traderInfo.order_quantity = random_number()
  }
}
</script>
