<template>
  <VbDemo>
    <VbCard>
      <GlobalCreditTable
        :rows="rows"
        :de_settings_value="de_settings_value"
        :width="500"
      />
      <pre style="color: white">{{ rows }}</pre>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import GlobalCreditTable from './GlobalCreditTable.vue';
import {createMultipleByClosure} from '@au21-frontend/utils';
import {createDemo__GlobalCreditTableRow} from '../../../../demo-helpers/GlobalCreditTable.helper';
import {createDefault__DeSettingsValue} from '../../../../demo-helpers/DeSettingsValue.helper';

@Component({
  components: {
    GlobalCreditTable,
  },
})
export default class GlobalCreditTableDemo extends Vue {
  rows = createMultipleByClosure(createDemo__GlobalCreditTableRow, 10);
  de_settings_value = createDefault__DeSettingsValue()
}
</script>
