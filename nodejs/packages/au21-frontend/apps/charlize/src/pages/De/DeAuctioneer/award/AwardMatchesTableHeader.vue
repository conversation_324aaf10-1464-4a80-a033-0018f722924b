<template>
  <div class="AwardMatchesTableHeader" @click="onClick()">
    {{ displayName }}
    <a-icon
      v-if="columnSortBy === matchesTable.sortBy"
      style="width: 12px; height: 12px"
      :type="matchesTable.sortDirection === 'asc' ? 'down' : 'up'"
    />
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component} from 'vue-property-decorator';
import AwardMatchesTable from './AwardMatchesTable.vue';

@Component({components: {}})
export default class AwardMatchesTableHeader extends Vue {
  params = null

  get displayName(): string {
    return this.params ? this.params.displayName : ''
  }

  get matchesTable(): AwardMatchesTable {
    return this.$parent.$parent.$parent as AwardMatchesTable
  }

  get columnSortBy() {
    return this.params.column.colDef.headerComponentParams.sortBy
  }

  onClick() {
    if (this.matchesTable.sortBy === this.columnSortBy) {
      this.matchesTable.sortDirection = this.matchesTable.sortDirection === 'asc' ? 'desc' : 'asc'
      return
    }
    this.matchesTable.sortBy = this.columnSortBy
    this.matchesTable.sortDirection = 'desc'
  }
}
</script>


<style lang="less" scoped>
.AwardMatchesTableHeader {
  width: 100%;
  text-align: center;
  white-space: normal;
  cursor: pointer;
}
</style>
