<template>
  <div class='DeAuctioneerPageHeader'>

    <!--  1) AUCTION NAME  -->

    <AuSectionBorder class='_auction_name_panel'>
      <div>
        <div class="au-label"
              style="text-align: center">
          Auction Name
        </div>
        <AuOutput
          :height='40'
          :width="599"
        >
          <strong v-if="category_name">{{category_name}} -</strong>
          {{deSettings.auction_name}}
        </AuOutput>


      </div>
    </AuSectionBorder>

    <!-- 2) Auction state -->

    <AuSectionBorder style='float:left; margin-right: 3px;'>
      <DeAuctioneerStatusPanel :common_status='commonStatus'
                               :auctioneer_status='auctioneerStatus' />
    </AuSectionBorder>


    <!-- 3) De Auctioneer Round Info -->

    <AuSectionBorder class='_round_info'>
      <DeAuctioneerRoundInfo
        :store='store'
        :selected_round='selected_round'
      />
    </AuSectionBorder>

  </div>
</template>

<script lang='ts'>

import {Component, Prop, Vue} from 'vue-property-decorator';
import {
  DeAuctioneerInfoValue,
  DeAuctioneerStatusValue,
  DeAuctionValue,
  DeCommonStatusValue,
  DeSettingsValue
} from '@au21-frontend/client-connector';
import AuSectionBorder from '../../../../ui-components/AuSectionBorder.vue';
import ToolbarButton from '../toolbar/ToolbarButton.vue';
import DeStartingPrice from '../../components/de-starting-price/DeStartingPrice.vue';
import DeAuctioneerRoundInfo from '../round_info/DeAuctioneerRoundInfo.vue';
import DeAuctioneerClock from '../clock/DeAuctioneerClock.vue';
import AuOutput from '../../../../ui-components/AuOuput/AuOutput.vue';
import {CharlizeStore} from '../../../../services/connector/CharlizeStore';
import DeAuctioneerStatusPanel from '../status_panel/DeAuctioneerStatusPanel.vue';

@Component({
  name: 'DeAuctioneerPageHeader',
  components: {
    DeAuctioneerStatusPanel,
    AuOutput,
    AuSectionBorder,
    DeAuctioneerClock,
    DeAuctioneerRoundInfo,
    DeStartingPrice,
    ToolbarButton
  }
})
export default class DeAuctioneerPageHeader extends Vue {
  @Prop({ required: true }) store: CharlizeStore;
  @Prop({ required: true }) selected_round: number;

  get de_auction(): DeAuctionValue | null {
    return this.store?.live_store?.de_auction;
  }

  get commonStatus(): DeCommonStatusValue | null {
    return this.de_auction?.common_status;
  }

  get auctioneerStatus(): DeAuctioneerStatusValue | null {
    return this.de_auction?.auctioneer_status;
  }

  get deSettings(): DeSettingsValue {
    return this.de_auction?.settings;
  }

  get auctioneerInfo(): DeAuctioneerInfoValue {
    return this.de_auction?.auctioneer_info;
  }

  // colors = Container.get(AuColors);

  get round_number(): number {
    // NOTE:DeRoundController doesn't seem to tolerate a null or undefined lastRound
    return this.de_auction?.blotter?.rounds?.length || 0;
  }

  get announced(): boolean {
    return true;
    // return find([
    //   DeAuctioneerState.STARTING_PRICE_NOT_SET,
    //   DeAuctioneerState.STARTING_PRICE_SET
    // ], s => s == this.commonStatus.auction_trader_state) == null;
  }

  get awardable(): boolean {
    return false;
  }

  get category_name(): string {
    // TODO Use store.
    return 'CATEGORY_NAME'
  }
}
</script>

<style lang='less' scoped>
@import (reference) "../../../../au-styles/variables.less";

.DeAuctioneerPageHeader {
  overflow: hidden;
  width: 1613px;

  ._label {
    text-align: center;
    width: 100%;
  }

  ._auction_name_panel {
    background-color: hsl(0, 0%, 20%);
    border: 1px solid hsl(0, 0%, 30%);
    float: left;
    height: 68px;
    margin-right: 3px;
    //overflow: hidden;
    width: 602px;
  }

  ._round_info {
    background-color: hsl(0, 0%, 20%);
    border: 1px solid hsl(0, 0%, 30%);
    float: left;
    margin: 0;
  }

}

</style>
