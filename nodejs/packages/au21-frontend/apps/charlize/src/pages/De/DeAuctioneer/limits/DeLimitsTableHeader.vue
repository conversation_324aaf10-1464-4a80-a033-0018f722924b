<template>
  <div class="DeLimitsTableHeader">
    {{ displayName }}
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component} from 'vue-property-decorator';

@Component({ components: {} })
export default class DeLimitsTableHeader extends Vue {
  params = null

  get displayName(): string {
    return this.params ? this.params.displayName : ''
  }

  refresh(params) {
    return true
  }
}
</script>


<style lang="less" scoped>
@import (reference) "../../../../au-styles/variables.less";

.DeLimitsTableHeader {
  text-align: center;
  width: 100%;
  white-space: normal;
}
</style>
