<template>
  <AuAgGrid
    class="ClientCreditTable"
    :height="height"
    :width="width"
    :columnDefs="columnDefs"
    :rowData="rows"
    :gridOptions="gridOptions"
    :auto_refresh="false"
  />
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import AuAgGridCenteredHeader from '../../../../ui-components/AuAgGridCenteredHeader.vue';
import {ColDef, GridOptions} from 'ag-grid-community';
import AuAgGrid from '../../../../ui-components/AuAgGrid.vue';
import NewCreditCell from './NewCreditCell.vue';
import {ClientCreditTableRow} from './ClientCreditTable.types';
import {ICellRendererParams,} from 'ag-grid-community/dist/lib/rendering/cellRenderers/iCellRenderer';
import {AuScreen} from '../../../../plugins/screen-plugin/AuScreen';


/**
 * @deprecated
 */
@Component({
  components: { AuAgGrid },
})
export default class ClientCreditTable extends Vue {
  @Prop({ required: true }) rows: ClientCreditTableRow[];

  width = 565;

  screen = new AuScreen();

  gridOptions: GridOptions = {
    headerHeight: 28,
    defaultColDef: {
      //
      headerComponentFramework: AuAgGridCenteredHeader,
      cellStyle: () => ({ padding: '0', border: '0' }),
      sortable: true,
    },
    getRowNodeId: (data: ClientCreditTableRow) => data.companyId,
    rowHeight: 24,
    suppressHorizontalScroll: true,
  };

  get height () {
    return this.screen.modal_height;
  }

  get columnDefs (): ColDef[] {
    return [
      {
        headerName: 'Buyer',
        cellRendererParams: { name: 'buyer' },
        cellRenderer: (params: ICellRendererParams) => {
          const row: ClientCreditTableRow = params.data;
          return `<div style="padding-left: 5px; padding-top: 6px">${row.companyName}</div>`;
        },
        sort: 'desc',
      },
      {
        headerName: 'Current Credit Limit',
        cellRendererParams: { name: 'credit-limit' },
        cellRendererFramework: NewCreditCell,
        cellRenderer: (params: ICellRendererParams) => {
          const row: ClientCreditTableRow = params.data;
          return `<div style="padding-right: 5px; padding-top: 6px; text-align: right">${row.currentCreditStr ? '' + row.currentCreditStr : ''}</div>`;
        },
      },
      {
        headerName: 'New Credit',
        cellRendererParams: { name: 'new-credit' },
        cellRendererFramework: NewCreditCell,
      },
    ];
  }
}
</script>

<style lang="less" scoped>
.ClientCreditTable {

}
</style>
