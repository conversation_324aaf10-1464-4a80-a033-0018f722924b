<template>
  <VbDemo>
    <VbCard>
      <DeAuctioneerRoundInfo
        :store="store"
        :selected_round="selected_round"
      />
    </VbCard>
    <VbCard>
      <button @click="refreshValues()">Refresh values</button>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import {createDemo__DeCommonStatusValue_setup} from '../../../../demo-helpers/DeCommonStatusValue.helper';
import {createDemo__DeAuctioneerStatusValue} from '../../../../demo-helpers/DeAuctioneerStatusValue.helper';
import {createDefault__DeSettingsValue} from '../../../../demo-helpers/DeSettingsValue.helper';
import {createDemo__DeAuctioneerInfoValue} from '../../../../demo-helpers/DeAuctioneerInfoValue.helper';
import DeAuctioneerRoundInfo from './DeAuctioneerRoundInfo.vue';
import {createDemo__DeAuctionValue_for_trader,} from '../../../../demo-helpers/DeAuctionValue.helper';
import {createDemo__store_for_auctioneer} from '../../../../demo-helpers/CharlizeStore.helper';

@Component({
  components: {DeAuctioneerRoundInfo}
})
export default class DeAuctioneerRoundInfoDemo extends Vue {
  store =  createDemo__store_for_auctioneer();
  selected_round = 5

  created () {
    this.store.live_store.de_auction = createDemo__DeAuctionValue_for_trader()
    this.store.live_store.de_auction.common_status = createDemo__DeCommonStatusValue_setup()
    this.store.live_store.de_auction.auctioneer_status = createDemo__DeAuctioneerStatusValue()
    this.store.live_store.de_auction.auctioneer_info = createDemo__DeAuctioneerInfoValue()
    this.store.live_store.de_auction.settings = createDefault__DeSettingsValue()
  }

  refreshValues() {
    this.store.live_store.de_auction.auctioneer_info = createDemo__DeAuctioneerInfoValue()
  }
}
</script>
