<template>
  <VbDemo>
    <VbCard>
      <DeTraderHistoryTable
        :height="400"
        :width="400"
        :historyRows="trader_history_rows"
        :settings="settings"
      />
      <button @click="addRow()">Add row</button>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import DeTraderHistoryTable from './DeTraderHistoryTable.vue';
import {
  createDemo__DeTraderHistoryRowElement,
  createDemo__DeTraderHistoryRowSequence,
} from '../../../../demo-helpers/DeTraderHistoryList.helper';
import {createDefault__DeSettingsValue} from '../../../../demo-helpers/DeSettingsValue.helper';

@Component({
  components: {DeTraderHistoryTable}
})
export default class DeTraderHistoryTableDemo extends Vue {
  settings = createDefault__DeSettingsValue();
  trader_history_rows = createDemo__DeTraderHistoryRowSequence(11);

  addRow () {
    this.trader_history_rows.push(createDemo__DeTraderHistoryRowElement(this.trader_history_rows.length + 1))
  }
}
</script>
