// NOTE This is node.js function, not intended for browser use!
function generateFooter () {
  const commit = getLastCommitHash()
  const timestamp = new Date().toUTCString()
  const version = require('../../package.json').version
  return `Copyright 2011-2021 Auctionologies LLC, Version: ${version}` // Build: ${commit}, ${timestamp}`
}

function getLastCommitHash () {
  //const hash = require('child_process').execSync('git rev-parse HEAD')
  //return hash.slice(0, 6)
  return 'removed'
}

module.exports = {
  generateFooter,
}
