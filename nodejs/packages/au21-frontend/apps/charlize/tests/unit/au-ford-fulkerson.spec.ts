import {AuFFDfsSolver, AuFFEdge, AuFFParams} from '../../src/services/flow-algos/domain/au-ford-fulkerson';


describe('AU Ford Fulkerson Solver 1', () => {
  it('should calculate max flow correctly with regular package ff', function () {
    // IE: we never include buyer edges for sellers , and seller edges for buyers
    const params = new AuFFParams(6)

    const g = new AuFFDfsSolver(params)

    g.addEdge(4, 0, 10)
    g.addEdge(4, 1, 5)
    //  g.addEdge(6, 2, 15);

    g.addEdge(0, 2, 4)
    g.addEdge(0, 3, 9)

    g.addEdge(1, 2, 4)
    g.addEdge(1, 3, 8)

    g.addEdge(2, 5, 3)
    g.addEdge(3, 5, 16)

    const maxflow = g.getMaxFlow()
    // console.log({ maxflow })
    expect(maxflow).toBe(15)

    const graph = g.getGraph()

    const solution = g
      .getGraph()
      .flatMap((node_edges: AuFFEdge[]) =>
        node_edges.filter((e) => e.flow > 0),
      )

    // console.log({ solution })
  })

  it('buyer and seller node should not appear on wrong side of edges', () => {
  })
})
