const { SHOW_CONNECTOR_LOG, WEBSOCKET_URL } = require("./build-options");
const generateFooter = require("./generate-footer").generateFooter;
const webpack = require("webpack");
const dotenv = require("dotenv");
const fs = require("fs");
const ThreadsPlugin = require("threads-plugin");
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;

// https://github.com/stephencookdev/speed-measure-webpack-plugin#readme
// const SpeedMeasurePlugin = require("speed-measure-webpack-plugin");

// Read and prepare app env for webpack define plugin.
const getApplicationEnv = () => {
  const envFileName = process.env.APP_MODE || "app";
  console.log({envFileName})
  const env = dotenv.parse(fs.readFileSync(`${__dirname}/.${envFileName}.env`));
  // Have to serialize values for define plugin to function properly
  for (key in env) {
    env[key] = JSON.stringify(env[key]);
  }
  return env;
};

// Read .local.env from charlize project root
const getLocalEnv = () => {
  const filePath = `${__dirname}/.local.env`
  if (!fs.existsSync(filePath)) {
    return {}
  }
  const env = dotenv.parse(fs.readFileSync(filePath));
  // Have to serialize values for define plugin to function properly
  for (key in env) {
    env[key] = JSON.stringify(env[key]);
  }
  console.log('env', env);
  return env;
};

const undefined_replacer = (k, v) => v === undefined ? "undefined" : v;

/*
 * Modify the webpack config by exporting an Object or Function.
 *
 * If the value is an Object, it will be merged into the final
 * config using `webpack-merge`.
 *
 * If the value is a function, it will receive the resolved config
 * as the argument. The function can either mutate the config and
 * return nothing, OR return a cloned or merged version of the config.
 *
 * https://cli.vuejs.org/config/#configurewebpack
 */
module.exports = (config) => {
  if (process.env.NODE_ENV === 'production') {
    // Disable sourcemaps for production
    config.devtool = false // TODO: we should set it to true for Book !
  }

  // HMR is broken without this.
  config.devServer = {
    disableHostCheck: true,
    // port: 8080,
    // public: '0.0.0.0:8080',
    // publicPath: "/",
    // headers: {
    //   'Access-Control-Allow-Origin': '*'
    // }
  }

  // speed plugin:
  // https://www.npmjs.com/package/speed-measure-webpack-plugin
  //config.plugins.push(new SpeedMeasurePlugin({
  //  //outputFormat: "human", // "humanVerbose",
  //  outputFormat: "json",
  //  outputTarget: "speed-measure-plugin.json",
  //  granularLoaderData: true,
  //  loaderTopFiles: 10,
  //}))

  // RULES
  const rules = config.module.rules;

  // Exclude unneded loaders. (@nx-plus/vue serves them by default)
  config.module.rules = config.module.rules.filter(rule => {
    const excludeLoaders = ['.scss', '.sass', '.styl']
    return !excludeLoaders.some(item => rule.test.exec(item))
  })

  // Ant uses javascript embeds in less which is not enabled by default.
  // See https://github.com/ant-design/ant-motion/issues/44#issuecomment-407498459
  const lessRule = rules.find((rule) => rule.test.exec(".less"));
  for (const { use } of lessRule.oneOf) {
    const lessLoader = use.find(({ loader }) => loader.includes("less-loader"));
    lessLoader.options.javascriptEnabled = true;
  }

  // Exclude moment locales except for english
  config.plugins.push(new webpack.ContextReplacementPlugin(/moment[\/\\]locale$/, /en-gb/))

  /**
   *   // 2) T_THREADS: thread loader
   *   // TODO: NOT SURE IF NEED THIS, docs say yes, but workes without it
   *   // - see: https://threads.js.org/getting-started
   *   // const tsLoaderRule = rules.find(rule => rule.test.exec(".ts"));
   *   // tsLoaderRule.use.forEach(rule => {
   *   //   if(rule.loader.includes('ts-loader') > -1){
   *   //     // TODO: unclear why this catches cache-loader also ??
   *   //     if (rule.compilerOptions) {
   *   //       rule.compilerOptions.module = "esnext";
   *   //     } else {
   *   //       rule.compilerOptions = {
   *   //         module: "esnext"
   *   //       };
   *   //     }
   *   //   }
   *   //
   *   // });
   * - https://threads.js.org/getting-started
   *
   *   module: {
   *       rules: [
   *         {
   *           test: /\.ts$/,
   *           loader: "ts-loader",
   * +         options: {
   * +           compilerOptions: {
   * +             module: "esnext"
   * +           }
   * +         }
   *         }
   *       ]
   *     },
   */

  // if (process.env.NODE_ENV !== 'production' && !process.env.WEBSOCKET_PROXY) {
  //   // needed, otherwise we get the cryptic error:
  //   // - "Cannot read property 'upgrade' of undefined"
  //   throw new Error("non-blank WEBSOCKET_PROXY env variable must be set") //, even if production mode!");
  // }

  console.log("\n***********\nUsing WEBSOCKET_URL: " + process.env.WEBSOCKET_URL + "\n***********\n");

  const packageJson = fs.readFileSync('./package.json')
  const version = JSON.parse(packageJson).version
  fs.writeFileSync('apps/charlize/public/version.txt', `PACKAGE_VERSION=${version}`)

  // Pass env to dev-server.
  const definePlugin = config.plugins.find(plugin => plugin instanceof webpack.DefinePlugin);
  definePlugin.definitions["process.env"] = {
    ...definePlugin.definitions["process.env"],
    FOOTER_TEXT: JSON.stringify(generateFooter()),
    SHOW_CONNECTOR_LOG: JSON.stringify(SHOW_CONNECTOR_LOG),
    WEBSOCKET_URL: JSON.stringify(WEBSOCKET_URL),
    ...getApplicationEnv(),
    ...getLocalEnv(),
  };
  console.log("definePlugin.definitions", JSON.stringify(definePlugin.definitions, undefined_replacer, 2));

  // threads plugin: https://threads.js.org/getting-started
  config.plugins.push(new ThreadsPlugin());

  // Uncomment to enable bundle analyzer.
  // config.plugins.push(new BundleAnalyzerPlugin({analyzerMode: 'static'}));
};
