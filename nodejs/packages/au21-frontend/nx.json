{"implicitDependencies": {"workspace.json": "*", "package.json": {"dependencies": "*", "devDependencies": "*"}, "tsconfig.base.json": "*", "tslint.json": "*", ".eslintrc.json": "*", "nx.json": "*"}, "affected": {"defaultBase": "master"}, "npmScope": "au21-frontend", "tasksRunnerOptions": {"default": {"runner": "@nrwl/workspace/tasks-runners/default", "options": {"cacheableOperations": ["build", "lint", "test", "e2e"]}}}, "projects": {"charlize": {"tags": []}, "client-connector": {"tags": []}, "monitor-backend": {"tags": []}, "session-differ": {"tags": []}, "socket-monitor": {"tags": [], "implicitDependencies": ["monitor-backend"]}, "utils": {"tags": []}}}