This file is a merged representation of a subset of the codebase, containing specifically included files and files not matching ignore patterns, combined into a single document by Repomix.
The content has been processed where comments have been removed, empty lines have been removed.

================================================================
Directory Structure
================================================================
src/
  connector.ts
  unzip-worker.ts
tests/
  helpers/
    websocket-server.ts
  connector-browser.spec.ts
  unzip-worker.test.ts
  websocket-server-node.test.ts
package.json
tsconfig.json
vite-env.d.ts
vite.config.ts
vitest.config.ts
vitest.global-setup.ts

================================================================
Files
================================================================

================
File: src/connector.ts
================
import {v4 as uuid} from 'uuid'
import {ClientCommand, CommandType, EngineCommandEnvelope} from '@/types/generated'
import UnzipWorker from './unzip-worker?worker'
function getDefaultWebSocketUrl(): string {
  if(import.meta.env.VITE_WEBSOCKET_URL) {
    return import.meta.env.VITE_WEBSOCKET_URL
  } else {
    const location = window.location
    return`${location.protocol.replace('http', 'ws')}
  }
}
const random_session_id = uuid()
export interface ConnectorOptions {
  url?: string
  session_id?: string
  show_connector_log?: boolean
  clientCommandHandlers?: {
    [K in keyof typeof CommandType as `on${K}`]?: (cmd: ClientCommand & { command: CommandType }) => void
  }
  onSecondsSinceLastMessage?: (seconds: number) => void
  onTerminate?: (reason: { reason: string }) => void
  onError?: (error: Error) => void
}
export interface ConnectorState {
  isConnected: boolean
  messageCount: number
  lastMessageTime: number
  reconnectAttempts: number
  secondsSinceLastMessage: number
}
const MAX_RECONNECT_DELAY = 10000
const QUIET_COMMANDS = ['SetLiveStore', 'AddElements']
export class Connector {
  private static instance: Connector | null = null
  public static getInstance(): Connector {
    if (!Connector.instance) {
      throw new Error('Connector not initialized. Call create() first');
    }
    return Connector.instance;
  }
  private readonly options: ConnectorOptions
  private readonly outgoingQueue: EngineCommandEnvelope[] = []
  private ws: WebSocket | null = null
  private worker: Worker | null = null
  private messageTrackingInterval: number | null = null
  private reconnectInterval: number | null = null
  private state: ConnectorState
  private constructor(options: ConnectorOptions) {
    this.options = options
    this.state = this.getInitialState()
    this.setupWorker()
  }
  public static create(options: ConnectorOptions): Connector {
    if (!Connector.instance) {
      let url = options.url || getDefaultWebSocketUrl()
      if (!url.endsWith('/')) {
        url += '/'
      }
      console.log('url:', url, 'options:', options)
      if(!url.endsWith('socket/')) {
        throw new Error('WebSocket URL should end with socket/')
      }
      Connector.instance = new Connector({ ...options, url })
      Connector.instance.connect().catch(console.error)
    }
    return Connector.instance
  }
  public static reset(options?: ConnectorOptions): Connector | null {
    if (Connector.instance) {
      Connector.instance.cleanup()
    }
    Connector.instance = null
    return options ? Connector.create(options) : null
  }
  public connect(): Promise<void> {
    return new Promise((resolve) => {
      if (this.ws?.readyState === WebSocket.CONNECTING ||
        this.ws?.readyState === WebSocket.OPEN) {
        this.ws.close()
      }
      this.ws = new WebSocket(this.options.url!)
      this.ws.binaryType = 'arraybuffer'
      this.ws.onopen = () => {
        if (this.options.show_connector_log) {
          console.log('WebSocket connected:', this.options.url)
        }
        this.state.isConnected = true
        this.stopReconnection()
        this.state.reconnectAttempts = 0
        this.flushQueue()
        resolve()
      }
      this.ws.onmessage = (event) => {
        console.log('Received message:', event.data instanceof ArrayBuffer ? `ArrayBuffer (${event.data.byteLength} bytes)` : typeof event.data);
        this.state.lastMessageTime = Date.now()
        this.state.secondsSinceLastMessage = 0
        if (event.data instanceof ArrayBuffer) {
          this.worker?.postMessage(event.data, [event.data])
        }
        this.state.messageCount++
      }
      this.ws.onclose = () => {
        this.state.isConnected = false
        this.startReconnection()
      }
      this.ws.onerror = (error) => {
        if (this.options.show_connector_log) {
          console.error('WebSocket error:', error)
        }
        if (this.options.onError) {
          this.options.onError(new Error('WebSocket error: ' + error))
        }
        this.ws?.close()
      }
      this.setupBrowserEvents()
      this.startMessageTracking()
    })
  }
  public close(): void {
    this.cleanup()
  }
  public publish(envelope: EngineCommandEnvelope): void {
    envelope.session_id = this.options.session_id || random_session_id
    if (this.state.isConnected && this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(envelope))
      if (this.options.show_connector_log) {
        console.log('>>> sending:', envelope)
      }
    } else {
      this.outgoingQueue.push(envelope)
      if (this.options.show_connector_log) {
        console.log('queuing:', envelope)
      }
    }
  }
  public getState(): ConnectorState {
    return { ...this.state }
  }
  public isConnected(): boolean {
    return this.state.isConnected
  }
  public getMessageCount(): number {
    return this.state.messageCount
  }
  public getQueue(): EngineCommandEnvelope[] {
    return [...this.outgoingQueue]
  }
  private cleanup(): void {
    this.clearTimers()
    this.worker?.terminate()
    this.worker = null
    this.ws?.close()
    this.ws = null
    this.state = this.getInitialState()
    this.outgoingQueue.length = 0
    if (typeof window !== 'undefined') {
      window.removeEventListener('beforeunload', this.handleBeforeUnload)
    }
  }
  private getInitialState(): ConnectorState {
    return {
      isConnected: false,
      messageCount: 0,
      lastMessageTime: Date.now(),
      reconnectAttempts: 0,
      secondsSinceLastMessage: 0
    }
  }
  private setupWorker(): void {
    this.worker = new UnzipWorker()
    this.worker.onmessage = (event) => {
      const cmd = event.data
      if (cmd) {
        this.handleCommand(cmd)
      }
    }
    this.worker.onerror = (error) => {
      if (this.options.onError) {
        this.options.onError(error instanceof Error ? error : new Error('Worker error'))
      }
    }
  }
  private handleCommand(cmd: ClientCommand): void {
    if (!cmd?.command || !this.options.clientCommandHandlers) return
    const handler = this.options.clientCommandHandlers[`on${cmd.command}`]
    if (handler) {
      handler(cmd)
    } else {
      console.error('No handler for command:', cmd.command)
    }
    if (this.options.show_connector_log && !QUIET_COMMANDS.includes(cmd.command)) {
      console.log('WS <<< ', cmd.command)
    }
  }
  private flushQueue(): void {
    while (this.outgoingQueue.length > 0) {
      const msg = this.outgoingQueue.shift()
      if (msg) this.publish(msg)
    }
  }
  private startMessageTracking(): void {
    this.updateMessageTracking()
    this.messageTrackingInterval = window.setInterval(() => {
      this.updateMessageTracking()
    }, 1000)
  }
  private updateMessageTracking(): void {
    this.state.secondsSinceLastMessage = Math.floor(
      (Date.now() - this.state.lastMessageTime) / 1000
    )
    if (this.options.onSecondsSinceLastMessage) {
      this.options.onSecondsSinceLastMessage(this.state.secondsSinceLastMessage)
    }
  }
  private handleBeforeUnload = () => {
    if (this.options.onTerminate) {
      this.options.onTerminate({ reason: 'BROWSER_UNLOADED' })
    }
  }
  private setupBrowserEvents(): void {
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', this.handleBeforeUnload)
    }
  }
  private startReconnection(): void {
    this.clearTimers()
    const delay = this.calculateReconnectDelay()
    this.state.reconnectAttempts++
    if (this.options.show_connector_log) {
      console.log(`WebSocket closed. Reconnecting in ${delay}ms... (attempt ${this.state.reconnectAttempts})`)
    }
    setTimeout(() => this.connect(), 100)
    this.reconnectInterval = window.setInterval(() => {
      if (!this.state.isConnected) {
        if (this.options.show_connector_log) {
          console.log(`Attempting reconnection ${this.state.reconnectAttempts}...`)
        }
        this.connect()
      }
    }, delay)
  }
  private stopReconnection(): void {
    if (this.reconnectInterval !== null) {
      window.clearInterval(this.reconnectInterval)
      this.reconnectInterval = null
    }
  }
  private clearTimers(): void {
    if (this.messageTrackingInterval !== null) {
      window.clearInterval(this.messageTrackingInterval)
      this.messageTrackingInterval = null
    }
    this.stopReconnection()
  }
  private calculateReconnectDelay(): number {
    const baseDelay = Math.min(
      100 * Math.pow(2, this.state.reconnectAttempts),
      MAX_RECONNECT_DELAY
    )
    return baseDelay + Math.random() * 1000
  }
}
if (import.meta.hot) {
  import.meta.hot.accept()
}

================
File: src/unzip-worker.ts
================
import pako from "pako"
self.onmessage = function (event: MessageEvent<ArrayBuffer>) {
	try {
		console.log('Received message in worker:', `ArrayBuffer (${event.data.byteLength} bytes)`);
		const arrayBuffer = event.data
		const unzipped = pako.inflate(new Uint8Array(arrayBuffer))
		const decoded = new TextDecoder().decode(unzipped)
		const json = JSON.parse(decoded)
		console.log('Parsed message in worker:', json);
		self.postMessage(json)
	} catch (e: any) {
		console.error("Error in unzip-worker:", e['message'] ? e['message'] : e)
		self.postMessage(null)
	}
}

================
File: tests/helpers/websocket-server.ts
================
import { WebSocketServer, WebSocket } from 'ws';
import pako from 'pako';
import type { Server as HttpServer } from 'http';
export interface TestServerControls {
    server: WebSocketServer;
    httpServer?: HttpServer;
    url: string;
    close: () => Promise<void>;
    waitForConnection: (timeout?: number) => Promise<WebSocket>;
    getLastReceivedMessage: () => any | null;
    sendCompressed: (client: WebSocket, data: any) => void;
}
let lastReceivedMessage: any = null;
let connectionResolver: ((client: WebSocket) => void) | null = null;
let connectionRejecter: ((reason?: any) => void) | null = null;
let connectionTimeoutId: NodeJS.Timeout | null = null;
export function startTestServer(port: number): Promise<TestServerControls> {
    console.log(`[Test Server Helper] Attempting to start WebSocket server on port ${port}...`);
    lastReceivedMessage = null;
    connectionResolver = null;
    connectionRejecter = null;
    if (connectionTimeoutId) clearTimeout(connectionTimeoutId);
    connectionTimeoutId = null;
    return new Promise<TestServerControls>((resolveStart, rejectStart) => {
        const wss = new WebSocketServer({ port });
        const startupErrorHandler = (error: Error) => {
            console.error(`[Test Server Helper] Startup Error on port ${port}: ${error.message}`);
            wss.off('listening', listeningHandler);
            rejectStart(error);
        };
        const listeningHandler = () => {
            const address = wss.address();
            const serverUrl = `ws://localhost:${(address as any).port}`;
            console.log(`[Test Server Helper] Server listening on ${serverUrl}`);
            wss.off('error', startupErrorHandler);
            wss.on('connection', (wsClient) => {
                console.log(`[Test Server Helper] Client connected to ${serverUrl}.`);
                if (connectionResolver) {
                    if (connectionTimeoutId) clearTimeout(connectionTimeoutId);
                    connectionResolver(wsClient);
                    connectionResolver = null;
                    connectionRejecter = null;
                    connectionTimeoutId = null;
                }
                wsClient.on('message', (message) => {
                    try {
                        const messageString = message.toString();
                        console.log(`[Test Server Helper] Received on port ${port}: ${messageString.substring(0, 100)}...`);
                        const parsed = JSON.parse(messageString);
                        lastReceivedMessage = parsed;
                        if (parsed?.simplename === 'SessionConnectCommand') {
                            console.log(`[Test Server Helper] Port ${port}: Received SessionConnectCommand, sending compressed CommandSucceeded.`);
                            const response = { command: 'CommandSucceeded' };
                            sendCompressedData(wsClient, response, port);
                        }
                    } catch (e) {
                        console.error(`[Test Server Helper] Port ${port}: Error processing message:`, e);
                        lastReceivedMessage = { error: 'Failed to parse message', raw: message.toString() };
                    }
                });
                wsClient.on('close', (code) => {
                    console.log(`[Test Server Helper] Port ${port}: Client disconnected (Code: ${code}).`);
                });
                wsClient.on('error', (error) => {
                    console.error(`[Test Server Helper] Port ${port}: Client WebSocket error:`, error);
                });
            });
            wss.on('error', (error) => {
                console.error(`[Test Server Helper] Port ${port}: Server runtime error:`, error);
            });
            const waitForConnection = (timeout = 5000): Promise<WebSocket> => {
                return new Promise((resolveWait, rejectWait) => {
                    console.log(`[Test Server Helper] Port ${port}: Waiting for client connection...`);
                    connectionResolver = resolveWait;
                    connectionRejecter = rejectWait;
                    connectionTimeoutId = setTimeout(() => {
                        console.error(`[Test Server Helper] Port ${port}: Timeout waiting for client connection.`);
                        connectionResolver = null;
                        connectionRejecter = null;
                        rejectWait(new Error(`Timeout waiting for client connection on port ${port}`));
                    }, timeout);
                });
            };
            const sendCompressed = (client: WebSocket, data: any) => {
                sendCompressedData(client, data, port);
            };
            const close = (): Promise<void> => {
                console.log(`[Test Server Helper] Port ${port}: Closing server...`);
                return new Promise((resolveClose, rejectClose) => {
                    wss.close((err) => {
                        if (err) {
                            console.error(`[Test Server Helper] Port ${port}: Error closing server listener:`, err);
                        } else {
                            console.log(`[Test Server Helper] Port ${port}: Server listener closed.`);
                        }
                        const clientClosePromises: Promise<void>[] = [];
                        console.log(`[Test Server Helper] Port ${port}: Closing ${wss.clients.size} client(s)...`);
                        wss.clients.forEach(client => {
                            if (client.readyState === WebSocket.OPEN) {
                                clientClosePromises.push(new Promise<void>(resolveClient => {
                                    client.on('close', () => resolveClient());
                                    client.terminate();
                                }));
                            } else if (client.readyState !== WebSocket.CLOSED) {
                                client.terminate();
                            }
                        });
                        Promise.all(clientClosePromises).then(() => {
                            console.log(`[Test Server Helper] Port ${port}: All clients closed/terminated.`);
                            lastReceivedMessage = null;
                            connectionResolver = null;
                            connectionRejecter = null;
                            if (connectionTimeoutId) clearTimeout(connectionTimeoutId);
                            resolveClose();
                        }).catch(clientErr => {
                            console.error(`[Test Server Helper] Port ${port}: Error closing clients:`, clientErr);
                            rejectClose(clientErr);
                        });
                    });
                });
            };
            resolveStart({
                server: wss,
                url: serverUrl,
                close,
                waitForConnection,
                getLastReceivedMessage: () => lastReceivedMessage,
                sendCompressed,
            });
        };
        wss.once('listening', listeningHandler);
        wss.once('error', startupErrorHandler);
    });
}
function sendCompressedData(client: WebSocket, data: any, port: number) {
    if (client.readyState === WebSocket.OPEN) {
        try {
            const jsonString = JSON.stringify(data);
            const encoded = new TextEncoder().encode(jsonString);
            const compressed = pako.deflate(encoded);
            client.send(compressed);
            console.log(`[Test Server Helper] Port ${port}: Sent compressed data: ${jsonString.substring(0,100)}...`);
        } catch (e) {
            console.error(`[Test Server Helper] Port ${port}: Error compressing/sending data:`, e);
        }
    } else {
        console.warn(`[Test Server Helper] Port ${port}: Attempted to send data to a non-open client (state: ${client.readyState}).`);
    }
}

================
File: tests/connector-browser.spec.ts
================
import { describe, it, expect, vi } from 'vitest'
import { Connector, ConnectorOptions } from '@/connector'
import type { ClientCommand, EngineCommandEnvelope } from '@/types/generated'
describe('Connector (Browser Environment)', () => {
    beforeEach(() => {
        Connector.reset();
    });
    it('should connect, send, receive compressed message, and call handler', async () => {
        let resolveHandler: (value: ClientCommand | PromiseLike<ClientCommand>) => void;
        let rejectHandler: (reason?: any) => void;
        const commandReceivedPromise = new Promise<ClientCommand>((resolve, reject) => {
            resolveHandler = resolve;
            rejectHandler = reject;
        });
        const clientCommandHandlers: ConnectorOptions['clientCommandHandlers'] = {
            onCommandSucceeded: (cmd) => {
                resolveHandler(cmd);
            },
        };
        const connector = Connector.create({
            url: 'ws://localhost:9002/socket/',
            clientCommandHandlers,
            show_connector_log: true,
            onError: (err) => {
                console.error("[Browser Test] Connector Error:", err);
                rejectHandler(err);
            }
        });
        try {
            await vi.waitFor(() => connector.getState().isConnected, { timeout: 5000, interval: 50 });
            const commandToSend: EngineCommandEnvelope = {
                session_id: '',
                simplename: "SessionConnectCommand",
                classname: "SessionConnectCommand",
                command: {
                    browser_name: "test-runner-chrome",
                    browser_os: "test-os",
                    browser_version: "1.0",
                    sid: "session-browser-test",
                    state: "OPENED"
                }
            };
            // Publish the command
            connector.publish(commandToSend);
            // Wait for the onCommandSucceeded handler to resolve the promise
            // Add a timeout mechanism here if the test timeout isn't sufficient
            const receivedCommand = await commandReceivedPromise;
            const expectedCommand = { command: 'CommandSucceeded' };
            expect(receivedCommand).toEqual(expectedCommand);
        } finally {
            connector.close();
        }
    }, 15000);
});

================
File: tests/unzip-worker.test.ts
================
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import pako from 'pako'
interface MockWorkerGlobalScope {
    postMessage: ReturnType<typeof vi.fn>;
    onmessage: ((event: MessageEvent<ArrayBuffer>) => void) | null;
    onerror?: (event: ErrorEvent) => void;
    addEventListener: ReturnType<typeof vi.fn>;
    removeEventListener: ReturnType<typeof vi.fn>;
}
const mockSelf: MockWorkerGlobalScope = {
    postMessage: vi.fn(),
    onmessage: null,
    addEventListener: vi.fn(),
    removeEventListener: vi.fn()
};
const originalSelf = globalThis.self;
describe('Unzip Worker', () => {
    beforeEach(async () => {
        vi.resetAllMocks()
        globalThis.self = mockSelf as any;
        await import('@/unzip-worker');
    })
    afterEach(() => {
        globalThis.self = originalSelf;
        vi.resetModules();
    })
    it('should correctly unzip and parse valid data', () => {
        if (typeof mockSelf.onmessage !== 'function') {
            throw new Error("Worker script did not set self.onmessage during setup");
        }
        const testObject = { test: 'data', number: 123 }
        const textEncoder = new TextEncoder()
        const encoded = textEncoder.encode(JSON.stringify(testObject))
        const compressed = pako.deflate(encoded)
        const arrayBuffer = compressed.buffer
        const mockEvent = { data: arrayBuffer } as MessageEvent<ArrayBuffer>;
        mockSelf.onmessage(mockEvent);
        expect(mockSelf.postMessage).toHaveBeenCalledTimes(1)
        expect(mockSelf.postMessage).toHaveBeenCalledWith(testObject)
    })
    it('should handle pako errors and post null', () => {
        if (typeof mockSelf.onmessage !== 'function') {
            throw new Error("Worker script did not set self.onmessage during setup");
        }
        const invalidData = new ArrayBuffer(10);
        const mockEvent = { data: invalidData } as MessageEvent<ArrayBuffer>;
        mockSelf.onmessage(mockEvent);
        expect(mockSelf.postMessage).toHaveBeenCalledTimes(1)
        expect(mockSelf.postMessage).toHaveBeenCalledWith(null)
    })
    it('should handle JSON parsing errors and post null', () => {
        if (typeof mockSelf.onmessage !== 'function') {
            throw new Error("Worker script did not set self.onmessage during setup");
        }
        const textEncoder = new TextEncoder()
        const encoded = textEncoder.encode("this is not json")
        const compressed = pako.deflate(encoded)
        const arrayBuffer = compressed.buffer
        const mockEvent = { data: arrayBuffer } as MessageEvent<ArrayBuffer>;
        mockSelf.onmessage(mockEvent);
        expect(mockSelf.postMessage).toHaveBeenCalledTimes(1)
        expect(mockSelf.postMessage).toHaveBeenCalledWith(null)
    })
})

================
File: tests/websocket-server-node.test.ts
================
import { describe, it, expect, beforeAll, afterAll, vi } from 'vitest';
import { WebSocketServer, WebSocket } from 'ws';
const TEST_PORT = 8999;
let wss: WebSocketServer | null = null;
let serverUrl: string;
beforeAll(async () => {
    console.log(`[Server Setup] Starting WebSocket server on port ${TEST_PORT}...`);
    try {
        wss = await new Promise<WebSocketServer>((resolve, reject) => {
            const server = new WebSocketServer({ port: TEST_PORT });
            server.on('listening', () => {
                const address = server.address();
                if (address && typeof address !== 'string') {
                    serverUrl = `ws://localhost:${address.port}`;
                    console.log(`[Server Setup] Server listening on ${serverUrl}`);
                    server.removeAllListeners('error');
                    resolve(server);
                } else {
                    const errMsg = `[Server Setup] Failed to get server address. Address: ${address}`;
                    console.error(errMsg);
                    server.close(() => reject(new Error(errMsg)));
                }
            });
            server.on('error', (error) => {
                console.error(`[Server Setup] Server error during startup: ${error.message}`);
                server.removeAllListeners('listening');
                server.close(() => reject(error));
            });
            server.on('connection', (wsClient) => {
                console.log('[Test Server] Client connected');
                wsClient.send('Hello from server!');
                wsClient.on('message', (message) => {
                    console.log(`[Test Server] Received message: ${message.toString()}`);
                    wsClient.send(`Server received: ${message.toString()}`);
                });
                wsClient.on('close', (code, reason) =>
                    console.log(`[Test Server] Client disconnected (Code: ${code}, Reason: ${reason.toString()})`)
                );
                wsClient.on('error', (error) =>
                    console.error('[Test Server] Client error:', error)
                );
            });
            server.on('error', (error) => {
                console.error(`[Test Server] Runtime error: ${error.message}`);
            });
        });
        console.log('[Server Setup] Server startup promise resolved.');
    } catch (error: any) {
        console.error(`[Server Setup] Failed to start server: ${error.message}`);
        wss = null;
        throw error;
    }
}, 30000);
afterAll(async () => {
    console.log("[Server Teardown] Starting cleanup...");
    if (!wss) {
        console.log('[Server Teardown] Server was not running or already cleaned up.');
        return;
    }
    const serverInstance = wss;
    wss = null;
    await new Promise<void>((resolve, reject) => {
        console.log('[Server Teardown] Closing Test WebSocket Server...');
        let closedClients = 0;
        const totalClients = serverInstance.clients.size;
        if (totalClients > 0) {
            console.log(`[Server Teardown] Closing ${totalClients} client(s)...`);
            serverInstance.clients.forEach((client) => {
                if (client.readyState === WebSocket.OPEN) {
                    client.close(1001, "Server shutting down");
                }
            });
        }
        serverInstance.close((err) => {
            if (err) {
                console.error("[Server Teardown] Error closing test server:", err);
                reject(err);
            } else {
                console.log("[Server Teardown] Test WebSocket Server closed successfully.");
                resolve();
            }
        });
    });
    console.log("[Server Teardown] Cleanup complete.");
}, 10000);
describe('WebSocket Server (Node.js Client Test)', () => {
    it('should connect a Node.js client, receive a message, and close gracefully', async () => {
        console.log("[Test Case] Running test...");
        expect(wss, "WebSocket server instance should exist").toBeDefined();
        expect(serverUrl, "Server URL should be defined").toBeDefined();
        const receivedMessage = await new Promise<string>((resolve, reject) => {
            console.log(`[Test Client] Connecting to ${serverUrl}...`);
            const wsClient = new WebSocket(serverUrl);
            const connectTimeout = setTimeout(() => {
                console.error('[Test Client] Connection timed out.');
                wsClient.terminate();
                reject(new Error('WebSocket connection timed out'));
            }, 5000);
            const messageTimeout = setTimeout(() => {
                console.error('[Test Client] Message receive timed out.');
                wsClient.terminate();
                reject(new Error('WebSocket message receive timed out'));
            }, 7000);
            wsClient.on('open', () => {
                clearTimeout(connectTimeout);
                console.log('[Test Client] Connected successfully.');
            });
            wsClient.on('message', (message: Buffer) => {
                clearTimeout(messageTimeout);
                const messageStr = message.toString();
                console.log(`[Test Client] Received message: "${messageStr}"`);
                wsClient.close(1000, 'Test complete');
                console.log('[Test Client] Closing connection after receiving message.');
                resolve(messageStr);
            });
            wsClient.on('error', (error) => {
                clearTimeout(connectTimeout);
                clearTimeout(messageTimeout);
                console.error('[Test Client] WebSocket error:', error);
                if (wsClient.readyState !== WebSocket.CLOSED && wsClient.readyState !== WebSocket.CLOSING) {
                    wsClient.terminate();
                }
                reject(error);
            });
            wsClient.on('close', (code, reason) => {
                clearTimeout(connectTimeout);
                clearTimeout(messageTimeout);
                console.log(`[Test Client] Connection closed (Code: ${code}, Reason: "${reason.toString()}")`);
                if (code !== 1000 && code !== 1001 ) {
                    console.warn(`[Test Client] Connection closed with unexpected code ${code}`)
                }
            });
        });
        console.log("[Test Case] Message promise resolved.");
        expect(receivedMessage).toBe('Hello from server!');
        console.log("[Test Case] Assertion passed.");
    }, 10000);
});

================
File: package.json
================
{
  "name": "connector-test-suite",
  "version": "1.0.0",
  "private": true,
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "test": "vitest",
    "test:run": "vitest run",
    "coverage": "vitest run --coverage",
    "typecheck": "tsc --noEmit"
  },
  "dependencies": {
    "pako": "^2.1.0",
    "uuid": "^11.1.0"
  },
  "devDependencies": {
    "@playwright/test": "^1.52.0",
    "@types/node": "^22.15.3",
    "@types/pako": "^2.0.3",
    "@types/uuid": "^10.0.0",
    "@types/ws": "^8.18.1",
    "@vitest/browser": "^3.1.2",
    "@vitest/coverage-v8": "^3.1.2",
    "typescript": "^5.8.3",
    "vite": "^6.3.3",
    "vitest": "^3.1.2",
    "ws": "^8.18.1"
  }
}

================
File: tsconfig.json
================
{
  "compilerOptions": {
    "target": "ESNext",
    "module": "ESNext",
    "moduleResolution": "Bundler", // Recommended for Vite/ESM
    "lib": ["ESNext", "DOM", "DOM.Iterable", "WebWorker"],
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "resolveJsonModule": true,
    "isolatedModules": true, // Required by Vite
    "noEmit": true,          // Let Vite handle builds
    "jsx": "preserve",       // Or "react-jsx" if needed by dependencies
    "baseUrl": ".",          // Important for path resolution
    "paths": {
      "@/*": ["./src/*"]
    },
    "types": ["vite/client", "vitest/globals"] // Include Vite env types and Vitest globals
  },
  "include": [
    "src/**/*.ts",
    "tests/**/*.ts",
    "vite.config.ts",
    "vitest.config.ts"
  ],
  "exclude": ["node_modules", "dist"]
}

================
File: vite-env.d.ts
================
interface ImportMetaEnv {
    readonly VITE_TEST_WS_URL: string
}
interface ImportMeta {
    readonly env: ImportMetaEnv
}

================
File: vite.config.ts
================
import { defineConfig } from 'vite'
import path from 'path'
export default defineConfig({
    resolve: {
        alias: {
            '@': path.resolve(__dirname, './src'),
        },
    },
    worker: {
        format: 'es',
    },
})

================
File: vitest.config.ts
================
import { defineConfig } from 'vitest/config'
import path from 'path';
export default defineConfig({
    test: {
        globalSetup: './vitest.global-setup.ts',
        coverage: {
            provider: 'v8',
            reporter: ['text', 'json', 'html', 'lcov'],
            include: ['src/**/*.ts'],
            exclude: [
                'src/types/**/*.ts',
            ],
            all: true,
            clean: true,
        },
        workspace: [
            {
                test: {
                    name: 'node',
                    globals: true,
                    environment: 'node',
                    include: ['tests/**/*.test.ts'],
                    exclude: ['tests/**/*.spec.ts'],
                    testTimeout: 10000,
                    pool: 'threads',
                    alias: { '@': path.resolve(__dirname, './src') },
                }
            },
            {
                test: {
                    name: 'browser',
                    globals: true,
                    environment: 'browser',
                    include: ['tests/**/*.spec.ts'],
                    exclude: ['tests/**/*.test.ts'],
                    testTimeout: 15000,
                    browser: {
                        enabled: true,
                        provider: 'playwright',
                        instances: [{ browser: 'chromium' }],
                        headless: process.env.CI === 'true' || process.env.HEADLESS === 'true',
                    },
                    alias: { '@': path.resolve(__dirname, './src') },
                }
            }
        ]
    }
});

================
File: vitest.global-setup.ts
================
import { startTestServer, TestServerControls } from './tests/helpers/websocket-server';
const GLOBAL_TEST_PORT = 9002;
let globalServerControls: TestServerControls | null = null;
export async function setup(): Promise<() => Promise<void>> {
    console.log(`[Global Setup v3.1.2] Starting WebSocket server on port ${GLOBAL_TEST_PORT}...`);
    try {
        globalServerControls = await startTestServer(GLOBAL_TEST_PORT);
        const serverUrl = globalServerControls.url;
        console.log(`[Global Setup v3.1.2] Server started at ${serverUrl}`);
        process.env.VITE_TEST_WS_URL = serverUrl;
        console.log(`[Global Setup v3.1.2] Set VITE_TEST_WS_URL=${process.env.VITE_TEST_WS_URL}`);
        return async () => {
            console.log('[Global Teardown v3.1.2] Stopping WebSocket server...');
            if (globalServerControls) {
                await globalServerControls.close();
                console.log('[Global Teardown v3.1.2] Server stopped.');
                globalServerControls = null;
            } else {
                console.warn('[Global Teardown v3.1.2] Server controls not found, server might not have started correctly.');
            }
        };
    } catch (error) {
        console.error('[Global Setup v3.1.2] Failed to start WebSocket server:', error);
        process.exit(1);
    }
}



================================================================
End of Codebase
================================================================
