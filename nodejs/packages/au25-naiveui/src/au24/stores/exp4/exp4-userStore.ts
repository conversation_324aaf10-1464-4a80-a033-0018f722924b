// taken from exp4-naiveui-segmented-page

import { defineStore } from 'pinia';
import { type User, UserRole, type UserGroup, type Invitation } from '@/au24/stores/exp4/exp4-domain';
import { initializeExp4StoreWithMockData } from "@/au24/stores/exp4/exp4-mock-data"

// Utility function to simulate async delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Pinia Store using Options API
export const useExp4UserGroupStore = defineStore('userGroup', {
	state: () => ({
		users: [] as User[],
		admins: [] as UserGroup<UserRole.ADMIN>[],
		managers: [] as UserGroup<UserRole.MANAGER>[],
		employees: [] as UserGroup<UserRole.EMPLOYEE>[],
		invitations: [] as Invitation[]
	}),

	getters: {
		getUserById: (state) => {
			return (id: string) => state.users.find(user => user.id === id);
		},
		getGroupById: (state) => {
			return (id: string) => {
				return [...state.admins, ...state.managers, ...state.employees].find(group => group.id === id);
			};
		}
	},

	actions: {
		async createUser(user: User) {
			await delay(500); // Simulate API call
			this.users.push(user);
		},

		async updateUser(id: string, updatedUser: User) {
			await delay(500); // Simulate API call
			const index = this.users.findIndex(user => user.id === id);
			if (index !== -1) {
				this.users[index] = updatedUser;
			}
		},

		async deleteUser(id: string) {
			await delay(500); // Simulate API call
			this.users = this.users.filter(user => user.id !== id);
		},

		async createGroup(group: UserGroup<UserRole>) {
			await delay(500); // Simulate API call
			switch (group.role) {
				case UserRole.ADMIN:
					this.admins.push(group as UserGroup<UserRole.ADMIN>);
					break;
				case UserRole.MANAGER:
					this.managers.push(group as UserGroup<UserRole.MANAGER>);
					break;
				case UserRole.EMPLOYEE:
					this.employees.push(group as UserGroup<UserRole.EMPLOYEE>);
					break;
			}
		},

		async updateGroup(id: string, updatedGroup: UserGroup<UserRole>) {
			await delay(500); // Simulate API call
			const updateInArray = (arr: UserGroup<UserRole>[]) => {
				const index = arr.findIndex(group => group.id === id);
				if (index !== -1) {
					arr[index] = updatedGroup;
				}
			};

			updateInArray(this.admins);
			updateInArray(this.managers);
			updateInArray(this.employees);
		},

		async deleteGroup(id: string) {
			await delay(500); // Simulate API call
			this.admins = this.admins.filter(group => group.id !== id);
			this.managers = this.managers.filter(group => group.id !== id);
			this.employees = this.employees.filter(group => group.id !== id);
		},

		async sendInvitation(email: string, role: UserRole, groupId: string, invitedBy: string) {
			await delay(500); // Simulate API call
			const newInvitation: Invitation = {
				id: Date.now().toString(),
				email,
				role,
				groupId,
				invitedBy,
				invitedAt: new Date(),
				expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
				status: 'PENDING'
			};
			this.invitations.push(newInvitation);
		},

		initializeMockData() {
			console.log("initializeMockData")
			initializeExp4StoreWithMockData(this);
		}
	}
});
