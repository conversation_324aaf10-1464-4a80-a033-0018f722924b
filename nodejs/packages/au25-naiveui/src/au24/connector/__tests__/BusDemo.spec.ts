import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"
import { mount } from "@vue/test-utils"
import BusDemo from "./BusDemo.vue"
import { NetworkDown, useCommandBus } from "../command-buses"

describe("BusDemo", () => {
	let wrapper
	const consoleLogSpy = vi.spyOn(console, "log")
	const { networkDownBus } = useCommandBus()

	beforeEach(() => {
		wrapper = mount(BusDemo)
	})

	afterEach(() => {
		consoleLogSpy.mockClear()
		networkDownBus.reset()
		networkUpBus.reset()
	})

	it("renders the slot content", () => {
		wrapper = mount(BusDemo, {
			slots: {
				default: "<p>Hello, World!</p>"
			}
		})

		expect(wrapper.html()).toContain("<p>Hello, World!</p>")
	})

	it("subscribes to NetworkDown event and logs the event", () => {
		//networkDownBus.emit(new NetworkDown())
		networkDownBus.emit(new NetworkDown())

		expect(consoleLogSpy).toHaveBeenCalledTimes(2)
		expect(consoleLogSpy).toHaveBeenCalledWith("NetworkDown event received")
	})

	it("unsubscribes from all events when unsubscribeAll is called", () => {
		networkDownBus.reset()
		networkDownBus.emit(new NetworkDown())
		expect(consoleLogSpy).toHaveBeenCalledTimes(2)
	})
})
