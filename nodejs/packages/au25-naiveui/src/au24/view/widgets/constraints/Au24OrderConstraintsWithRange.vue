<!-- {'model': 'claude-3-sonnet-20240229', 'input_tokens': 2425, 'output_tokens': 1185, 'input_cost': '$0.007275', 'output_cost': '$0.017775', 'total_cost': '$0.025050'} -->
Here's the Vue 3 version of the provided code, using Naive UI:

<template>
	<div class="DeOrderConstraintsWithRange">
		<div class="_heading" :style="`left: 10px; color:${colors.au_buy_dimmed()}`">
			Buy:
			{{ buy_range }}
		</div>
		<div class="_heading" :style="`left: 10px; color:${colors.au_sell_dim()}`">
			Sell:
			{{ sell_range }}
		</div>
		<DeOrderConstraintsBar
			style="margin-top: 4px"
			:width="250"
			:height="12"
			:tick_font_size="10"
			:constraints="constraints"
			:order_quantity="order_quantity"
			:order_type="order_type"
		/>
	</div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue"
import DeOrderConstraintsBar from "./Au24OrderConstraintsBar.vue"
import { type DeBidConstraints, OrderType } from "@/au24/types/generated.js"
import {auColors, AuColors} from "@/au24/au-styles/AuColors";

const props = withDefaults(
	defineProps<{
		order_quantity: number
		order_type: OrderType
		constraints: DeBidConstraints
		quantity_label: string
	}>(),
	{}
)

const colors: AuColors = auColors

const constraints = ref<DeBidConstraints>({
	max_buy_quantity: 40,
	max_sell_quantity: 20,
	min_buy_quantity: 30,
	min_sell_quantity: 15
})

const buy_range = computed(() => {
	if (constraints.value.min_buy_quantity === 0 && constraints.value.max_buy_quantity === 0) return "none"
	else if (constraints.value.min_buy_quantity === constraints.value.max_buy_quantity)
		return constraints.value.max_buy_quantity + ""
	else return `${constraints.value.min_buy_quantity} to ${constraints.value.max_buy_quantity} ${props.quantity_label}`
})

const sell_range = computed(() => {
	if (constraints.value.min_sell_quantity === 0 && constraints.value.max_sell_quantity === 0) return "none"
	else if (constraints.value.min_sell_quantity === constraints.value.max_sell_quantity)
		return constraints.value.max_sell_quantity + ""
	else
		return `${constraints.value.min_sell_quantity} to ${constraints.value.max_sell_quantity} ${props.quantity_label}`
})
</script>

<style lang="less" scoped>
@import (reference) "@/au24/au-styles/variables.less";

.DeOrderConstraintsWithRange {
	font-size: 12px;
	height: 63px;
	overflow: hidden;
	padding: 2px;
	width: 260px;

	._heading {
		display: inline-block;
		font-size: 12px;
		font-weight: bold;
		margin-left: 8px;
		text-align: left;
		width: 120px;
	}
}
</style>
