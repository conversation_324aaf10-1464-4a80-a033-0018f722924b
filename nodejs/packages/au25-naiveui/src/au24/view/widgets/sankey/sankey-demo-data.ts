import { DeMatrixEdgeElement, DeRoundTraderElement } from "@/au24/types/generated.ts"

export const sampleSankeyTraders: DeRoundTraderElement[] = [
	{
		company_shortname: "TraderA",
		cid: "TA001",
		constraints: {
			max_buy_quantity: 100,
			max_sell_quantity: 80,
			min_buy_quantity: 0,
			min_sell_quantity: 0
		}
	},
	{
		company_shortname: "TraderB",
		cid: "TB002",
		constraints: {
			max_buy_quantity: 120,
			max_sell_quantity: 90,
			min_buy_quantity: 0,
			min_sell_quantity: 0
		}
	},
	{
		company_shortname: "TraderC",
		cid: "TC003",
		constraints: {
			max_buy_quantity: 80,
			max_sell_quantity: 110,
			min_buy_quantity: 0,
			min_sell_quantity: 0
		}
	},
	{
		company_shortname: "TraderD",
		cid: "TD004",
		constraints: {
			max_buy_quantity: 150,
			max_sell_quantity: 100,
			min_buy_quantity: 0,
			min_sell_quantity: 0
		}
	}
] as DeRoundTraderElement[]

export const sampleSankeyEdges: DeMatrixEdgeElement[] = [
	{
		seller_cid: "TA001",
		buyer_cid: "TC003",
		match: 30
	},
	{
		seller_cid: "TA001",
		buyer_cid: "TD004",
		match: 40
	},
	{
		seller_cid: "TB002",
		buyer_cid: "TC003",
		match: 50
	},
	{
		seller_cid: "TB002",
		buyer_cid: "TD004",
		match: 70
	}
] as DeMatrixEdgeElement[]
