<template>
	<div>
		<NButton @click="getSelectedCompanies">Get Selected Companies</NButton>
		<TraderSelectTable ref="traderSelectTable" :companies="companies" :height="tableHeight" :width="tableWidth" />
	</div>
</template>

<script setup lang="ts">
import { ref } from "vue"
import TraderSelectTable from "./TraderSelectTable.vue"
import { type CompanyElement } from "@/au24/types/generated.js"
import { times } from "lodash"
import { createDemo__CompanyElement } from "@/au24/helpers/demo-helpers/CompanyElement.helper.ts"

const traderSelectTable = ref<InstanceType<typeof TraderSelectTable> | null>(null)

const companies = ref<Array<CompanyElement>>(times(30, createDemo__CompanyElement))
const tableHeight = 500
const tableWidth = 500

const getSelectedCompanies = () => {
	// Do something with the selected companies
	if (traderSelectTable.value) {
		const selectedCompanyIds = traderSelectTable.value.getSelectedCompanies()?.map(c => c.id) || []
		console.log("Selected Companies from exposed selection:", selectedCompanyIds)
	}
}
</script>
