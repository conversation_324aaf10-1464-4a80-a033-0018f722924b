<template>
	<n-data-table
		:columns="columns"
		:data="companies"
		:max-height="height"
		:min-height="height"
		:row-key="rowKey"
		:row-props="rowProps"
		:checked-row-keys="selected_companies"
		@update:checked-row-keys="handleCheck"
	/>
</template>

<script setup lang="ts">
import { DataTableColumns, DataTableRowKey, NDataTable } from "naive-ui"
import { CompanyElement } from "@/au24/types/generated.ts"
import { computed } from "vue"

const props = defineProps<{
	companies: CompanyElement[]
	height: number
	width: number
}>()

const selected_companies = defineModel<string[]>()

const rowKey = (row: CompanyElement) => row.id
const selectedSet = computed(() => new Set(selected_companies.value))

const handleCheck = (rowKeys: DataTableRowKey[]) => {
	selected_companies.value = rowKeys.map(key => key as string)
}

const toggleRowSelection = (row: CompanyElement) => {
	const currentSelected = selected_companies.value || []
	if (selectedSet.value.has(row.id)) {
		selected_companies.value = currentSelected.filter(id => id !== row.id)
	} else {
		selected_companies.value = [...currentSelected, row.id]
	}
}

const rowProps = (row: CompanyElement) => {
	return {
		style: 'cursor: pointer;',
		onClick: (e: MouseEvent) => {
			// Prevent toggling if clicking on the checkbox itself
			if ((e.target as HTMLElement).closest('.n-checkbox')) {
				return
			}
			toggleRowSelection(row)
		}
	}
}

const columns: DataTableColumns<CompanyElement> = [
	{
		type: "selection",
		disabled(row: CompanyElement) {
			return false
		}
	},
	{
		title: "Company Short Name",
		key: "company_shortname",
		sorter: "default"
	},
	{
		title: "Company Long Name",
		key: "company_longname",
		defaultSortOrder: "ascend",
		sorter: "default"
	}
]
</script>

<style scoped>
</style>
