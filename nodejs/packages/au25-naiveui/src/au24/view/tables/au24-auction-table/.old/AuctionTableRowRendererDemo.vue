<template>
	<div>
		<div>
			<span>is auctioneer</span>
			<n-switch v-model:value="isAuctioneer" label="Is Auctioneer">
				<template #checked>yes</template>
				<template #unchecked>no</template>
			</n-switch>
		</div>
		<br />
		<hr />
		<div style="width: 80%; overflow: hidden; border: 1px solid red">
			<AuctionTableRowRenderer :params></AuctionTableRowRenderer>
		</div>
	</div>
</template>

<script setup lang="ts">
import AuctionTableRowRenderer from "@/au24/view/tables/au24-auction-table/AuctionTableRowRenderer.vue"
import { AuctionRowElement } from "@/au24/types/generated.ts"
import { createDemo__AuctionRowElement } from "@/au24/helpers/demo-helpers/AuctionRowElement.helper.ts"
import { computed, Ref, ref, watch } from "vue"

const isAuctioneer = ref(false)

const auction: Ref<AuctionRowElement> = computed(() => createDemo__AuctionRowElement(isAuctioneer.value, 1))

const params = computed(() => ({
	data: createDemo__AuctionRowElement(isAuctioneer.value, 1),
	isAuctioneer: isAuctioneer.value
}))

watch(params, () => {
	console.log(params.value)
})
</script>

<style scoped></style>
