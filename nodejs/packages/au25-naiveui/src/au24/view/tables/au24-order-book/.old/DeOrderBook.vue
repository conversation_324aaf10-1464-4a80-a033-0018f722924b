<template>
	<div>
		<!--		<TableHeading>-->
		<!--			<div style="display: inline-block">Order Book</div>-->
		<!--		</TableHeading>-->
		<AuBaseTable
			class="DeOrderBook"
			:width
			:height
			:columnDefs
			:rowData
			:gridOptions
			:getRowHeight="() => row_height"
		/>
	</div>
</template>

<script setup lang="ts">
import { computed } from "vue"
import DeOrderBookCell from "./DeOrderBookCell.vue"
import { ColDef, GridApi, GridOptions } from "ag-grid-community"
import { DeOrderBookRow } from "./DeOrderBookRow.ts"
import { range } from "lodash"
import { type CompanyElement, type DeRoundTraderElement, OrderType } from "@/au24/types/generated.js"
import AuBaseTable from "@/au24/view/tables/au24-base-table/.old/AuBaseTable.vue"
import AuAgGridCenteredHeader from "@/au24/view/ui-component/AuAgGridCenteredHeader.vue"

const props = defineProps<{
	height: number
	round_trader_elements: DeRoundTraderElement[]
	companies: CompanyElement[]
	quantity_label: string
}>()

const width = 330
const row_height = 40

let gridApi: GridApi | null = null
const gridOptions: GridOptions = {
	defaultColDef: {
		cellRenderer: DeOrderBookCell,
		headerComponent: AuAgGridCenteredHeader,
		cellStyle: () => ({ padding: "0", border: "0" })
	},
	headerHeight: 33,
	suppressHorizontalScroll: true
}

function createCellDefinition(headerName: string, width: number): ColDef {
	return { headerName: headerName, width: width }
}

function getCompany(companyId: string): CompanyElement {
	const result = props.companies.find(c => c.company_id === companyId)
	if (!result) {
		throw new Error(`Company with id ${companyId} is not found`)
	}
	return result
}

const cells_by_side = computed(() => {
	const sellCells: DeRoundTraderElement[] = []
	const buyCells: DeRoundTraderElement[] = []
	props.round_trader_elements.forEach(cell => {
		if (cell.order_type === OrderType.SELL) {
			sellCells.push(cell)
		} else if (cell.order_type === OrderType.BUY) {
			buyCells.push(cell)
		}
	})
	return {
		sellCells,
		buyCells,
		maxCellList: sellCells.length > buyCells.length ? sellCells : buyCells
	}
})

const empty_row_count = computed(() => {
	const body_height = props.height - 40
	const rows_height = cells_by_side.value.buyCells.length * row_height
	const empty_height = body_height - rows_height
	return empty_height > 0 ? Math.ceil(empty_height / row_height) : 0
})

const rowData = computed(() => {
	const rows: DeOrderBookRow[] = []

	cells_by_side.value.maxCellList.forEach((value, index) => {
		const row: DeOrderBookRow = {
			buyCompanyShort: "",
			buySubmittedBy: "",
			buyTime: "",
			buyTimeStr: "",
			buyQuantityInt: 0,
			buyQuantityStr: "",
			hasBuy: false,
			hasSell: true,
			match: 0,
			id: index + "",
			sellCompanyShort: "",
			sellSubmittedBy: "",
			sellTime: "",
			sellQuantityInt: 0,
			sellQuantityStr: ""
		}

		const sellCell = cells_by_side.value.sellCells[index] as DeRoundTraderElement | undefined
		if (sellCell) {
			row.sellTime = sellCell.timestamp_formatted
			row.sellCompanyShort = getCompany(sellCell.cid).company_shortname
			row.sellSubmittedBy = sellCell.order_submitted_by
			row.sellQuantityInt = sellCell.quantity_int
			row.sellQuantityStr = sellCell.quantity_str
			row.match = 10
			row.hasSell = true
		}

		const buyCell = cells_by_side.value.buyCells[index] as DeRoundTraderElement | undefined
		if (buyCell) {
			row.buyTime = buyCell.timestamp_formatted
			row.buyCompanyShort = getCompany(buyCell.cid).company_shortname
			row.buySubmittedBy = buyCell.order_submitted_by
			row.buyQuantityInt = buyCell.quantity_int
			row.buyQuantityStr = buyCell.quantity_str
			row.match = 10
			row.hasBuy = true
		}

		rows.push(row)
	})

	range(empty_row_count.value).forEach(index => {
		rows.push({
			id: "empty-row." + index
		} as DeOrderBookRow)
	})

	return rows
})

const columnDefs = computed((): ColDef[] => {
	return [
		{
			headerName: "Buyer",
			field: "buyCompanyShort",
			width: 110
		},
		{
			headerName: `Buy (${props.quantity_label})`,
			field: "buyQuantity",
			width: 69
		},
		{
			headerName: `Sell (${props.quantity_label})`,
			field: "sellQuantity",
			width: 69
		},
		{
			headerName: "Seller",
			field: "sellCompanyShort",
			width: 110
		}
	]
})
</script>

<style lang="less" scoped>
.DeOrderBook {
	// Add any component-specific styles here
}
</style>
