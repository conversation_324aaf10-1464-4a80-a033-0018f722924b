<template>
	<div>
		<pre>selected: {{ selectedCompany && selectedCompany.company_shortname }}</pre>
		<CompanyTable :companies="companies" :height="500" :width="500" :on-company-selected="onCompanySelected" />
	</div>
</template>

<script setup lang="ts">
import { ref } from "vue"
import CompanyTable from "./CompanyTable.vue"
import { times } from "lodash"
import { createDemo__CompanyElement } from "@/au24/helpers/demo-helpers/CompanyElement.helper.ts"
import { type CompanyElement } from "@/au24/types/generated.js"

const companies = times(30, createDemo__CompanyElement)
const selectedCompany = ref<CompanyElement | null>(null)

const onCompanySelected = (company: CompanyElement) => {
	selectedCompany.value = company
}
</script>
