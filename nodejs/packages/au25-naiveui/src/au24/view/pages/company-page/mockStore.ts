import { ref } from 'vue'
import _ from 'lodash'
import { createDemo__CompanyElement } from '../../../helpers/demo-helpers/CompanyElement.helper'
import type { CompanyElement } from '../../../types/generated'

export const useStore = () => {
  const companies = ref<CompanyElement[]>([])

  const initializeCompanies = () => {
    companies.value = _.times(30, createDemo__CompanyElement)
  }

  return {
    companies,
    initializeCompanies
  }
}