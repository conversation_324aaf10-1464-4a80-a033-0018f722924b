<script setup lang="ts">
import { ref, computed, defineComponent, h } from 'vue'
import type { SelectOption } from 'naive-ui'
import {
    NCard,
    NButton,
    NSpace,
    NInputGroup,
    NDivider,
    NDataTable,
    NSelect
} from 'naive-ui'

// Dummy data for 4 rounds
const roundsData = [
    {
        round: 1,
        totalBuy: 1000,
        totalSell: 800,
        matched: 800,
        potential: 850,
        price: "10.50"
    },
    {
        round: 2,
        totalBuy: 900,
        totalSell: 850,
        matched: 850,
        potential: 870,
        price: "10.75"
    },
    {
        round: 3,
        totalBuy: 875,
        totalSell: 870,
        matched: 870,
        potential: 870,
        price: "11.00"
    },
    {
        round: 4,
        totalBuy: 870,
        totalSell: 870,
        matched: 870,
        potential: 870,
        price: "11.25"
    }
];

const matchesData = [
    {
        id: "T1",
        name: "Trader 1",
        type: "sell",
        total: 300,
        matches: [
            { counterparty: "Trader 4", quantity: 200 },
            { counterparty: "Trader 5", quantity: 100 }
        ]
    },
    {
        id: "T2",
        name: "Trader 2",
        type: "sell",
        total: 570,
        matches: [
            { counterparty: "Trader 5", quantity: 320 },
            { counterparty: "Trader 6", quantity: 250 }
        ]
    },
    {
        id: "T4",
        name: "Trader 4",
        type: "buy",
        total: 200,
        matches: [
            { counterparty: "Trader 1", quantity: 200 }
        ]
    },
    {
        id: "T5",
        name: "Trader 5",
        type: "buy",
        total: 420,
        matches: [
            { counterparty: "Trader 1", quantity: 100 },
            { counterparty: "Trader 2", quantity: 320 }
        ]
    },
    {
        id: "T6",
        name: "Trader 6",
        type: "buy",
        total: 250,
        matches: [
            { counterparty: "Trader 2", quantity: 250 }
        ]
    }
];

// State
const currentRound = ref(3)
const matchingAlgorithm = ref('timestamp')
const viewMode = ref('all')

const maxMatchRound = 4
const currentRoundData = computed(() => roundsData[currentRound.value - 1])

// Simple Sankey component
const SimpleSankey = defineComponent({
    props: {
        data: {
            type: Object,
            required: true
        }
    },
    setup(props) {
        // Convert to n-space with custom styling
        const scale = (value) => {
            const maxValue = Math.max(props.data.totalSell, props.data.matched, props.data.totalBuy)
            return (value / maxValue) * 100
        }

        return () => {
            return h(NSpace, { vertical: true, justify: 'center', align: 'center' }, {
                // Render sankey bars using n-space and n-divider
                default: () => [
                    // Total Sold -> Sellers
                    // ... similar structure but using naive UI components
                ]
            })
        }
    }
})

// Switch rounds
const switchRound = (direction) => {
    if (direction === 'next') {
        currentRound.value = Math.min(4, currentRound.value + 1)
    } else if (direction === 'prev') {
        currentRound.value = Math.max(1, currentRound.value - 1)
    } else if (direction === 'first') {
        currentRound.value = 1
    } else if (direction === 'last') {
        currentRound.value = 4
    }
}

// Filter matches based on view mode
const filteredMatches = computed(() => {
    if (viewMode.value === 'all') return matchesData
    return matchesData.filter(trader => trader.type === viewMode.value)
})
</script>

<template>
    <n-space vertical>
        <!-- Header Card -->
        <n-card>
            <template #header>
                <n-space justify="space-between" align="center">
                    <!-- Round Controls -->
                    <n-space>
                        <n-button
                                @click="switchRound('first')"
                                :disabled="currentRound === 1"
                        >
                            <template #icon>
                                <i-chevrons-left />
                            </template>
                        </n-button>

                        <!-- ... Other round controls -->
                    </n-space>

                    <!-- Algorithm Toggle -->
                    <n-button
                            @click="matchingAlgorithm = matchingAlgorithm === 'timestamp' ? 'minimum' : 'timestamp'"
                            type="primary"
                    >
                        {{ matchingAlgorithm === 'timestamp' ? 'Timestamp Order' : 'Minimum Matches' }}
                    </n-button>
                </n-space>
            </template>

            <!-- Stats Grid -->
            <n-grid cols="6" :x-gap="12">
                <n-grid-item>
                    <n-statistic label="Total Sell" :value="currentRoundData.totalSell" />
                </n-grid-item>
                <!-- ... Other stats -->
            </n-grid>
        </n-card>

        <!-- Main Content -->
        <n-space>
            <!-- Matches Table -->
            <n-card style="width: 66%">
                <template #header>
                    <n-space justify="space-between">
                        <n-text>Matches</n-text>
                        <n-radio-group v-model:value="viewMode">
                            <n-radio-button value="all">All</n-radio-button>
                            <n-radio-button value="sellers">Sellers</n-radio-button>
                            <n-radio-button value="buyers">Buyers</n-radio-button>
                        </n-radio-group>
                    </n-space>
                </template>

                <n-data-table :data="filteredMatches">
                    <!-- Table columns config -->
                </n-data-table>
            </n-card>

            <!-- Flow Visualization -->
            <n-card style="width: 33%">
                <template #header>
                    <n-text>Flow Visualization</n-text>
                </template>
                <SimpleSankey :data="currentRoundData" />
            </n-card>
        </n-space>

        <!-- History Chart -->
        <n-card>
            <template #header>
                <n-text>Round History</n-text>
            </template>
            <!-- History Chart component -->
        </n-card>

    </n-space>
</template>

<style scoped>
/* Add any component specific styles */
</style>
