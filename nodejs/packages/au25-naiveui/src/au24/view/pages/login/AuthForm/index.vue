<template>
	<div class="form-wrap">
		<Logo mini :dark="isDark" class="mb-4" />
		<div class="title mb-4">{{ title }}</div>
		<div class="text mb-12">Sign in to auction platform.</div>

		<div class="form">
			<transition name="form-fade" mode="out-in" appear>
				<AuSignIn v-if="typeRef === 'signin'" key="signin" @forgot-password="gotoForgotPassword()" />
				<AuForgotPassword v-else-if="typeRef === 'forgotpassword'" key="forgotpassword" />
				<AuSignUp v-else-if="typeRef === 'signup'" key="signup" />
			</transition>
		</div>

		<n-divider title-placement="center">Or</n-divider>

		<div class="social-btns flex flex-col gap-4 mb-12">
			<n-button strong secondary size="large">
				<span class="b-icon">
					<img src="@/assets/images/google-icon.svg?url" />
				</span>
				Sign in with Google
			</n-button>
			<n-button strong secondary>
				<span class="b-icon" size="large">
					<img src="@/assets/images/facebook-icon.svg?url" />
				</span>
				Sign in with Facebook
			</n-button>
		</div>

		<div class="sign-text text-center">
			<div class="sign-text" v-if="typeRef === 'signin'">
				Don't you have an account?
				<n-button text @click="gotoSignUp()" type="primary" size="large">Sign up</n-button>
			</div>
			<div class="sign-text" v-if="typeRef === 'forgotpassword'">
				<n-button text @click="gotoSignIn()" type="primary" size="large">Back to Sign in</n-button>
			</div>
			<div class="sign-text" v-if="typeRef === 'signup'">
				Do you have an account?
				<n-button text @click="gotoSignIn()" type="primary" size="large">Sign in</n-button>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { useThemeStore } from "@/stores/theme"
import Logo from "@/app-layouts/common/Logo.vue"
import { NButton, NDivider } from "naive-ui"
import { computed, onBeforeMount, ref } from "vue"
import { useRouter } from "vue-router"
import AuSignIn from "@/au24/view/pages/login/AuthForm/AuSignIn.vue"
import AuForgotPassword from "@/au24/view/pages/login/AuthForm/AuForgotPassword.vue"
import AuSignUp from "@/au24/view/pages/login/AuthForm/AuSignUp.vue"

export type FormType = "signin" | "signup" | "forgotpassword"

const props = defineProps<{
	type?: FormType
	useOnlyRouter?: boolean
}>()

const typeRef = ref<FormType>("signin")

const router = useRouter()
const themeStore = useThemeStore()
const isDark = computed<boolean>(() => themeStore.isThemeDark)
const title = computed<string>(() =>
	typeRef.value === "signin" ? "Welcome Back" : typeRef.value === "signup" ? "Hello" : "Forgot Password"
)

function gotoSignIn() {
	if (!props.useOnlyRouter) {
		typeRef.value = "signin"
	}
	router.replace({ name: "Login" })
}

function gotoSignUp() {
	if (!props.useOnlyRouter) {
		typeRef.value = "signup"
	}
	router.replace({ name: "Register" })
}

function gotoForgotPassword() {
	if (!props.useOnlyRouter) {
		typeRef.value = "forgotpassword"
	}
	router.replace({ name: "ForgotPassword" })
}

onBeforeMount(() => {
	if (props.type) {
		typeRef.value = props.type
	}
})
</script>

<style lang="scss" scoped>
.form-wrap {
	width: 100%;
	min-width: 270px;
	max-width: 400px;

	.logo {
		:deep(img) {
			max-height: 37px;
		}
	}

	.title {
		font-size: 36px;
		font-family: var(--font-family-display);
		line-height: 1.2;
		font-weight: 700;
	}

	.text {
		font-size: 18px;
		line-height: 1.3;
		color: var(--fg-secondary-color);
	}

	.social-btns {
		.b-icon {
			margin-right: 16px;

			img {
				display: block;
				height: 20px;
			}
		}
	}
}

.form-fade-enter-active,
.form-fade-leave-active {
	transition:
		opacity 0.2s ease-in-out,
		transform 0.3s ease-in-out;
}

.form-fade-enter-from {
	opacity: 0;
	transform: translateX(10px);
}

.form-fade-leave-to {
	opacity: 0;
	transform: translateX(-10px);
}
</style>
