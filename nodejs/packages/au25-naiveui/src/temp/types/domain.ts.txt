
// 1) User-Related Types

// UserRole Enum
type UserRole = 'ADMIN' | 'AUCTIONEER' | 'TRADER' | 'EXTERNAL_OBSERVER' | 'USER';

// User Interface
interface User {
  id: string;
  name: string;
  email: string;
  userRole: UserRole;
  userGroups: UserGroup[];
  languagePreference?: string;
  timeZone?: string;
  notificationPreferences?: NotificationPreferences;
}

// UserGroup Interface
interface UserGroup {
  id: string;
  name: string;
  members: User[];
  permissions: Permission[];
}

// Permission Interface
interface Permission {
  id: string;
  name: string;
  description: string;
}

// NotificationPreferences Interface
interface NotificationPreferences {
  emailNotifications: boolean;
  smsNotifications: boolean;
  pushNotifications: boolean;
}

// UserSession Interface
interface UserSession {
  userId: string;
  userRole: UserRole;
  token: string;
  expiresAt: Date;
}

// 2) Host and System-Related Types

// Host Interface
interface Host {
    id: string;
    name: string;
    isMockMode: boolean;
    users: User[];
    auctions: Auction[];
  }
  
  // SystemSettings Interface
  interface SystemSettings {
    defaultLanguage: string;
    supportedLanguages: string[];
    defaultTimeZone: string;
    supportedTimeZones: string[];
    securityPolicies: SecurityPolicies;
  }
  
  // SecurityPolicies Interface
  interface SecurityPolicies {
    passwordPolicy: PasswordPolicy;
    sessionTimeout: number; // in minutes
    maxLoginAttempts: number;
  }
  
  // PasswordPolicy Interface
  interface PasswordPolicy {
    minLength: number;
    requireNumbers: boolean;
    requireSpecialCharacters: boolean;
    requireUppercase: boolean;
    requireLowercase: boolean;
  }
  
  // 3) Auction and Trading Types


  // AuctionStatus Enum
type AuctionStatus = 'CREATED' | 'ONGOING' | 'COMPLETED' | 'CANCELLED';

// Auction Interface
interface Auction {
  id: string;
  name: string;
  status: AuctionStatus;
  startingPrice: number;
  priceMovementLogic: PriceMovementLogic;
  participants: UserGroup[];
  rounds: AuctionRound[];
  awards: AuctionAward[];
}

// PriceMovementLogic Interface
interface PriceMovementLogic {
  increment: number;
  decrement: number;
  strategy: 'FIXED' | 'DYNAMIC';
}

// AuctionRound Interface
interface AuctionRound {
  roundNumber: number;
  startTime: Date;
  endTime: Date;
  orders: Order[];
}

// OrderType Enum
type OrderType = 'BUY' | 'SELL';

// Order Interface
interface Order {
  id: string;
  userId: string;
  auctionId: string;
  roundNumber: number;
  orderType: OrderType;
  quantity: number;
  price: number;
  timestamp: Date;
}

// AuctionAward Interface
interface AuctionAward {
  userId: string;
  quantity: number;
  price: number;
}

// FinancialLimits Interface
interface FinancialLimits {
  creditLimit: number;
  usedCredit: number;
  budgetLimit: number;
  usedBudget: number;
}

// 4) Notification and Messaging Types

// NotificationType Enum
type NotificationType = 'INFO' | 'WARNING' | 'ERROR' | 'SUCCESS';

// Notification Interface
interface Notification {
  id: string;
  message: string;
  type: NotificationType;
  timestamp: Date;
  isRead: boolean;
}

// Alert Interface (for System Monitoring)
interface Alert {
  id: string;
  message: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  timestamp: Date;
  acknowledged: boolean;
}

// 5)  System Monitoring and Reporting Types

// SystemMetric Interface
interface SystemMetric {
    metricName: string;
    value: number;
    timestamp: Date;
  }
  
  // AuditLog Interface
  interface AuditLog {
    id: string;
    userId: string;
    action: string;
    timestamp: Date;
    details: string;
  }
  
  // Report Interface
  interface Report {
    id: string;
    name: string;
    description: string;
    createdAt: Date;
    generatedBy: string;
  }
  
  // ReportFilters Interface
  interface ReportFilters {
    dateRange?: {
      startDate: Date;
      endDate: Date;
    };
    auctionId?: string;
    userId?: string;
    status?: AuctionStatus[];
  }
  
  // AnalyticsData Interface
  interface AnalyticsData {
    metrics: SystemMetric[];
    trends: any; // Replace 'any' with a more specific type if needed
  }

  
  // 6)  Miscellaneous Types

  // NotificationPreferences Interface (Already defined under User-Related Types)

// Language and Time Zone Types
type LanguageCode = 'en' | 'es' | 'fr' | 'de' | 'zh' | 'ja'; // Extend as needed

// SecurityPolicy Interface (Already defined under Host and System-Related Types)

// PriceMovementStrategy Enum (if needed separately)
type PriceMovementStrategy = 'FIXED' | 'DYNAMIC';

