export FRONTEND_PATH := 'apps/au24-frontend'
export ENGINE_PATH   := 'apps/engine'

build-all-and-run-dev:
    # probably better to do on CI in several steps because:
    # - can cache the npm and gradle deps separately
    # - currently we can build npm and quarkus with one image
    #   - but that need not always be the case
    cd ${FRONTEND_PATH} && just frontend-build-ci
    cd ${ENGINE_PATH} && just copy-frontend-dist-to-engine
    cd ${ENGINE_PATH} && just engine-build-ci
    cd ${ENGINE_PATH} && just dev

set dotenv-load

# NOTE: this might be working, it seems that it could be queued:
delete_gitlab_project_artifacts:
    # Run the Node.js script
    node scripts/delete-artifacts.js

find_lock_files:
    node scripts/lock-file-finder.js

delete_node_modules_and_lock_files:
    find . -name "node_modules" -type d -prune -exec rm -rf '{}' +
    find . -name "package-lock.json" -type f -delete
    find . -name "yarn.lock" -type f -delete
    find . -name "pnpm-lock.yaml" -type f -delete

# Generate initial changelog from origin/dev (fails if file exists)
generate-origin-dev-changelog:
    #!/usr/bin/env python3
    import os
    import sys
    from datetime import datetime

    changelog_path = "docs/CHANGELOG.origin.dev.md"
    if os.path.exists(changelog_path):
        print(f"Error: {changelog_path} already exists")
        sys.exit(1)

    with open(changelog_path, "w") as f:
        f.write("# Changelog (origin/dev)\n\n")
        f.write(f"Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

    os.system('''git log origin/dev -n 20 --reverse --pretty=format:"## %ai [origin/dev] [%h]%n%s%n%b%n" >> ''' + changelog_path)

# Update changelog with new commits since last entry
update-origin-dev-changelog:
    #!/usr/bin/env python3
    import os
    import sys
    import re
    from datetime import datetime

    changelog_path = "docs/CHANGELOG.origin.dev.md"
    if not os.path.exists(changelog_path):
        print(f"Error: {changelog_path} does not exist. Run generate-origin-dev-changelog first.")
        sys.exit(1)

    # Find last commit hash
    with open(changelog_path, "r") as f:
        content = f.read()
        matches = re.findall(r'\[([a-f0-9]+)\]', content)
        if not matches:
            print("Error: No commit hash found in changelog")
            sys.exit(1)
        last_hash = matches[-1]

    # Get new commits since last hash
    temp_file = "/tmp/new_commits.md"
    os.system(f'''git log {last_hash}..origin/dev --reverse --pretty=format:"## %ai [origin/dev] [%h]%n%s%n%b%n" > {temp_file}''')

    # Append new commits if any
    if os.path.getsize(temp_file) > 0:
        with open(changelog_path, "a") as f:
            with open(temp_file, "r") as new_commits:
                f.write(new_commits.read())

    os.remove(temp_file)

# turbo:
# - I'm not sure why these can't be run with pnpm
# - nor BTW does in run here! only works in the terminal!


#run-au21-backend-app-book:
#    #!/usr/bin/env zsh
#    source ~/.zshrc
#    kq # kill quarkus
#    kn # kill node
#    printf '\033]0;\007'  # Reset terminal title
#    (cd ./projects/kotlin/au21-engine && just dev) &
#    (cd ./projects/node/packages/au21-frontend && NODE_OPTIONS="--max-old-space-size=4096" just run-app) &
#    #(cd ./projects/node/packages/au21-frontend && NODE_OPTIONS="--max-old-space-size=4096" just run-book)

dev-au21-engine:
    (cd ./java/au21-engine && just dev)

dev-au21-frontend-app:
    (cd ./projects/node/packages/au21-frontend && NODE_OPTIONS="--max-old-space-size=4096" just run-app)

dev-au21-frontend-book:
    (cd ./projects/node/packages/au21-frontend && NODE_OPTIONS="--max-old-space-size=4096" just run-book)

# Misc

# Documentation Processing
next-doc:
    #!/usr/bin/env python3
    import subprocess
    import sys

    # Run the find-next-doc.py script and capture its output
    result = subprocess.run(['./scripts/find-next-doc.py'],
                          capture_output=True,
                          text=True)

    # If the script failed, print error and exit
    if result.returncode != 0:
        print(result.stderr, file=sys.stderr)
        sys.exit(result.returncode)

    # Print the file path
    print(result.stdout.strip())

# docs-all-desc:
#     cd docs && find . -type f -print0 | xargs -0 stat -f "%m %N" | sort -rn | cut -d' ' -f2-

# docs-all-asc:
#     cd docs && find . -type f -name "*" -exec stat -f "%m %N" {} \; | sort -n | cut -f2- -d" "

# docs-md-desc:
#     cd docs && find . -type f -name "*.md" -exec stat -f "%m %N" {} \; | sort -rn | cut -f2- -d" "

# docs-md-asc:
#     cd docs && find . -type f -name "*.md" -exec stat -f "%m %N" {} \; | sort -n | cut -f2- -d" "


# misc
obsidian:
    #!/usr/bin/env sh
    cd docs || exit 1
    if [ ! -d ".obsidian" ]; then
        echo "Error: .obsidian folder not found in docs directory"
        exit 1
    fi
    open -a Obsidian .

# Open all development URLs in Chrome
dev-urls:
    #!/usr/bin/env sh
    # Monitoring
    open -a "Google Chrome" http://localhost:16686  # Jaeger UI
    open -a "Google Chrome" http://localhost:5341   # Seq UI

    # Backend
    open -a "Google Chrome" http://localhost:4040/q/dev            # Quarkus Dev UI
    open -a "Google Chrome" http://localhost:4040/graphql          # GraphQL Endpoint
    open -a "Google Chrome" http://localhost:4040/q/graphql-ui     # GraphQL UI

    # AU21 Frontend
    open -a "Google Chrome" http://localhost:8080                  # App
    open -a "Google Chrome" http://localhost:8081                  # Book

    # AU25 React
    open -a "Google Chrome" http://localhost:3000                  # App
    open -a "Google Chrome" http://localhost:3001                  # Book
    open -a "Google Chrome" http://localhost:5001                  # React Cosmos

    # AU25 NaiveUI
    # open -a "Google Chrome" http://localhost:3000                  # App
    # open -a "Google Chrome" http://localhost:3001                  # Book


# Display service URLs
# dev-urls:
#     # Monitoring
#     echo "Monitoring URLs:"
#     echo "Jaeger UI: http://localhost:16686"
#     echo "Seq UI: http://localhost:5341"

#     # Backend
#     echo "\nBackend URLs:"
#     echo "Quarkus Dev UI: http://localhost:4040/q/dev"
#     echo "GraphQL Endpoint: http://localhost:4040/graphql"
#     echo "GraphQL UI: http://localhost:4040/q/graphql-ui"

#     # AU21 Frontend
#     echo "\nAU21 Frontend URLs:"
#     echo "App: http://localhost:8080"
#     echo "Book: http://localhost:8081"

#     # AU25 React
#     echo "\nAU25 React URLs:"
#     echo "App: http://localhost:3000"
#     echo "Book: http://localhost:3001"
#     echo "React Cosmos: http://localhost:5001"

#     # AU25 NaiveUI
#     #echo "\nAU25 NaiveUI URLs:"
#     #echo "App: http://localhost:3000"
#     #echo "Book: http://localhost:3001"

# Start full development environment using Zellij
dev-all:
    zellij --layout layouts/dev.kdl

# Start the service management dashboard
dev-dashboard:
    cd python && streamlit run src/au25_auction/service_dashboard.py