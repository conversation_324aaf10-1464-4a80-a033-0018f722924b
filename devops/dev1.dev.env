# OBJECTDB_ACTIVATION_CODE=A812-7Y7H-GR1Y-22AS-JTRC
# Mar 3, 2021:

# PROBOBLY WE NEED TO ELIMINATE THIS FILE, OR JUST LEAVE THE ACTIVATION CODE ?

# not sure this is being used:
OBJECTDB_ACTIVATION_CODE=A812-7Y7H-GR1Y-1JT0-91KK
#OBJECTDB_HOME=db

# Note that for jib this is changed from db/storage.db to app/db/storage.db
# note used?
# OBJECTDB_DATABASE=app/db/storage.odb
OBJECTDB_URL=au21-engine.odb
JAVA_OPTS=-agentpath:/deployments/yourkit/libyjpagent.so=port=10001,listen=all
# ??: Note this only works with IntelliJ plugin, for gradle, have to use local.properties?

